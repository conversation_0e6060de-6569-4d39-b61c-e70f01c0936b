FROM node:18-alpine


# Install dependencies for node-gyp and build tools
RUN apk add --no-cache python3 make g++ build-base && ln -sf python3 /usr/bin/python

ARG SHOPIFY_API_KEY
ENV SHOPIFY_API_KEY=1871c0a009ecd2a3d3002a067c9b5f63
ENV NEW_RELIC_NO_CONFIG_FILE=true
ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
ENV NEW_RELIC_LOG=stdout

EXPOSE 8081
WORKDIR /app
COPY web/ ./
RUN npm install
RUN npm run clean
RUN npm run build
CMD ["npm", "run", "worker"]
