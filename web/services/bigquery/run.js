import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "../logger.js";

const {setTimeout} = require("timers/promises");

const pendingTransferStates = ["TRANSFER_STATE_UNSPECIFIED", "PENDING", "RUNNING"];
const failureTransferStates = ["FAILED", "CANCELLED"];
const successTransferStates = ["SUCCEEDED"];

const DATDREW_EU_SYNC_RESOURCE = "projects/1009805801819/locations/us-central1/transferConfigs/66f5f4f2-0000-2d97-b4f9-ac3eb151dca4";

// Imports the Datatransfer library
const {DataTransferServiceClient} = require('@google-cloud/bigquery-data-transfer').v1;

// Instantiates a client
const datatransferClient = new DataTransferServiceClient();

const INITIAL_BACKOFF_MS = 30000;
const TRANSFER_MAX_RETRIES = 5;

async function waitForTransferCompletion(transferRunName, transferState) {
    let retryDelay = INITIAL_BACKOFF_MS;
    let retries = 0;
    
    while (pendingTransferStates.includes(transferState) && retries < TRANSFER_MAX_RETRIES) {
        logger.error(`Data Transfer[${transferRunName}] is still pending to complete after ${retries} checks`);
        await setTimeout(retryDelay);
        retries++;
        retryDelay *= 1.5;

        const [transferR] = await datatransferClient.getTransferRun({ name: transferRunName });
        transferState = transferR.state ?? "";
    }

    if (failureTransferStates.includes(transferState)) {
        logger.error(`Data Transfer[${transferRunName}] failed with state ${transferState}`);
    } else if (retries >= TRANSFER_MAX_RETRIES) {
        logger.error(`Data Transfer[${transferRunName}] is still pending to complete after ${retries} checks`);
    } else {
        logger.info(`Data Transfer[${transferRunName}] completed with state ${transferState}`);
    }

    return transferState;
}


async function callScheduleTransferRuns(parent, waitForCompletion = true) {

    const [runsRes] = await datatransferClient.startManualTransferRuns({
        requestedRunTime: { seconds: Math.floor(Date.now() / 1000) },
        parent: parent,
    });

    const transferRuns = runsRes.runs;
    if (transferRuns.length === 0) {
        logger.error(`Data Transfer[${parent}] failed to start.`);
        return "FAILED_TO_START";
    }

    if (!waitForCompletion) {
        return transferRuns[0]?.state;
    }

    const transferState = await waitForTransferCompletion(
        transferRuns[0]?.name,
        transferRuns[0]?.state
    );

    return transferState;
}

// Note - We use federated external queries in bq to sync to a us-central1 dataset ("datadrew")
// This dataset has a read-only replica in EU which we use for running dbt transformations in EU
async function runDatadrewEUSync({waitForCompletion} = {waitForCompletion: true}) {
    try {
        let resp = await callScheduleTransferRuns(DATDREW_EU_SYNC_RESOURCE, waitForCompletion);
        logger.info(`datadrewEUSync success - ${JSON.stringify(resp)}`);
        return true;
    } catch (e) {
        // Log a detailed error message and ensure stack trace is available for debugging
        logger.error(`datadrewEUSync: Error scheduling data transfer: ${e.message}`);
        return false;
    }
}


export default {
    runDatadrewEUSync,
    callScheduleTransferRuns
}