import logger from "../logger";
import axios from "axios";
import settings from "../settings/index.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import secretmanager from "../secretmanager/index.js";
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek)


const SLACK_CLIENT_ID = "4856082670358.7215182905585";
const INTEGRATION_TYPE_SLACK = "slack";

async function saveSlackAccessToken(data, shop_id) {
  if (!data) {
    logger.error("saveSlackAccessToken: Data not found", {shop_id});
    return false;
  }

  let incoming_webhook_payload;
  try {
    incoming_webhook_payload = JSON.stringify(data.incoming_webhook);
  } catch (err) {
    logger.error("saveSlackAccessToken: Invalid JSON", {message : err.message ?? "", incoming_webhook: data.incoming_webhook ?? "", shop_id});
    return false;
  }

  const metadata = JSON.stringify({
    authed_user_id: data.authed_user?.id,
    bot_user_id: data.bot_user_id,
    team_id: data.team?.id,
    team_name: data.team?.name,
    channel_name: data.incoming_webhook?.channel,
    incoming_webhook_url: data.incoming_webhook?.url,
    incoming_webhook_payload,
  });

  const optionalData = {
    scope: data?.scope,
    metadata: metadata,
  }

  try {
    const done = await secretmanager.saveCredentials(shop_id, INTEGRATION_TYPE_SLACK, data.access_token, optionalData);
    if (!done) {
      logger.error("saveSlackAccessToken: Error saving credentials", {shop_id});
      return false;
    }
    return true;
  } catch (err) {
    logger.error("saveSlackAccessToken: Something went wrong.", {shop_id, error: err.message ?? ""});
    return false;
  }
}

async function disconnectSlack(shop_id) {
  const data = await getSlackData(shop_id);
  if (!data || data.length === 0) {
    logger.error("disconnectSlack: No data found");
    return false;
  }
  const { token_id } = data;
  const partial_configuration = {
    active_flag: 0,
  };
  try {
    const done = await secretmanager.updatePartial(token_id, partial_configuration);
    if (!done) {
      logger.error("disconnectSlack: Error disconnecting slack");
      return false;
    }
    // Disable weekly report setting
    // Slack job will also get disabled
    return await settings.addOrUpdateSettings(shop_id, settings.SLACK_WEEKLY_REPORT, { active_flag: 0 });
  } catch (err) {
    logger.error("disconnectSlack: error ", {message : err.message ?? ""});
    return false;
  }
}

async function getSlackData(shop_id) {
  try {

    const slackData = await secretmanager.getCredentialsByShopId(shop_id, INTEGRATION_TYPE_SLACK);
    if (!slackData || Object.keys(slackData).length === 0) {
      return {};
    }

    let metadata;
    try {
      metadata = JSON.parse(slackData?.metadata ?? "{}");
    } catch (err) {
      logger.error("getSlackData: Invalid metadata JSON", {message: err.message ?? "", metadata: slackData?.metadata ?? ""});
      metadata = {};
    }

    const data = {
      ...slackData,
      ...metadata
    };

    delete data.metadata;
    return data ?? {};
  } catch (err) {
    logger.error("getSlackData: error", {message : err.message ?? ""});
    return {};
  }
}

async function generateAuthUrl(shop_id) {
  const redirect_uri = `${process.env.HOST}/api/slack/oauth/callback?sid=${shop_id}`;
  return `https://slack.com/oauth/v2/authorize?client_id=${SLACK_CLIENT_ID}&scope=incoming-webhook&redirect_uri=${redirect_uri}`;
}

function formatDifference(diff) {
  if (diff > 0) {
    return `(${diff}% :chart_with_upwards_trend:)`;
  } else if (diff < 0) {
    return `(${diff}% :chart_with_downwards_trend:)`;
  } else {
    return ``;
  }
}

const reportSections = [
  { name: "Sales",code: "total_price", emoji: "shopping_trolley", unicode: "1f6d2" },
  { name: "Facebook Ad Spend",code: "fb_spend", emoji: "money_with_wings", unicode: "1f4b8" },
  { name: "Orders",code: "order_count", emoji: "package", unicode: "1f4e6" },
  { name: "AOV",code: "aov", emoji: "moneybag", unicode: "1f4b0" },
  { name: "New Customers",code: "new_cust_count", emoji: "bust_in_silhouette", unicode: "1f464" },
  { name: "Returning Customers",code: "returning_cust_count", emoji: "repeat", unicode: "1f501" },
];

function getBlocks(reportData) {
  const blocks = [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "Your Weekly performance report",
        "emoji": true
      }
    },
    {
      "type": "rich_text",
      "elements": [
        {
          "type": "rich_text_section",
          "elements": [
            {
              "type": "text",
              "text": `for ${reportData.name ?? ""}`,
              "style": {
                "italic": true
              }
            }
          ]
        }
      ]
    },
    {
      "type": "rich_text",
      "elements": [
        {
          "type": "rich_text_section",
          "elements": [
            {
              "type": "text",
              "text": `${reportData.week_start} - ${reportData.week_end}`,
              "style": {
                "bold": true
              }
            }
          ]
        }
      ]
    },
    {
      "type": "rich_text",
      "elements": [
        {
          "type": "rich_text_section",
          "elements": [
            {
              "type": "text",
              "text": `compared to ${reportData.prev_week_start} - ${reportData.prev_week_end}`,
              "style": {
                "italic": true
              }
            }
          ]
        }
      ]
    },
    {
      "type": "divider"
    }
  ];

  reportSections.forEach((section) => {
    blocks.push(
      {
        "type": "section",
        "fields": [
          {
            "type": "plain_text",
            "text": `${section.name} :${section.emoji}:`,
            "emoji": true
          },
          {
            "type": "plain_text",
            "text": `${reportData[section.code + "_formatted"]} ${formatDifference(reportData[section.code + "_diff"])}`,
            "emoji": true
          }
        ]
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "\n"
        }
      },
      {
        "type": "divider"
      },
    );
  });

  blocks.push({
    "type": "actions",
    "elements": [
      {
        "type": "button",
        "text": {
          "type": "plain_text",
          "text": "📊 View Your Dashboard",
          "emoji": true
        },
        "url": "https://app.datadrew.io"
      }
    ]
  });

  return blocks;
}

async function sendSlackMessage(incoming_webhook_url, messagePayload) {

  if (!incoming_webhook_url) {
    logger.error("sendSlackMessage: Incoming webhook url not found");
    return { error: "Incoming webhook url not found" };
  }

  if (!messagePayload) {
    logger.error("sendSlackMessage: Message payload not found");
    return { error: "Message payload not found" };
  }

  try {
    const response = await axios.post(incoming_webhook_url, messagePayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status !== 200) {
      logger.error(`sendSlackMessage: Failed to send message to Slack: ${response.statusText}`);
      return { error: `Failed to send message to Slack: ${response.statusText}` };
    }

    return {success: 'Message sent to Slack successfully'};
  } catch (error) {
    logger.error('sendSlackMessage: Error sending message to Slack:', {error : error.message ?? ""});
    return { error: `Error sending message to Slack : ${error.message ?? ""}` };
  }
}

async function sendWeeklyReport(shop_id, content) {
  try {
    let slackData = await getSlackData(shop_id)
    if (!slackData || !slackData.incoming_webhook_url || !slackData.active_flag) {
        logger.error("sendWeeklyReport: Slack not connected", {shop_id})
        return {error: "Slack not connected"};
    }

    let shopSettings = await settings.getShopSettings(shop_id, settings.SLACK_WEEKLY_REPORT)
    let slackSettings = shopSettings[settings.SLACK_WEEKLY_REPORT] ?? {}

    if (!slackSettings || !slackSettings.active_flag) {
        logger.error("sendWeeklyReport: Slack weekly report not enabled", {shop_id})
        return {error: "Slack weekly report not enabled"};
    }

    if (!content) {
        logger.error("sendWeeklyReport: Content not found", {shop_id})
        return {error: "Content not found"};
    }

    const messagePayload = {
        text: 'Your Weekly Performance Report',
        blocks: getBlocks(content)
    }

    const response = await sendSlackMessage(
        slackData.incoming_webhook_url,
        messagePayload
    );

    return response;
  } catch (err) {
    logger.error("sendWeeklyReportJob: Error", err);
    return {error: err.message ?? ""};
  }
}

export {
  SLACK_CLIENT_ID,
  saveSlackAccessToken,
  generateAuthUrl,
  getSlackData,
  getBlocks,
  sendSlackMessage,
  disconnectSlack,
  sendWeeklyReport
}
