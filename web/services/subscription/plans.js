import dotenv from 'dotenv';
import { BillingInterval, LATEST_API_VERSION } from "@shopify/shopify-api";
import { features as f, limits as l } from "./features.js";
import { getActiveSubscription } from '../subscription.js'
import shop from '../shop.js';
import logger from '../logger.js';
import unleash from "../unleash.js";
import dayjs from "dayjs";

// Helper function to get all normalized features
const getAllNormalizedFeatures = () => {
    const normalizedFeatures = new Set();
    
    // Recursively extract all feature keys
    const extractFeatures = (obj) => {
        for (const key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                extractFeatures(obj[key]);
            } else if (typeof obj[key] === 'boolean') {
                normalizedFeatures.add(key);
            }
        }
    };
    
    extractFeatures(f);
    return Array.from(normalizedFeatures);
};

dotenv.config();
// Old pricing model
export const FreemiumPlan = "Freemium";
export const PremiumPlanMonthly = "Premium Plan Monthly";
export const PremiumPlanAnnual = "Premium Plan Annual";
// New pricing model
export const FreemiumGrowthPlan = "Freemium Basic";
export const GrowthPlanMonthly = "Growth Plan Monthly";
export const GrowthPlanAnnual = "Growth Plan Annual";
export const GrowthPlusMonthly = "Growth Plus Monthly";
export const GrowthPlusAnnual = "Growth Plus Plan Annual";

export const shopifyBillingConfig = {
    // Old pricing model
    [PremiumPlanMonthly]: {
        amount: 39.0,
        currencyCode: "USD",
        interval: BillingInterval.Every30Days,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_monthly',
        trialDays : 7
    },
    [PremiumPlanAnnual]: {
        amount: 348.0,
        currencyCode: "USD",
        interval: BillingInterval.Annual,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_annual',
        trialDays : 7
    },

    // New pricing model
    [GrowthPlanMonthly]: {
        amount: 39.0,
        currencyCode: "USD",
        interval: BillingInterval.Every30Days,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_monthly',
        trialDays : 7
    },
    [GrowthPlanAnnual]: {
        amount: 390.0,
        currencyCode: "USD",
        interval: BillingInterval.Annual,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_annual',
        trialDays : 7
    },
    [GrowthPlusMonthly]: {
        amount: 99.0,
        currencyCode: "USD",
        interval: BillingInterval.Every30Days,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_monthly',
        trialDays : 7
    },
    [GrowthPlusAnnual]: {
        amount: 990.0,
        currencyCode: "USD",
        interval: BillingInterval.Annual,
        test : process.env.NODE_ENV !== 'production',
        planType : 'fixed_annual',
        trialDays : 7
    }
}


// The transactions with Shopify will always be marked as test transactions, unless NODE_ENV is production.
// See the ensureBilling helper to learn more about billing in this template.
const planList = {
    [FreemiumPlan] : {
        amount: 0,
        currencyCode: "USD",
        interval: BillingInterval.Every30Days, // dummy
        test : process.env.NODE_ENV !== 'production',//dummy
        planType : 'free',
        trialDays : 0,
        planName : FreemiumPlan,
        features : [
            "ltv_cohorts",
            "cohort_filters",
            "repurchase_rate",
            "basic_customer_segments_wo_export",
            "all_integrations",
            "scheduled_reports",
            "connect_multiple_accounts"
        ],
        usage_limits : [
            "data_time_limit_3_months",
            "shop_limit_unlimited",
        ],
        render : {
            prev_features: [
                "pricing.includes"
            ],
            limited_features : [
                "pricing.limited-data-3m",
            ],
            features : [
                "p-ltv-cohorts",
                "p-product-repurchase",
                "pricing.new-vs-returning",
                "pricing.integration-summary",
                "pricing.automated-reports",
                "p-cust-seg",
                "p-onboarding"
            ],
            display_price: 0,
            display_name : "Basic"
        }
    },
    [PremiumPlanMonthly]: {
        ...shopifyBillingConfig[PremiumPlanMonthly],
        planName: PremiumPlanMonthly,
        features : ["all_features"],
        usage_limits : ["all_unlimited"],
        render : {
            prev_features: [
                "pricing.basic-plus"
            ],
            features : [
                "pricing.data-unlimited",
                "p-data-export-unlimited",
                "p-breakdown-cohorts",
                "p-basket-analysis",
                "p-rfm",
                "p-benchmarks",
                "p-multi-store",
                "p-support"
            ],
            display_price: 39,
            interval_type: "month",
            display_name : "Premium",
            isRecommended: true
        }
    },
    [PremiumPlanAnnual]: {
        ...shopifyBillingConfig[PremiumPlanAnnual],
        planName: PremiumPlanAnnual,
        features : ["all_features"],
        usage_limits : ["all_unlimited"],
        render : {
            prev_features: [
                "pricing.basic-plus"
            ],
            features : [
                "pricing.data-unlimited",
                "p-data-export-unlimited",
                "p-breakdown-cohorts",
                "p-basket-analysis",
                "p-rfm",
                "p-benchmarks",
                "p-multi-store",
                "p-support"
            ],
            display_name: "Premium",
            display_price : 348,
            interval_type: "year",
            isRecommended: true
        }
    },
    [FreemiumGrowthPlan] : {
        amount: 0,
        currencyCode: "USD",
        interval: BillingInterval.Every30Days,
        test : process.env.NODE_ENV !== 'production',
        planType : 'free',
        trialDays : 0,
        planName: FreemiumGrowthPlan,
        features : [
            "ltv_cohorts",
            "basic_customer_segments_wo_export",
            "all_integrations",
            "scheduled_reports"
        ],
        usage_limits : [
            "data_time_limit_3_months",
            "shop_limit_1",
        ],
        render : {
            prev_features: [
                "pricing.includes"
            ],
            limited_features : [
                "pricing.limited-data-3m",
            ],
            features : [
                "p-ltv-cohorts",
                "pricing.new-vs-returning",
                "pricing.integration-summary",
                "pricing.automated-reports",
                "p-cust-seg",
            ],
            display_name : "Basic",
            display_price: 0
        }
    },
    [GrowthPlanMonthly]: {
        ...shopifyBillingConfig[GrowthPlanMonthly],
        planName: GrowthPlanMonthly,
        features : [
            "ltv_cohorts",
            "ltv_cohorts_export",
            "cohort_filters",
            "rfm_segments",
            "rfm_segments_export",
            "new_vs_returning",
            "new_vs_returning_export",
            "basic_segments",
            "basic_segments_export",
            "transaction_frequency",
            "transaction_frequency_export",
            "all_product_analysis",
            "industry_benchmarks",
            "all_integrations",
            "scheduled_reports"
        ],
        usage_limits : [
            "shop_limit_1",
            "order_limit_2000"
        ],
        render : {
            limited_features : [
                "pricing.limited-orders-2000"
            ],
            prev_features: [
                "pricing.basic-plus"
            ],
            features : [
                "pricing.data-unlimited",
                "p-ltv-cohort-filters",
                "p-rfm",
                "p-product-repurchase",
                "p-basket-analysis",
                "p-data-export-unlimited",
                "p-benchmarks",
                "p-onboarding"
            ],
            display_name : "Growth",
            display_price: 39,
            interval_type: "month",
            isRecommended: true
        }
    },
    [GrowthPlanAnnual]: {
        ...shopifyBillingConfig[GrowthPlanAnnual],
        planName: GrowthPlanAnnual,
        features : [
            "ltv_cohorts",
            "ltv_cohorts_export",
            "cohort_filters",
            "rfm_segments",
            "rfm_segments_export",
            "new_vs_returning",
            "new_vs_returning_export",
            "basic_segments",
            "basic_segments_export",
            "transaction_frequency",
            "transaction_frequency_export",
            "all_product_analysis",
            "industry_benchmarks",
            "all_integrations",
            "scheduled_reports"
        ],
        usage_limits : [
            "shop_limit_1",
            "order_limit_2000"
        ],
        render : {
            limited_features : [
                "pricing.limited-orders-2000"
            ],
            prev_features: [
                "pricing.basic-plus"
            ],
            features : [
                "pricing.data-unlimited",
                "p-ltv-cohort-filters",
                "p-rfm",
                "p-product-repurchase",
                "p-basket-analysis",
                "p-data-export-unlimited",
                "p-benchmarks",
                "p-onboarding"
            ],
            display_name : "Growth",
            display_price: 390,
            // strike_price: 468,
            interval_type: "year",
            isRecommended: true
        }
    },
    [GrowthPlusMonthly]: {
        ...shopifyBillingConfig[GrowthPlusMonthly],
        planName: GrowthPlusMonthly,
        features : ["all_features"],
        usage_limits : ["all_unlimited"],
        render : {
            prev_features: [
                "pricing.growth-plus"
            ],
            features : [
                "pricing.unlimited-orders",
                "pricing.data-unlimited",
                "p-breakdown-cohorts",
                "p-rfm-tagging",
                "p-multi-store",
                "pricing.multiple-ad-accounts",
                "p-support"
            ],
            display_name : "Growth Plus",
            interval_type: "month",
            display_price: 99,
            isRecommended: true
        }
    },
    [GrowthPlusAnnual]: {
        ...shopifyBillingConfig[GrowthPlusAnnual],
        planName: GrowthPlusAnnual,
        features : ["all_features"],
        usage_limits : ["all_unlimited"],
        render : {
            prev_features: [
                "pricing.growth-plus"
            ],
            features : [
                "pricing.unlimited-orders",
                "pricing.data-unlimited",
                "p-breakdown-cohorts",
                "p-rfm-tagging",
                "p-multi-store",
                "pricing.multiple-ad-accounts",
                "p-support"
            ],
            display_name : "Growth Plus",
            display_price: 990,
            // strike_price: 1188,
            interval_type: "year",
            isRecommended: true
        }
    },
};

export const VALID_COUPONS = {
    "BFCM50" : {
        displayNameKey : "price-50-off-1m",
        plans : [], // deprecated
        discount : {
            "value": {
                "percentage": 0.5
            },
            "durationLimitInIntervals": 1
        }
    },
    "KICKSTART20" : {
        displayNameKey : "price-20-off-3m",
        plans : [PremiumPlanMonthly, GrowthPlanMonthly],
        discount : {
            "value": {
                "percentage": 0.2
            },
            "durationLimitInIntervals": 3
        }
    },
    "DATADREWBEYOND" : {
        displayNameKey : "datadrew-beyond",
        plans : [PremiumPlanAnnual, GrowthPlanAnnual, GrowthPlusAnnual],
        discount : {
            "value": {
                "percentage": 0.45
            },
            "durationLimitInIntervals": 1
        }
    }
};

// currently we have the ordering according to this array.
export const OLD_PRICING_PLANS = [
  FreemiumPlan,
  PremiumPlanMonthly,
  PremiumPlanAnnual,
];

export const NEW_PRICING_PLANS = [
  FreemiumGrowthPlan,
  GrowthPlanMonthly,
  GrowthPlanAnnual,
  GrowthPlusMonthly,
  GrowthPlusAnnual,
];

export const evaluateCoupon = (coupon, plan) => {
    if (!coupon || !VALID_COUPONS[coupon]) {
        return null;
    }
    const validCoupon = VALID_COUPONS[coupon];
    if (validCoupon.plans.includes(plan)) {
        return validCoupon;
    }
    return null;
}

export const getRecommendedPlanDetails = async (shop_id, include_feature = "") => {

    let activePlan = await getActivePlanDetails(shop_id);
    let currentPlanName = activePlan.planName ?? "";

    let recommendations = {
        [FreemiumPlan]: [PremiumPlanMonthly],
        // if a feature is not in Growth Plan, Growth Plus Plan will be recommended
        [FreemiumGrowthPlan]: [GrowthPlanMonthly, GrowthPlusMonthly],
        [GrowthPlanMonthly]: [GrowthPlusMonthly],
        [GrowthPlanAnnual]: [GrowthPlusAnnual],
    }

    if (!(currentPlanName in recommendations)) {
        // No recommendation means all features are enabled
        return {};
    }

    // Get the recommended plan
    for (let i = 0; i < recommendations[currentPlanName].length; i++) {
        let recommendedPlanName = recommendations[currentPlanName][i];
        let planDetails = await getPlanDetails(recommendedPlanName);

        // no feature or data_time_limit is requested
        if (!include_feature || include_feature == "data_time_limit") {
            return planDetails;
        }

        // if last plan, return it
        if (i === recommendations[currentPlanName].length - 1) {
            return planDetails;
        }

        // if all include_features are present in the plan, return it
        if (!!include_feature && planDetails.features?.[include_feature]) {
            return planDetails;
        }
    }

    return {};
}

export const isFeatureEnabled = async (shop_id, featureKey) => {

    let shopObj = await shop.getShopById(shop_id);
    if (!shopObj || !shopObj.shop_id) {
        return false;
    }

    let featureContext = {
        myshopify_domain: shopObj.myshopify_domain,
        installed_at: dayjs(shopObj.row_created_at).format("YYYYMMDD"),
        country_name: shopObj.country_name,
        plan_display_name: shopObj.plan_display_name
    };

    let isSubscriptionEnabled = unleash.isEnabled('subscription_enabled', featureContext, function() {
        return true; // default to true if featureKey doesn't exist
    });

    let planDetails = await getActivePlanDetails(shop_id);
    return !isSubscriptionEnabled || (!!planDetails.features?.[featureKey]);
}


// Get the details of the active plan
// It will always return the details of current plan which can be a free plan as well
export const getActivePlanDetails = async (shop_id, is_admin = false) => {
    let subscription = await getActiveSubscription(shop_id)
    let currentPlanName = subscription?.name ?? "";
    // currentPlanName = FreemiumGrowthPlan;

    if (!currentPlanName) {
        // if no active subscription, then this shop is placed in new or old pricing depending on the current logic on unleash
        let isNewPricingEnabled = await shop.isNewPricingEnabled(shop_id);
        currentPlanName = isNewPricingEnabled ? FreemiumGrowthPlan : FreemiumPlan;
    }

    let planDetails = await getPlanDetails(currentPlanName);

    // check in unleash for every feature
    let shopObj = await shop.getShopById(shop_id);
    if (!shopObj || !shopObj.shop_id) {
        return planDetails;
    }

    let featureContext = {
        datadrew_plan : currentPlanName,
        myshopify_domain: shopObj.myshopify_domain,
        installed_at: dayjs(shopObj.row_created_at).format("YYYYMMDD"),
        country_name: shopObj.country_name,
        plan_display_name: shopObj.plan_display_name,
        is_admin: is_admin
    };

    let enabledFeatures = { ...planDetails.features };
    // Add additional features dynamically ex. for beta access or to enable a feature globally
    const allNormalizedFeatures = getAllNormalizedFeatures();
    for (let featureKey of allNormalizedFeatures) {
        // Gradual rollout or blocking: Use original featureKey
        let addFeatureKey = `add_${featureKey}`;
        // Check if Unleash wants to dynamically add this feature
        let isAdditionalFeatureEnabled = unleash.isEnabled(addFeatureKey, featureContext, function() {
            return false; // default to false if the add-feature flag is not defined in Unleash
        });

        if (isAdditionalFeatureEnabled) {
            enabledFeatures[featureKey] = true;
        }
    }

    // For features already in the subscription plan or added features
    for (let featureKey in enabledFeatures) {
        // Gradual rollout or blocking: Use original featureKey
        let isFeatureEnabled = unleash.isEnabled(featureKey, featureContext, function() {
            return true; // default to true if feature is not defined in Unleash
        });

        if (!isFeatureEnabled) {
            // If the feature is disabled by Unleash, remove it from the enabledFeatures
            delete enabledFeatures[featureKey];
        }
    }

    planDetails.features = enabledFeatures;
    return planDetails;
}

export const getPlanDetails = async (planName) => {
    const plan = planList[planName] ?? {};
    if (!plan.features || !plan.usage_limits) {
        logger.warn(`Plan ${planName} is missing features or usage limits`);
        return plan;
    }
    
    // Resolve feature references
    const resolvedFeatures = plan.features.reduce((acc, featureKey) => {
        // either featureKey is a key in the features object or it is a direct feature from all_features
        if (featureKey in f) {
            return { ...acc, ...f[featureKey] };
        } else if (featureKey in f.all_features) {
            return { ...acc, ...{ [featureKey]: f.all_features[featureKey] } };
        } else {
            logger.warn(`Feature ${featureKey} not found in features or all_features`);
            return acc;
        }
    }, {});
  
    // Resolve usage limit references
    const resolvedLimits = plan.usage_limits.reduce((acc, limitKey) => {
        const limit = l[limitKey] ?? {};
        return { ...acc, ...limit };
    }, {});

    return { 
        ...plan,
        features: resolvedFeatures,
        usage_limits: resolvedLimits,
    };
};

export const getAvailablePlans = async (shop_id, coupon = "") => {
    
    let activePlan = await getActivePlanDetails(shop_id);
    const activePlanName = activePlan.planName ?? "";
    let plans = OLD_PRICING_PLANS;
    if (NEW_PRICING_PLANS.includes(activePlanName)) {
        plans = NEW_PRICING_PLANS;
    }

    // check if coupon is valid for at least one of the plans
    let isCouponValid = false;
    if (!!coupon) {
        for (let planName of plans) {
            if (evaluateCoupon(coupon, planName)) {
                isCouponValid = true;
                break;
            }
        }

        if (!isCouponValid) {
            return {isCouponValid: false};
        }
    }

    const activePlanIndex = plans.indexOf(activePlanName);
    if (activePlanIndex === -1) {
        return {error: "Active plan not found. Please contact support."};
    }

    const monthlyPlans = {};
    const yearlyPlans = {};
    for (let index = 0; index < plans.length; index++) {
        let planName = plans[index];
        const plan = planList[planName];
        let updatedPlan = { ...plan.render };

        const validCoupon = evaluateCoupon(coupon, planName);
        if (validCoupon) {
            // apply the coupon
            const discountValue = validCoupon.discount.value.percentage;
            const originalPrice = updatedPlan.display_price;

            updatedPlan.display_price = (originalPrice - (originalPrice * discountValue)).toFixed(2);
            if (!updatedPlan.strike_price) {
                updatedPlan.strike_price = originalPrice;
            }
        }

        let planAction = null;
        if (planName === activePlanName) {
            planAction = "active";
        } else if (activePlan.planType === 'free') {
            planAction = "startTrial7d";
        } else if (activePlanIndex < index) {
            planAction = "upgrade";
        } else {
            planAction = "downgrade";
        }

        if (planAction) {
            updatedPlan.action = planAction;
        }

        switch (plan.planType) {
            case 'free':
                monthlyPlans[planName] = updatedPlan;
                yearlyPlans[planName] = updatedPlan;
                break;
            case 'fixed_monthly':
                monthlyPlans[planName] = updatedPlan;
                break;
            case 'fixed_annual':
                yearlyPlans[planName] = updatedPlan;
                break;
        }
    }


    // GrowthPlanMonthly
    // GrowthPlanAnnual

    // GrowthPlusMonthly
    // GrowthPlusAnnual

    // PremiumPlanMonthly
    // PremiumPlanAnnual

    let activeTab = 0;
    if (activePlanName in yearlyPlans && activePlan.planType !== 'free') {
        activeTab = 1;
    }

    return {plans : [monthlyPlans, yearlyPlans], activeTab, isCouponValid};
};
