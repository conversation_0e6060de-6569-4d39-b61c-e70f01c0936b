import logger from '../logger.js';
import { SESv2Client, SendEmailCommand } from "@aws-sdk/client-sesv2";
import {weeklyReportRender} from '../../dist/index.js';
import settings from "../settings/index.js";


const sesClient = new SESv2Client({
    region: process.env.AWS_SES_REGION,
    credentials: {
        accessKeyId: process.env.AWS_SES_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SES_SECRET,
    },
});

async function sendWeeklyReport(shop_id, content) {
    let shopSettings = await settings.getShopSettings(
        shop_id,
        settings.EMAIL_WEEKLY_REPORT
    );

    let emailSettings = shopSettings[settings.EMAIL_WEEKLY_REPORT] ?? {};
    if (!emailSettings || !emailSettings.active_flag) {
        logger.error("No email settings found for shop", {shop_id});
        return {error: "No email settings found"};
    }

    if (!emailSettings.setting_value || !emailSettings.setting_value.emails) {
        logger.error("No email found for shop", {shop_id});
        return {error: "No email recipients found"};
    }

    let emailList = emailSettings.setting_value.emails.split(",").map(e => e.trim()).filter(e => e.length > 0);
    if (emailList.length == 0) {
        logger.error("No email found for shop", {shop_id});
        return {error: "No email recipients found"};
    }

    let htmlContent = "";
    try {
        htmlContent = await weeklyReportRender(content);
    } catch (error) {
        logger.error(`Failed to render email: ${error.message ?? ""}`, {error : error.message ?? ""});
        return {error: "Failed to render email"};
    }

    for (let email of emailList) {
        const params = {
            FromEmailAddress: 'Datadrew Reports <<EMAIL>>',
            Destination: {
                ToAddresses: [email],
            },
            ReplyToAddresses: ['<EMAIL>'],
            Content: {
                Simple: {
                    Subject: {
                        Data: `Your weekly report ${content.name ? `for ${content.name} ` : ""} - ${content.week_str_short}`,
                        Charset: 'UTF-8'
                    },
                    Body: {
                        Html: {
                            Data: htmlContent,
                            Charset: 'UTF-8'
                        }
                    }
                }
            },
            ConfigurationSetName: 'dashr-app',
            ListManagementOptions: {
                ContactListName: 'MyContactList',
                TopicName: 'Unsubscribe',
            }
        };

        try {
            const command = new SendEmailCommand(params);
            const sendResp = await sesClient.send(command);
            logger.info('Email sent successfully:', sendResp);
        } catch (error) {
            logger.error(`Failed to send email: ${error.message ?? ""}`, {error : error.message ?? ""});
            return {error: "Failed to send email"};
        }
    }

    return {success: true};
}

export { sendWeeklyReport }
