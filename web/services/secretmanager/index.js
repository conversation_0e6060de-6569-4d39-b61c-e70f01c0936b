import integrations from "../airbyte/integrations.js";
import mysql from "../database.js";
import logger from '../logger.js';
import { cache } from '../redis.js';
import crypto from "crypto";

async function saveCredentials(shop_id, integration_type, access_token, optional_fields = {}) {

    const token_id = crypto.randomUUID();
    let saveData = {
        token_id,
        shop_id,
        integration_type,
        access_token,
        active_flag: 1
    };

    const optionalKeys = ['refresh_token', 'expiry_date', 'scope', 'token_type', 'metadata'];
    for (const key of optionalKeys) {
        if (optional_fields[key] !== undefined) {
            if (key === 'metadata' && typeof optional_fields[key] === 'object') {
                saveData[key] = JSON.stringify(optional_fields[key]);
            } else {
                saveData[key] = optional_fields[key] ?? "";
            }
        }
    }

    try {
        await mysql.query('INSERT INTO integration_tokens SET ?', saveData);
        return { success: true, token_id: token_id};
    } catch (err) {
        logger.error('secretmanager.saveCredentials : mysql query failure', {message: err.message ?? "", shop_id, integration_type});
        return { success: false };
    }
}

async function getCredentialsByShopId(shop_id, integration_type) {

    const key = `tokens_shop_id_${shop_id}_${integration_type}`;
    const creds = await cache.get(key);
    if (!!creds) {
        return creds;
    }

    try {
        const query = 'SELECT * FROM integration_tokens WHERE shop_id = ? AND integration_type = ? AND active_flag = 1 ORDER BY row_created_at DESC LIMIT 1';
        const data = await mysql.query(query, [shop_id, integration_type]);
        if (data.length === 0) {
            return {};
        }

        await cache.set(key, data[0]);
        return data[0];
    } catch (err) {
        logger.error('secretmanager.getCredentials : mysql query failure', {message: err.message ?? "", shop_id, integration_type});
        return {};
    }

}

async function getCredentials(token_id) {

    const key = `tokens_${token_id}`;
    const creds = await cache.get(key);
    if (!!creds) {
        return creds;
    }

    try {
        const query = 'SELECT * FROM integration_tokens WHERE token_id = ?';
        const data = await mysql.query(query, token_id);
        if (data.length === 0) {
            logger.error("secretmanager.getCredentials : No data found for token_id", {token_id});
            return {error: 'No data found'};
        }
        await cache.set(key, data[0]);
        return data[0];
    } catch (err) {
        logger.error('secretmanager.getCredentials : mysql query failure', {message: err.message ?? "", token_id});
        return {error: 'Something went wrong. Please try again'};
    }
}

async function updatePartial(token_id, partial_configuration) {

    const cred = await getCredentials(token_id);
    if (!cred || cred.error) {
        logger.error('secretmanager.update : failed to get credentials', {token_id});
        return false;
    }

    let newCred = Object.assign(cred, partial_configuration)
    delete newCred.row_created_at;
    delete newCred.row_updated_at;
    delete newCred.token_id;

    try {
        const query = 'UPDATE integration_tokens SET ? WHERE token_id = ?';
        await mysql.query(query, [newCred, token_id]);
        await cache.del(`tokens_${token_id}`);
        await cache.del(`tokens_shop_id_${newCred.shop_id}_${newCred.integration_type}`);
        return true;
    } catch (err) {
        logger.error('secretmanager.update : mysql query failure', {message: err.message ?? "", token_id, partial_configuration});
        return false;
    }
}

async function saveTokenMetadata(payload) {
    const { request_id, token_id } = payload;
    const accountInfo = await integrations.getConfigureOptions(request_id);

    if (!accountInfo || accountInfo.error) {
        logger.error('secretmanager.saveTokenMetadata : failed to get account info', {payload, error: accountInfo.error ?? ""});
        return false;
    }

    const creds = await getCredentials(token_id);
    if (!creds || creds.error) {
        logger.error('secretmanager.saveTokenMetadata : failed to get credentials', {payload, error: creds.error ?? ""});
        return false;
    }

    let existingMetadata = {};

    try {
        existingMetadata = creds.metadata ? JSON.parse(creds.metadata) : {};
    } catch (error) {
        logger.error('secretmanager.saveTokenMetadata : failed to parse existing metadata', {message: error.message ?? "", metadata: creds.metadata});
        return false;
    }

    try {
        let newMetaData = {...existingMetadata, ...accountInfo};
        const partial_configuration = {
            metadata: JSON.stringify(newMetaData)
        }
        const done = await updatePartial(token_id, partial_configuration);
        if (!done) {
            logger.error('secretmanager.saveTokenMetadata : failed to save metadata', payload);
            return false;
        }
        return true;
    } catch(err) {
        logger.error('secretmanager.saveTokenMetadata', {message: err.message ?? "", payload});
        return false;
    }
}

export default {
    saveCredentials,
    getCredentials,
    updatePartial,
    saveTokenMetadata,
    getCredentialsByShopId
}