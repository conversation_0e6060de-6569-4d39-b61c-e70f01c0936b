import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "../logger.js";
import { cache } from "../redis.js";
var axios = require('axios');

import mysql from "../database.js";

function extractObj(shop, authResult) {
    var tokenObj = {
        shop_id: shop.shop_id,
        domain: shop.myshopify_domain,
        login_access_token: authResult.accessToken ?? '',
        access_token : authResult.LongLivedAccessToken ?? '',
        fb_id: authResult.id ?? '',
        fb_name: authResult.name ?? '',
        fb_email : authResult.email ?? ''
    }

    if ('grantedScopes' in authResult && authResult.grantedScopes != '') {
        tokenObj.login_scope = authResult.grantedScopes
    }

    if ('data_access_expiration_time' in authResult) {
        var d = new Date(0); // The 0 there is the key, which sets the date to the epoch
        d.setUTCSeconds(authResult.data_access_expiration_time)
        tokenObj.data_access_expiry_at = d.toISOString()
    }

    return tokenObj;
}

async function generateToken(authResult) {
    const axiosInstance = axios.create();
    var {data} = await axiosInstance.get(
        'oauth/access_token',
        {
            baseURL : 'https://graph.facebook.com/v14.0/',
            params : {
                'grant_type' : 'fb_exchange_token',
                'client_id' : '3144526972437033',
                'client_secret' : process.env.FB_APP_SECRET ?? "",
                'fb_exchange_token' : authResult.accessToken ?? ''
            },
        }
    )

    logger.info('generateToken - ', data);

    return data.access_token ?? '';
}

async function saveToken(shop, authResult) {
    try {
        var connection = await mysql.connection();
    } catch (err) {
        logger.error('fbtoken.save : mysql connection failure', err);
        return {}
    }

    var tokenObj = extractObj(shop, authResult)
    try {
        let tokenData = await connection.query('SELECT * FROM fb_access_tokens WHERE shop_id = ?', [tokenObj.shop_id + '']);
        let existingAccount = tokenData && tokenData.length>0;
        if (existingAccount) {
            await connection.query("UPDATE fb_access_tokens SET ? WHERE shop_id = ?", [tokenObj, tokenObj.shop_id + '']);
        } else {
            await connection.query("INSERT INTO fb_access_tokens SET ?", tokenObj);
        }
        return {
            data : tokenObj,
            existingAccount: existingAccount
        };
    } catch (err) {
        logger.error('fbtoken.save: ', err);
        return {}
    } finally {
        await cache.del('facebook_config_' + tokenObj.shop_id)
        await connection.release();
    }
}

async function getToken(shop_id) {
    try {
        let tokenData = await mysql.query('SELECT * FROM fb_access_tokens WHERE shop_id = ? order by row_created_at desc limit 1', [shop_id + '']);
        if (tokenData && tokenData.length > 0) {
            return tokenData[0];
        } else {
            return {}
        }
    } catch (err) {
        logger.error('fbtoken.get: ', err);
        return {}
    }
}

export {saveToken, generateToken, getToken}