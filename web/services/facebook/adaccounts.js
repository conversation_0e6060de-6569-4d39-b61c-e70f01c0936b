import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
import logger from "../logger.js";
var axios = require('axios');

async function getAdAccounts(access_token) {
    try {
        const axiosInstance = axios.create();

        var {data} = await axiosInstance.get(
            'me/adaccounts',
            {
                baseURL : 'https://graph.facebook.com/v14.0/',
                params : {
                    'access_token' : access_token,
                    'fields' : 'name,amount_spent,currency'
                },
            }
        )
    
        return data['data'] ?? [];
    } catch (err) {
        logger.error('facebook.getAdAccounts error : ' , err)
        return []
    }
}

async function getMonthlyInsights(account_id, access_token, start_date, end_date) {
    if (account_id == '' || !access_token) {
        return []        
    }

    let timeRange = {since : dayjs(start_date).format("YYYY-MM-DD"), until: dayjs(end_date).format("YYYY-MM-DD")}
    let parameters = {
        'access_token' : access_token,
        'fields' : 'account_id,account_name,account_currency,clicks,impressions,cpc,ctr,frequency, reach, spend, social_spend, cpm',
        'level': 'account',
        'time_range': JSON.stringify(timeRange),
        'time_increment': 'monthly'
    }

    const axiosInstance = axios.create();

    try {
        var {data} = await axiosInstance.get(
            '/'+ account_id +'/insights',
            {
                baseURL : 'https://graph.facebook.com/v14.0/',
                params : parameters,
            }
        )

        return data['data'] ?? [];
    } catch (e) {
        logger.error('getMonthlyInsights error - ', e)
        return []
    }
}

export {getAdAccounts, getMonthlyInsights}