import { initialize } from 'unleash-client';
import {cache} from './redis';

class CustomRedisStoreFeatureFlags {
  async set(key, data) {
    await cache.set(`ff_${key}`, data);
  }
  async get(key) {
    const data = await cache.get(`ff_${key}`);
    return data
  }
}

const unleash = initialize({
  url: 'https://feat.datadrew.io/api',
  appName: 'dashr',
  timeout: 30000, // 30 seconds
  refreshInterval: 120000, // 2 minutes
  metricsInterval: 120000, // 2 minutes
  customHeaders: { Authorization: process.env.UNLEASH_TOKEN },
  storageProvider: new CustomRedisStoreFeatureFlags(),
});


unleash.on('ready', () => console.log('Unleash is ready'));
unleash.on('synchronized', () => console.log('Unleash has synchronized'));
unleash.on('error', (error) => console.error('Unleash error:', error));
unleash.on('warn', (warn) => console.warn('Unleash warning:', warn));

export default unleash;