import util from '../../common/util.js';
import bigquery from '../../bigquery/index.js';

async function cartAnalysis({shop, start_date, end_date, breakdown, combination_size, basket_size, applied_filters}) {

    const {is_valid, errors} = util.validate(shop.shop_id, start_date, end_date)
    if (!is_valid) {
        return {errors}
    }

    let result = {}
    result.carts = await bigquery.cartAnalysis(
        shop.shop_id,
        start_date,
        end_date,
        combination_size,
        basket_size,
        breakdown,
        applied_filters
    );

    var money = util.moneyFormatter(shop.currency)
    for (var k in result.carts) {
        result.carts[k].order_percentage = `${result.carts[k].order_percentage.toFixed(2)}%`
        result.carts[k].aov_display = money(parseInt(result.carts[k].aov))
        result.carts[k].total_sales_display = money(parseInt(result.carts[k].total_sales))
        result.carts[k].new_total_sales_display = money(parseInt(result.carts[k].new_total_sales))
        result.carts[k].repeat_total_sales_display = money(parseInt(result.carts[k].repeat_total_sales))
    }
    return result
}

export {cartAnalysis}