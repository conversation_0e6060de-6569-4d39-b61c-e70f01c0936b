import logger from '../logger.js';
import shopService from '../shop.js';
import {cubejsAdminInstance} from "../cube/instance.js"
import bigqueryService from '../bigquery/index.js';
// TODO - improve cost for these queries
// TODO - first 5k products doesn't cut it - sort it out?
// TODO - handle ordering of the options
let filterQueries = (shop_ids) => {
    return {
        'shipping_address_city' : {
            "dimensions": [
                "Orders.shippingAddressCity"
            ],
            "filters": [
                {
                    "member": "Orders.shippingAddressCity",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'shipping_address_province' : {
            "dimensions": [
                "Orders.shippingAddressProvince"
            ],
            "filters": [
                {
                    "member": "Orders.shippingAddressProvince",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'shipping_address_country' : {
            "dimensions": [
                "Orders.shippingAddressCountry"
            ],
            "filters": [
                {
                    "member": "Orders.shippingAddressCountry",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'order_tags' : {
            "dimensions": [
                "Orders.tags"
            ],
            "filters": [
                {
                    "member": "Orders.tags",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'customer_tags' : {
            "dimensions": [
                "Customers.tags"
            ],
            "filters": [
                {
                "member": "Customers.tags",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'source_name' : {
            "dimensions": [
                "Orders.sourceName"
            ],
            "filters": [
                {
                    "member": "Orders.sourceName",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_type' : {
            "dimensions": [
                "Products.productType"
            ],
            "filters": [
                {
                "member": "Products.productType",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_tags' : {
            "dimensions": [
                "Products.tags"
            ],
            "filters": [
                {
                    "member": "Products.tags",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_vendor' : {
            "dimensions": [
                "Products.vendor"
            ],
            "filters": [
                {
                    "member": "Products.vendor",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_title' : {
            "dimensions": [
                "Products.title"
            ],
            "filters": [
                {
                    "member": "Products.title",
                    "operator": "set"
                },
                {
                    "member": "Shops.shopId",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        }
    }
}


let filterQueriesOSS = (shop_ids) => {
    return {
        'shipping_address_city' : {
            "dimensions": [
                "ShopifyOrders.shipping_address_city"
            ],
            "filters": [
                {
                    "member": "ShopifyOrders.shipping_address_city",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'shipping_address_province' : {
            "dimensions": [
                "ShopifyOrders.shipping_address_province"
            ],
            "filters": [
                {
                    "member": "ShopifyOrders.shipping_address_province",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'shipping_address_country' : {
            "dimensions": [
                "ShopifyOrders.shipping_address_country"
            ],
            "filters": [
                {
                    "member": "ShopifyOrders.shipping_address_country",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'order_tags' : {
            "dimensions": [
            "ShopifyOrders.tags"
            ],
            "filters": [
                {
                    "member": "ShopifyOrders.tags",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'customer_tags' : {
            "dimensions": [
                "ShopifyCustomers.tags"
            ],
            "filters": [
                {
                    "member": "ShopifyCustomers.tags",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'source_name' : {
            "dimensions": [
                "ShopifyOrders.source_name"
            ],
            "filters": [
                {
                    "member": "ShopifyOrders.source_name",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_type' : {
            "dimensions": [
                "ShopifyProducts.product_type"
            ],
            "filters": [
                {
                "member": "ShopifyProducts.product_type",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_tags' : {
            "dimensions": [
                "ShopifyProducts.tags"
            ],
            "filters": [
                {
                    "member": "ShopifyProducts.tags",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_vendor' : {
            "dimensions": [
                "ShopifyProducts.vendor"
            ],
            "filters": [
                {
                "member": "ShopifyProducts.vendor",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        },
        'product_title' : {
            "dimensions": [
                "ShopifyProducts.title"
            ],
            "filters": [
                {
                    "member": "ShopifyProducts.title",
                    "operator": "set"
                },
                {
                    "member": "ShopifyShops.shop_id",
                    "operator": "equals",
                    "values": shop_ids
                }
            ],
            "limit": 5000
        }
    }
}

function processTags(resultSet) {

    let tags = []
    for (var k in resultSet) {
        for (var ki in resultSet[k]) {
            tags.push(resultSet[k][ki].toString())
        }
    }

    var processed_tags = {}

    for (var k in tags) {
        if (!tags[k] || tags[k] == "") {
            continue
        }

        let tagStr = tags[k].trim();
        if (!tagStr) {
            continue
        }

        let tagList = tagStr.split(",")
        for (var ti in tagList) {
            tagList[ti] = tagList[ti].trim()
            if (!tagList[ti]) {
                continue
            }

            if (!(tagList[ti] in processed_tags)) {
                processed_tags[tagList[ti]] = 1
            }
        }
    }
    
    return Object.keys(processed_tags)
}

const getDiscountCodeFilterOptions = async (shop) => {
    let options = await bigqueryService.getDiscountCodeFilterOptions(shop.shop_id);
    return options;
}

const getFilterOptions = async ({filter, shop}) => {
    
    if (filter === 'discount_codes') {
        let options = await getDiscountCodeFilterOptions(shop);
        return {options: options, total: options.length};
    }

    let useMigrationDataset = await shopService.shouldUseMigrationDataset(shop.shop_id);
    const filterQueriesToUse = useMigrationDataset ? filterQueriesOSS([shop.shop_id]) : filterQueries([shop.shop_id]);

    if (!(filter in filterQueriesToUse)) {
        console.log('Invalid filter type');
        return {options: [], total: 0};
    }

    const cubejsApi = cubejsAdminInstance(useMigrationDataset);
    try {
      const resultSet = await cubejsApi.load(filterQueriesToUse[filter]);
      const result = resultSet.tablePivot();
      return {options: processTags(result), total: result.length};
    } catch (err) {
      logger.error("Error fetching filter options", err);
      return {options: [], total: 0};
    }
}

export {getFilterOptions}