import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
const isoWeek = require('dayjs/plugin/isoWeek')
dayjs.extend(isoWeek);

var quarterOfYear = require('dayjs/plugin/quarterOfYear')
dayjs.extend(quarterOfYear);

var time_frame_handler = {
    week : {
        format : (dt) => dt.isoWeekday(1).format("YYYY-MM-DD"),
        add : (dt) => dt.add(1, 'week'),
        row_name : (dt) => dt.isoWeekday(1).format(`Wk ${dt.isoWeek()}: DD MMM`),
        column_name: (i) => `${i}`,
        range : (dt) => {
            return {
                start_date : dt.isoWeekday(1).format("YYYY-MM-DD"),
                end_date : dt.isoWeekday(7).format("YYYY-MM-DD")
            }
        }
    },
    month : {
        format : (dt) => dt.format("YYYY-MM"),
        add : (dt) => dt.add(1, 'month'),
        row_name : (dt) => dt.format("MMM YYYY"),
        column_name: (i) => `${i}`,
        range : (dt) => {
            return {
                start_date : dt.startOf('month').format("YYYY-MM-DD"),
                end_date : dt.endOf('month').format("YYYY-MM-DD")
            }
        }
    },
    quarter : {
        format : (dt) => `${dt.format("YYYY")}-${String("0" + dt.quarter()).slice(-2)}`,
        add : (dt) => dt.add(1, 'quarter'),
        row_name : (dt) => [
            `Jan-Mar ${dt.format("YYYY")}`,
            `Apr-Jun ${dt.format("YYYY")}`,
            `Jul-Sept ${dt.format("YYYY")}`,
            `Oct-Dec ${dt.format("YYYY")}`,
        ][dt.quarter()-1],
        column_name: (i) => `${i}`,
        range : (dt) => {
            return {
                start_date : dt.startOf('quarter').format("YYYY-MM-DD"),
                end_date : dt.endOf('quarter').format("YYYY-MM-DD")
            }
        }
    },
    year : {
        format : (dt) => dt.format("YYYY"),
        add : (dt) => dt.add(1, 'year'),
        row_name : (dt) => dt.format("YYYY"),
        column_name: (i) => `${i}`,
        range : (dt) => {
            return {
                start_date : dt.startOf('year').format("YYYY-MM-DD"),
                end_date : dt.endOf('year').format("YYYY-MM-DD")
            }
        }
    }
}


async function createBreakdownCohorts(cohortList, cohort_start_date, end_date, time_frame, is_blur_active) {

    var handler = time_frame_handler[time_frame]
    var cohorts = {}
    var createBucket = (bucket_name, bucket_key, chart_name, index) => {
        return {
            name : bucket_name,
            customer_count : 0,
            dataKey : bucket_key,
            index : index,
            chart_name : chart_name,
            total_sales : 0,
            order_count : 0
        }
    }

    var currentDate = dayjs(cohort_start_date)
	var endDate = dayjs(end_date)
	for (var i = 0; i < cohortList.length; i++) {
        var key = cohortList[i]
        cohorts[key] = {
            name : cohortList[i],
            txn_frequency_map : {},
            dataKey : key,
            blur : is_blur_active,
            repeat_customer_count : 0,
            buckets : {},
            // to capture -> just first orders
            first_order_bucket : createBucket("First Order", "first_order", "First Order", "first_order"),
            // to capture -> first orders + subsequent orders in same month
            first_month : createBucket("First Month", "first_month", handler.row_name(currentDate), "first_month"),
        }

        var newCurrentDate = dayjs(cohort_start_date)
        var ind = 0;
        while(handler.format(newCurrentDate) <= handler.format(endDate)) {
            // months after the first order for the dynamic cohort
            // it is received as diff_0, diff_1, diff_2, etc for the first, second, third month
            var bucket_key = `diff_${ind}`
            cohorts[key].buckets[bucket_key] = createBucket(
                handler.column_name(ind),
                bucket_key,
                handler.row_name(newCurrentDate),
                ind
            )
            newCurrentDate = handler.add(newCurrentDate)
            ind += 1
        }
	}

	return cohorts
}


/*
    Create a Customer cohort based on first purchase of order.
    Cohorts can further have buckets like first_order_bucket, next time frame buckets
*/
async function createTimeFrameCohorts(cohort_start_date, cohort_end_date, time_frame, original_start_date, bucket_end_date) {

    var handler = time_frame_handler[time_frame]
    var cohorts = {}
    var createBucket = (bucket_name, bucket_key, chart_name, index, bucketDt) => {
        return {
            name : bucket_name,
            customer_count : 0,
            dataKey : bucket_key,
            index : index,
            chart_name : chart_name,
            drilldown: {
                ...handler.range(bucketDt),
                name : chart_name,
                dataKey : bucket_key
            },
            total_sales : 0,
            order_count : 0,
            customers : new Set() // deprecated
        }
    }

    var originalStartDateObj = dayjs(original_start_date)
    var currentDate = dayjs(cohort_start_date)
	var endDate = dayjs(cohort_end_date)
	while (handler.format(currentDate) <= handler.format(endDate)) {
        var key = handler.format(currentDate)
        cohorts[key] = {
            name : handler.row_name(currentDate),
            txn_frequency_map : {},
            dataKey : key,
            drilldown : {
                ...handler.range(currentDate),
                name : handler.row_name(currentDate),
                dataKey : key
            },
            blur : handler.format(currentDate) < handler.format(originalStartDateObj),
            repeat_customer_count : 0,
            buckets : {},
            // to capture -> just first orders
            first_order_bucket : createBucket("First Order", "first_order", "First Order", "first_order", currentDate),
            // to capture -> first orders + subsequent orders in same month
            first_month : createBucket("First Month", "first_month", handler.row_name(currentDate), "first_month", currentDate)
        }

        var newCurrentDate = dayjs(currentDate)
        var ind = 0;
        var bucketEndDate = dayjs(bucket_end_date)
        while(handler.format(newCurrentDate) <= handler.format(bucketEndDate)) {
            var bucket_key = handler.format(newCurrentDate)
            cohorts[key].buckets[bucket_key] = createBucket(
                handler.column_name(ind),
                bucket_key,
                handler.row_name(newCurrentDate),
                ind,
                newCurrentDate
            )
            newCurrentDate = handler.add(newCurrentDate)
            ind += 1
        }

		currentDate = handler.add(currentDate)
	}

	return cohorts
}

export {
    createBreakdownCohorts,
    createTimeFrameCohorts,
    time_frame_handler
}