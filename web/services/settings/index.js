import mysql from "../database.js";
import logger from '../logger.js';
import { cache, redis } from '../redis.js';
import automation from '../automation/index.js';
import shop from '../shop.js';

const SLACK_WEEKLY_REPORT = 'slack_weekly_report';
const EMAIL_WEEKLY_REPORT = 'email_weekly_report';
const GLOBAL_FILTERS = 'global_filters';

const getShopSettingsFromDB = async (shop_id) => {

    let key = `settings_${shop_id}`
    let settings = await cache.get(key)
    if (!!settings) {
        return settings
    }

    let dbSettings = []

    try {
        dbSettings = await mysql.query('SELECT * FROM shop_settings WHERE shop_id = ?', [shop_id + ""]);
    } catch (err) {
        logger.error('getShopSettingsFromDB: ', err)
    }

    if (dbSettings.length > 0) {
        await cache.set(key, dbSettings)
    }

    return dbSettings
}

export const getShopSettings = async function (shop_id, setting_key = "") {

    let settings = await getShopSettingsFromDB(shop_id)
    if (!settings || settings.length == 0) {
        return {}
    }

    let shopSettings = {};
    for (var k in settings) {
        let setting = settings[k]

        if (setting_key != "" && setting.setting_key != setting_key) {
            continue
        }

        shopSettings[setting.setting_key] = setting
        try {
            if (setting.setting_value) {
                shopSettings[setting.setting_key].setting_value = JSON.parse(setting.setting_value)
            }
        } catch (err) {
            logger.error('getShopSettings parse error : ', err)
        }
    }

    return shopSettings
}

// create a new setting
async function addShopSettings (shop_id, setting_key, setting_value) {

    try {
        JSON.stringify(setting_value)
    } catch (err) {
        logger.error("addShopSettings: JSON.stringify error", err);
        return false
    }

    const data = {
      shop_id,
      setting_key: setting_key,
      setting_value : JSON.stringify(setting_value),
      active_flag: 1
    };

    try {
      await mysql.query(`INSERT INTO shop_settings SET ?`, data);
      await cache.del(`settings_${shop_id}`)
      return true;
    } catch(err) {
      logger.error("addShopSettings: Query execution error", err);
      return false;
    }
}

// keep the job active_flag in sync with the setting active_flag
async function addOrUpdateJobForSetting (shop_id, setting_key, active_flag) {

    if (setting_key == GLOBAL_FILTERS) {
        await shop.pushAsyncTask({
            action: 'runDatadrewEUSync',
            waitForCompletion: true
        })
        // dbt("shopify") //  Not running on every settings update
        return true;
    }

    if (setting_key != SLACK_WEEKLY_REPORT && setting_key != EMAIL_WEEKLY_REPORT) {
        return true;
    }

    // get all active jobs for this setting
    let jobs = await automation.getJobs(shop_id, setting_key);
    if (jobs.length == 0) {
        if (active_flag) {
            return await automation.makeWeeklyReportJob(shop_id, setting_key);
        }
        return true;
    }

    // ideally there should be only one job
    for (let job of jobs) {
        if (job.active_flag != active_flag) {
            let done = await automation.updatePartial(job.job_id, {active_flag: active_flag});
            if (!done) {
                return false;
            }
        }
    }

    return true;
}

// add or update settings
// update only active_flag and setting_value for a setting that already exists
// update by shop_id and setting_key
export const addOrUpdateSettings = async function (shop_id, setting_key, newSetting) {

    if (!newSetting || !setting_key || Object.keys(newSetting).length == 0) {
        return false
    }

    let shopSettings = await getShopSettings(shop_id, setting_key)
    let setting = shopSettings[setting_key] ?? {}


    if (!setting || !setting.setting_key) {
        let settingsAdded = addShopSettings(shop_id, setting_key, newSetting.setting_value ?? {})
        if (settingsAdded) {
            return await addOrUpdateJobForSetting(shop_id, setting_key, 1);
        }
        return false;
    } else if (setting.shop_id != shop_id) {
        logger.error("addOrUpdateSettings: shop_id mismatch", setting);
        return false
    }

    // only update active_flag and setting_value
    let newActiveFlag = newSetting.active_flag ?? setting.active_flag
    let newSettingValue = newSetting.setting_value ?? setting.setting_value
    try {
        newSettingValue = JSON.stringify(newSettingValue)
    } catch (err) {
        logger.error("addOrUpdateSettings: JSON.stringify error", err);
        return false
    }

    let updateSettingObj = {
        active_flag : newActiveFlag,
        setting_value : newSettingValue
    }

    try {
        await mysql.query("UPDATE shop_settings SET ? WHERE auto_id = ?", [updateSettingObj, setting.auto_id]);
        await cache.del(`settings_${shop_id}`)

        return await addOrUpdateJobForSetting(shop_id, setting_key, newActiveFlag);
    } catch (err) {
        logger.error('addOrUpdateSettings: ', err);
        return false;
    }
}

export default {
    SLACK_WEEKLY_REPORT,
    EMAIL_WEEKLY_REPORT,
    getShopSettings,
    addOrUpdateSettings
}