import mysql from '../../services/database.js';
import logger from '../../services/logger.js';
import { cache } from '../../services/redis.js';
import UserInvitation, { USER_INVITATION_STATUS_PENDING } from '../user/invitation/index.js';
import user from '../user.js';
import Workspace from './index.js';


const WORKSPACE_USER_STATUS_ACTIVE = 1
const WORKSPACE_USER_STATUS_INACTIVE = 0

export const WORKSPACE_ROLE_OWNER = 'Owner';
export const WORKSPACE_ROLE_ADMIN = 'Admin';
export const WORKSPACE_ROLE_MEMBER = 'Member';
class WorkspaceUser {

    authed_user = null
    workspace_id = null
    user_id = null

    constructor(authed_user) {
        this.authed_user = authed_user;
    }

    setWorkspaceID(workspace_id) {
        this.workspace_id = workspace_id;
        return this;
    }

    setUserID(user_id) {
        this.user_id = user_id;
        return this;
    }

    static #getByWorkspaceIDAndUserIDCacheKey(workspace_id, user_id) {
        return `workspace_user_by_workspace_id_and_user_id_v3_${workspace_id}_${user_id}`;
    }

    static async clearAllByWorkspaceIDAndUserIDCache(workspace_id, user_id) {
        var cacheKey = WorkspaceUser.#getByWorkspaceIDAndUserIDCacheKey(workspace_id, user_id);
        await cache.del(cacheKey);
    }

    static async getByWorkspaceIDAndUserID(workspace_id, user_id) {
        var cacheKey = WorkspaceUser.#getByWorkspaceIDAndUserIDCacheKey(workspace_id, user_id);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache
        }

        var workspace_user = {}
        try {
            const query = `SELECT a.*, b.email as user_email FROM workspace_users a
                INNER JOIN users b
                ON a.user_id = b.uid
                WHERE a.workspace_id = ? and a.user_id = ?`;
            let result = await mysql.query(query, [workspace_id, user_id]);
            workspace_user = result.length > 0 ? result[0] : {}
        } catch (err) {
            logger.error('workspace.getByWorkspaceIDAndUserID error : ', err)
        }

        if (workspace_user) {
            await cache.set(cacheKey, workspace_user)
        }

        return workspace_user
    }

    static #getByWorkspaceIDAndEmailCacheKey(workspace_id, email) {
        return `workspace_user_by_workspace_id_and_email_v2_${workspace_id}_${email}`;
    }

    static async clearAllByWorkspaceIDAndEmailCache(workspace_id, email) {
        var cacheKey = WorkspaceUser.#getByWorkspaceIDAndEmailCacheKey(workspace_id, email);
        await cache.del(cacheKey);
    }

    static async getByWorkspaceIDAndEmail(workspace_id, email) {
        var cacheKey = WorkspaceUser.#getByWorkspaceIDAndEmailCacheKey(workspace_id, email);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache
        }

        var workspace_user = {}
        try {
            const query = `SELECT * from workspace_users a
                INNER JOIN users b
                ON a.user_id = b.uid
                WHERE a.workspace_id = ? and b.email = ?`;
            let result = await mysql.query(query, [workspace_id, email]);
            workspace_user = result.length > 0 ? result[0] : {}
        } catch (err) {
            logger.error('workspace.getByWorkspaceIDAndEmail error : ', err)
        }

        if (workspace_user) {
            await cache.set(cacheKey, workspace_user)
        }

        return workspace_user
    }

    static #getAllByEmailCacheKey(email) {
        return `workspace_users_by_email_v2_${email}`;
    }

    static async clearAllByEmailCache(email) {
        var cacheKey = WorkspaceUser.#getAllByEmailCacheKey(email);
        await cache.del(cacheKey);
    }

    static async getAllByEmail(email) {
        var cacheKey = WorkspaceUser.#getAllByEmailCacheKey(email);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache
        }

        var workspace_users = []
        try {
            const query = `SELECT * from workspace_users a
                INNER JOIN users b
                ON a.user_id = b.uid
                WHERE b.email = ? and a.status = ?`;
            let result = await mysql.query(query, [email, WORKSPACE_USER_STATUS_ACTIVE]);
            workspace_users = result.length > 0 ? result : []
        } catch (err) {
            logger.error('workspace.getByWorkspaceIDAndEmail error : ', err)
        }

        if (workspace_users) {
            await cache.set(cacheKey, workspace_users)
        }

        return workspace_users
    }

    static async addUserToWorkspace(workspace_id, user_id, role, shop_ids_string) {
        let workspace_user = await WorkspaceUser.getByWorkspaceIDAndUserID(workspace_id, user_id);
        if (workspace_user && workspace_user.id) {
            logger.error('workspace.addUserToWorkspace: user already exists in workspace', {
                workspace_id: workspace_id,
                user_id: user_id
            });
            return {status: true, message: 'User already exists in workspace. Please manage permissions in workspace settings.'};
        }

        try {
            await mysql.query("INSERT INTO workspace_users SET ?", {
                workspace_id: workspace_id,
                user_id: user_id,
                role: role,
                shop_ids: shop_ids_string,
                status: Number(WORKSPACE_USER_STATUS_ACTIVE)
            });

            await WorkspaceUser.clearAllByWorkspaceIDCache(workspace_id);
            await WorkspaceUser.clearAllByEmailCache(workspace_user.user_email);
            await WorkspaceUser.clearUserWorkspacesCache(user_id);
            await user.clearUserCache(user_id, workspace_id);
            return {status: true, message: 'User added to workspace successfully'};
        } catch (err) {
            logger.error('workspace.addUserToWorkspace: failed to add user to workspace', err);
            return {status: false, message: 'Failed to add user to workspace'};
        }
    }

    static #getAllByWorkspaceIDCacheKey(workspace_id) {
        return `workspace_users_by_workspace_id_v4_${workspace_id}`;
    }

    static async clearAllByWorkspaceIDCache(workspace_id) {
        var cacheKey = WorkspaceUser.#getAllByWorkspaceIDCacheKey(workspace_id);
        await cache.del(cacheKey);
    }

    static async getAllByWorkspaceID(workspace_id) {
        var cacheKey = WorkspaceUser.#getAllByWorkspaceIDCacheKey(workspace_id);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache
        }

        if (!workspace_id) {
            logger.error('WorkspaceUser->getAllByWorkspaceID : missing required params', {
                workspace_id: workspace_id
            });
            return []
        }

        let workspace_users = []
        try {
            const query = `
                SELECT
                    a.id,
                    a.workspace_id,
                    a.user_id,
                    a.role,
                    GROUP_CONCAT(c.shop_id) as shop_ids,
                    a.status,
                    b.name as user_name, b.onboard_name as user_onboard_name, b.email as user_email
                FROM workspace_users a
                INNER JOIN users b
                    ON a.user_id = b.uid
                LEFT JOIN workspace_shops c
                    ON FIND_IN_SET(c.shop_id, a.shop_ids) > 0
                    AND c.workspace_id = a.workspace_id
                    AND c.status = 1
                WHERE a.workspace_id = ?
                GROUP BY a.id`;
            workspace_users = await mysql.query(query, [workspace_id]);

            if (workspace_users && workspace_users.length > 0) {
                workspace_users = workspace_users.map(wu => ({
                    ...wu,
                    shop_ids: wu.shop_ids ? wu.shop_ids.split(","): [],
                    user_name: wu.user_name ? wu.user_name : wu.user_onboard_name
                }));
            } else {
                workspace_users = []
            }
        } catch (err) {
            logger.error('WorkspaceUser->getAllByWorkspaceID : failed to get workspace users', err);
            return []
        }

        if (workspace_users) {
            await cache.set(cacheKey, workspace_users)
        }

        return workspace_users
    }

    static async updateWorkspaceMember(workspace_id, user_id, data) {
        if (!workspace_id || !user_id || !data) {
            logger.error('WorkspaceUser->updateWorkspaceMember : missing required params', {
                workspace_id: workspace_id,
                user_id: user_id,
                data: data
            });
            return {status: false, message: 'Missing required params'};
        }

        let workspace_user = await WorkspaceUser.getByWorkspaceIDAndUserID(workspace_id, user_id);
        if (!workspace_user) {
            logger.error('WorkspaceUser->updateWorkspaceMember : workspace user not found', {
                workspace_id: workspace_id,
                user_id: user_id
            });
            return {status: false, message: 'Workspace user not found'};
        }

        let role = data.role || workspace_user.role;
        let shop_ids = data.shop_ids || workspace_user.shop_ids;

        try {
            await mysql.query("UPDATE workspace_users SET role = ?, shop_ids = ? WHERE workspace_id = ? AND user_id = ?", [role, shop_ids.join(","), workspace_id, user_id]);
            await WorkspaceUser.clearAllByWorkspaceIDAndUserIDCache(workspace_id, user_id);
            await WorkspaceUser.clearAllByWorkspaceIDCache(workspace_id);
            await WorkspaceUser.clearAllByWorkspaceIDAndEmailCache(workspace_id, workspace_user.user_email);
            await WorkspaceUser.clearAllByEmailCache(workspace_user.user_email);
            await WorkspaceUser.clearUserWorkspacesCache(user_id);
            await user.clearUserCache(user_id, workspace_id);
            await Workspace.clearWorkspaceDetailsForUserCache(workspace_id, user_id);
            return {status: true, message: 'Workspace member updated successfully'};
        } catch (err) {
            logger.error('WorkspaceUser->updateWorkspaceMember : failed to update workspace member', err);
            return {status: false, message: 'Failed to update workspace member'};
        }
    }

    static #getUserWorkspacesCacheKey(userId) {
        return `user_workspaces_v1_${userId}`;
    }

    static async clearUserWorkspacesCache(userId) {
        var cacheKey = WorkspaceUser.#getUserWorkspacesCacheKey(userId);
        await cache.del(cacheKey);
    }

    static async getUserWorkspaces(userId) {
        // Check cache first
        var cacheKey = WorkspaceUser.#getUserWorkspacesCacheKey(userId);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache;
        }

        try {
            // Get all workspace associations and shops in a single query
            const query = `
                SELECT
                    wu.workspace_id,
                    wu.role,
                    wu.shop_ids,
                    wu.status,
                    w.workspace_type,
                    w.name as workspace_name,
                    ws.shop_id,
                    s.name as shop_name,
                    s.myshopify_domain
                FROM workspace_users wu
                JOIN workspaces w ON wu.workspace_id = w.workspace_id
                LEFT JOIN workspace_shops ws ON wu.workspace_id = ws.workspace_id AND ws.status = 1
                LEFT JOIN shops s ON ws.shop_id = s.shop_id
                WHERE wu.user_id = ? AND wu.status = 1
                ORDER BY wu.workspace_id
            `;

            const results = await mysql.query(query, [userId]);

            // Process results to group by workspace
            const workspacesMap = new Map();

            for (const row of results) {
                const workspaceId = row.workspace_id;

                // If this is a new workspace, initialize it in the map
                if (!workspacesMap.has(workspaceId)) {
                    const shopIds = row.shop_ids ? row.shop_ids.split(',') : [];
                    const isOwner = row.role === WORKSPACE_ROLE_OWNER;

                    workspacesMap.set(workspaceId, {
                        workspace_id: workspaceId,
                        name: row.workspace_name,
                        workspace_type: row.workspace_type,
                        role: row.role,
                        is_owner: isOwner,
                        is_admin: row.role === WORKSPACE_ROLE_ADMIN,
                        is_member: row.role === WORKSPACE_ROLE_MEMBER,
                        shops: [],
                        status: row.status,
                        _shopIds: shopIds // Temporary property to help with filtering
                    });
                }

                // Add shop to workspace if it exists and user has access
                if (row.shop_id) {
                    const workspace = workspacesMap.get(workspaceId);
                    const shopIds = workspace._shopIds;

                    // Only add shop if user has access (empty shopIds means access to all)
                    if (shopIds.length === 0 || shopIds.includes(row.shop_id)) {
                        // Check if shop is already added (avoid duplicates)
                        const shopExists = workspace.shops.some(s => s.shop_id === row.shop_id);

                        if (!shopExists) {
                            workspace.shops.push({
                                shop_id: row.shop_id,
                                name: row.shop_name,
                                myshopify_domain: row.myshopify_domain
                            });
                        }
                    }
                }
            }

            // Convert map to array and remove temporary properties
            const workspacesWithDetails = Array.from(workspacesMap.values()).map(workspace => {
                const { _shopIds, ...cleanWorkspace } = workspace;
                return cleanWorkspace;
            });

            // Cache the results
            if (workspacesWithDetails.length > 0) {
                await cache.set(cacheKey, workspacesWithDetails);
            }

            return workspacesWithDetails;
        } catch (error) {
            logger.error('WorkspaceUser->getUserWorkspaces : failed to get user workspaces', error);
            return [];
        }
    }
}

export default WorkspaceUser;