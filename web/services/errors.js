export default {
    client: {
        generic: "Something went wrong. Please try again later.",
        invalidInput: "Invalid input provided.",
        unauthorized: "You are not authorized to perform this action.",
        notFound: "The requested resource could not be found.",
        timeout: "The request timed out. Please try again.",
    },
    internal: {
        databaseConnection: "Failed to connect to the database.",
        queryExecution: "Failed to execute the database query.",
        dataProcessing: "Error occurred during data processing.",
        unexpected: "An unexpected error occurred.",
        validation: "Data validation failed.",
        authentication: "Failed to authenticate the user.",
    }
}