import { SOURCE_FB } from '../../airbyte/constants.js';
import logger from '../../logger.js';
import data from './data.js';
import errors from '../../errors.js';
import integrations from '../../airbyte/integrations.js';

const summary = async (intgs, filters) => {

    const {
        start_date,
        end_date,
        compare,
        compare_start_date,
        compare_end_date,
        time_frame
    } = filters;


    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    if (!intgs || intgs.length == 0) {
        return response;
    }

    let source_config_ids = intgs.map(intg => intg.source_config_id)
    try {

        let granularData = data.queryAccountMetrics(source_config_ids, start_date, end_date, compare, compare_start_date, compare_end_date, time_frame)
        let aggregateData = data.queryAccountMetrics(source_config_ids, start_date, end_date, compare, compare_start_date, compare_end_date)

        let resultSet = await Promise.all([
            granularData,
            aggregateData
        ]);

        // TODO - handle error

        response.data.currency = resultSet[0]?.currency ?? resultSet[1]?.currency ?? ""
        response.data.current = resultSet[0].current ?? []
        response.data.previous = resultSet[0].previous ?? []
        response.data.currentAgg = resultSet[1].current?.[0] ?? {}
        response.data.previousAgg = resultSet[1].previous?.[0] ?? {}
        return response;
    } catch (e) {
        logger.error('facebook.summary error', e)
        return response;
    }
}

// depreated - legacy call
const pivotTable = async (shop_id, filters) => {
    const {
        start_date,
        end_date,
        integration_request_ids
    } = filters;

    let response = {
        data: []
    }

    if (!integration_request_ids || integration_request_ids.length == 0) {
        response.error = errors.client.invalidInput
        return response;
    }

    let intgs = await integrations.getIntegrations(shop_id, SOURCE_FB, false, false)
    let fbIntgs = (intgs[SOURCE_FB] ?? []).filter(intg => integration_request_ids.includes(intg.request_id))
    let source_config_ids = fbIntgs.map(intg => intg.source_config_id)
    try {
        let result = await data.queryPivotTable(source_config_ids, start_date, end_date)
        response.data = result.data ?? []
        return response;
    } catch (e) {
        logger.error('facebook.pivotTable error', e)
        return response;
    }
}

const breakdownData = async (intgs, filters) => {
    const {
        start_date,
        end_date,
        time_frame,
        breakdown
    } = filters;

    let response = {
        data: {
            currency: "",
            current: [],
        }
    }

    if (!intgs || intgs.length == 0) {
        return response;
    }

    let source_config_ids = intgs.map(intg => intg.source_config_id)

    try {
        let result = await data.queryWithBreakdown(source_config_ids, start_date, end_date, time_frame, breakdown)
        
        response.data.currency = result.currency ?? ""
        response.data.current = result.current ?? []
        return response;
    } catch (e) {
        logger.error('facebook.breakdownData error', e)
        return response;
    }
}

export default {
    summary,
    pivotTable,
    breakdownData
}