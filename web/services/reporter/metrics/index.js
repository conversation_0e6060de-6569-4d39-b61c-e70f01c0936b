import { SOURCE_GOOGLE_ADS, SOURCE_FB, SOURCE_GOOGLE_ANALYTICS } from '../../airbyte/constants.js';
import logger from '../../logger.js';
import integrations from '../../airbyte/integrations.js';
import errors from '../../errors.js';
import google_ads from '../google_ads/index.js';
import facebook from '../facebook/index.js';
import google_analytics from '../google_analytics/index.js';
import shopify from '../shopify/index.js';
import blended from '../blended/index.js';

const combinedSummary = async (shop_id, filters) => {
    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    const {integration_request_ids, metrics} = filters;

    let fetch_all_metrics = false;
    let metrics_to_add = {
        shopify: false,
        google_ads: false,
        facebook: false,
        google_analytics: false
    }

    // Set flags for metrics to fetch
    if (metrics) {
        metrics.forEach(metric => {
           if (metric.startsWith("shopify:")) {
                metrics_to_add.shopify = true
            } else if (metric.startsWith("google-ads:")) {
                metrics_to_add.google_ads = true
            } else if (metric.startsWith("facebook:")) {
                metrics_to_add.facebook = true
            } else if (metric.startsWith("google-analytics:")) {
                metrics_to_add.google_analytics = true
            } else {
                fetch_all_metrics = true;
            }
        })
    }

    // Filter integrations based on integration_request_ids
    let integrations_to_filter = [];
    if (!!integration_request_ids && integration_request_ids.length > 0) {
        integrations_to_filter = integration_request_ids;
    }

    // Get integrations
    let intgs = await integrations.getIntegrations(shop_id, "", false, false)

    // Initialize summaries
    let facebookAdsSummary = {}
    let googleAdsSummary = {}
    let gaSummary = {}
    let shopifySummary = {}

    if (fetch_all_metrics || metrics_to_add.facebook) {
        let fbIntgs = intgs[SOURCE_FB] ?? [];
        if (integrations_to_filter.length > 0) {
            fbIntgs = fbIntgs.filter(intg => integrations_to_filter.includes(intg.request_id))
        }
        facebookAdsSummary = await facebook.summary(fbIntgs, filters)
    }

    if (fetch_all_metrics || metrics_to_add.google_ads) {
        let googleAdsIntgs = intgs[SOURCE_GOOGLE_ADS] ?? [];
        if (integrations_to_filter.length > 0) {
            googleAdsIntgs = googleAdsIntgs.filter(intg => integrations_to_filter.includes(intg.request_id))
        }
        googleAdsSummary = await google_ads.summary(googleAdsIntgs, filters)
    }

    if (fetch_all_metrics || metrics_to_add.google_analytics) {
        let gaIntgs = intgs[SOURCE_GOOGLE_ANALYTICS] ?? [];
        if (integrations_to_filter.length > 0) {
            gaIntgs = gaIntgs.filter(intg => integrations_to_filter.includes(intg.request_id))
        }
        gaSummary = await google_analytics.summary(gaIntgs, filters)
    }

    if (fetch_all_metrics || metrics_to_add.shopify) {
        shopifySummary = await shopify.summary(shop_id, filters)
    }

    // Blended metrics for Google ads and Facebook ads
    if (googleAdsSummary.error || facebookAdsSummary.error || gaSummary.error) {
        logger.error('metrics.summary error', {errors: [googleAdsSummary.error, facebookAdsSummary.error, gaSummary.error]})
        response.error = errors.client.generic;
        return response;
    }

    const mergeEntriesByBucketId = (entriesArr) => {
        const mergedEntries = {};
        entriesArr.forEach(entries => {
            entries.forEach(entry => {
                if (!mergedEntries[entry.bucketId]) {
                    mergedEntries[entry.bucketId] = {...entry}
                } else {
                    mergedEntries[entry.bucketId] = {
                        ...mergedEntries[entry.bucketId],
                        ...entry
                    }
                }
            });
        });
        return Object.values(mergedEntries);
    }

    // TODO - currency conversion
    response.data.currency = googleAdsSummary.data?.currency
        || facebookAdsSummary.data?.currency
        || gaSummary.data?.currency
        || shopifySummary.data?.currency
        || "";

    // Merging current and previous entries
    response.data.current = mergeEntriesByBucketId([
        googleAdsSummary.data?.current ?? [],
        facebookAdsSummary.data?.current ?? [],
        gaSummary.data?.current ?? [],
        shopifySummary.data?.current ?? []
    ]);

    response.data.previous = mergeEntriesByBucketId([
        googleAdsSummary.data?.previous ?? [],
        facebookAdsSummary.data?.previous ?? [],
        gaSummary.data?.previous ?? [],
        shopifySummary.data?.previous ?? []
    ]);

    // Merging current and previous aggregated entries
    response.data.currentAgg = {
        ...(googleAdsSummary.data?.currentAgg ?? {}),
        ...(facebookAdsSummary.data?.currentAgg ?? {}),
        ...(gaSummary.data?.currentAgg ?? {}),
        ...(shopifySummary.data?.currentAgg ?? {})
    }

    response.data.previousAgg = {
        ...(googleAdsSummary.data?.previousAgg ?? {}),
        ...(facebookAdsSummary.data?.previousAgg ?? {}),
        ...(gaSummary.data?.previousAgg ?? {}),
        ...(shopifySummary.data?.previousAgg ?? {})
    }

    return response;
}

const summary = async (shop_id, filters) => {
    let response = await combinedSummary(shop_id, filters);

    // console.log(response);

    // Apply formulae to each entry in the current and previous arrays
    response.data.current = response.data.current.map(data => blended.addCalculatedMetrics(blended.blendedMetrics, data));
    response.data.previous = response.data.previous.map(data => blended.addCalculatedMetrics(blended.blendedMetrics, data));

    // Apply formulae to currentAgg and previousAgg aggregates
    response.data.currentAgg = blended.addCalculatedMetrics(blended.blendedMetrics, response.data.currentAgg);
    response.data.previousAgg = blended.addCalculatedMetrics(blended.blendedMetrics, response.data.previousAgg);

    return response;
};

export default {
    summary
}
  