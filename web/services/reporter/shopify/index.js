import logger from '../../logger.js';
import data from './data.js';

const summary = async (shop_id, filters) => {

    const {
        start_date,
        end_date,
        compare,
        compare_start_date,
        compare_end_date,
        time_frame
    } = filters;


    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    if (!shop_id) {
        return response;
    }

    try {

        let granularData = data.queryAccountMetrics(shop_id, start_date, end_date, compare, compare_start_date, compare_end_date, time_frame)
        let aggregateData = data.queryAccountMetrics(shop_id, start_date, end_date, compare, compare_start_date, compare_end_date)

        let resultSet = await Promise.all([
            granularData,
            aggregateData
        ]);


        response.data.currency = resultSet[0]?.currency ?? resultSet[1]?.currency ?? ""
        response.data.current = resultSet[0].current ?? []
        response.data.previous = resultSet[0].previous ?? []
        response.data.currentAgg = resultSet[1].current?.[0] ?? {}
        response.data.previousAgg = resultSet[1].previous?.[0] ?? {}
        return response;
    } catch (e) {
        logger.error('shopify.summary error', e)
        return response;
    }
}

export default {
    summary
}