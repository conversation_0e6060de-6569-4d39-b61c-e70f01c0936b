import { cubejsAdminInstance } from "../../cube/instance.js";
import util from "../../common/util.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import logger from "../../logger.js";
import reporterUtil from "../util.js";
import shop from "../../shop.js";

const DIMENSIONS = [
  "shop_metrics.shop_currency",
  "shop_metrics.shop_id"
];

// parse result to remove prefix from keys
const parseResult = (result) => result.map(row =>
  Object.keys(row).reduce((acc, key) => {
    const newKey = key.replace("shop_metrics.", "shopify:").replace("_week", "").replace("_quarter", "").replace("_month", "").replace("_year", "");
    acc[newKey] = row[key] ?? 0;
    return acc;
  }, {})
);

const queryAccountMetrics = async (shop_id, start_date, end_date, compare, compare_start_date, compare_end_date, granularity = "") => {
  let response = {
    currency: "",
    current: [],
    previous: []
  }

  if (!shop_id) {
    response.error = "Invalid shop_id";
    return response
  }

  let timeDimensionFilter = {
    "dimension": "shop_metrics.date",
    "compareDateRange": [
      [start_date, dayjs(end_date).add(1, 'day').toDate()]
    ],
  }

  if (compare) {
    timeDimensionFilter.compareDateRange.push([compare_start_date, dayjs(compare_end_date).add(1, 'day').toDate()])
  }

  // Validate granularity
  if (granularity && !util.TIME_FRAMES[granularity]) {
    logger.error("Invalid granularity", {granularity})
    response.error = "Invalid granularity";
    return response;
  }

  if (granularity) {
    timeDimensionFilter.granularity = granularity;
  }

  let additional_measures = [
    "shop_metrics.cust_count",
    "shop_metrics.new_cust_count",
    "shop_metrics.returning_cust_count",
    "shop_metrics.arpu",
    "shop_metrics.new_cust_arpu",
    "shop_metrics.returning_cust_arpu"
  ];

  for (let i = 0; i < additional_measures.length; i++) {
    if (!granularity || granularity == "day") {
        continue;
    }
    additional_measures[i] = additional_measures[i] + "_" + granularity;
  }

  let query = {
    "limit": 5000,
    "dimensions": [
      "shop_metrics.shop_currency"
    ],
    "filters": [
      {
        "member": "shop_metrics.shop_id",
        "operator": "equals",
        "values": [shop_id]
      },
      {
        "member": "shop_metrics.shop_currency",
        "operator": "set"
      }
    ],
    "measures": [
        ...additional_measures,
        "shop_metrics.order_count",
        "shop_metrics.total_price",
        "shop_metrics.aov",
        "shop_metrics.count",
        "shop_metrics.new_cust_order_count",
        "shop_metrics.returning_cust_order_count",
        "shop_metrics.new_cust_total_price",
        "shop_metrics.returning_cust_total_price",
        "shop_metrics.new_cust_aov",
        "shop_metrics.returning_cust_aov",
        "shop_metrics.rev_last1m",
        "shop_metrics.rev_last3m",
        "shop_metrics.rev_last6m",
        "shop_metrics.rev_last1y",
    ],
    "timeDimensions": [timeDimensionFilter],
    "order": [["shop_metrics.date", "asc"]]
  }

  try {
      const useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
      const cubejsApi = cubejsAdminInstance(useMigrationDataset);
      const resultSet = await cubejsApi.load(query);

      resultSet.decompose().forEach((currentResultSet, ind) => {
          let rawData = currentResultSet.rawData();
          if (rawData.length !== 0) {

            let parsedData = parseResult(rawData)
            response.currency = parsedData[0]["shopify:shop_currency"] ?? "";
            if (ind == 0 && !granularity) {
              response.current = parsedData
            } else if (ind == 1 && !granularity) {
              response.previous = parsedData
            } else if (ind == 0 && !!granularity) {
              const timeDimension = `shopify:date.${granularity}`;
              response.current = reporterUtil.fillMissingDates('shopify', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)
            } else if (ind == 1 && !!granularity) {
              const timeDimension = `shopify:date.${granularity}`;
              response.previous = reporterUtil.fillMissingDates('shopify', parsedData, compare_start_date, compare_end_date, granularity, timeDimension, DIMENSIONS)
            }
          }
      });

      return response;
  } catch (error) {
    logger.error("Error in fetching shopify account metrics", error)
    response.error = error.message ?? ""
    return response
  }
}

export default {
  queryAccountMetrics
}