import util from "../common/util.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);


// Create a new empty row with 0 values except for dimensions, which will be copied from the resultRow
const createPlaceholderRow = (allDimensions, resultRow) => {
  return Object.keys(resultRow).reduce((acc, key) => {
    if (allDimensions.includes(key)) {
      acc[key] = resultRow[key]; // Keep the dimension's original value
    } else {
      acc[key] = 0; // Set other fields to 0
    }
    return acc;
  }, {});
};


// fillMissingDates : Final result will have all buckets with data filled from resultMap
// create all buckets of selected duration & granualarity
const fillMissingDates = (source, result, start_date, end_date, granularity, timeDimension, allDimensions) => {

    if (result.length === 0) {
      return []
    }
  
    let placeholderRow = createPlaceholderRow(allDimensions, result[0])
    // Create a map of the result keyed by bucketId
    let resultMap = {};
    for (var k in result) {
      const dt = dayjs(result[k][timeDimension]);
      const bucketId = util.TIME_FRAMES[granularity].format(dt);
      if (!(bucketId in resultMap)) {
        resultMap[bucketId] = []
      }
      resultMap[bucketId].push(result[k])
    }
  
    var bucketsMap = util.createTimeFrameBuckets(start_date, end_date, granularity)
  
    let filledResult = [];
    Object.entries(bucketsMap).map(([_, timeBucket]) => {
  
      // Add one empty row for each bucket that doesn't have data
      if (!(timeBucket.id in resultMap)) {
        filledResult.push({
          ...placeholderRow,
          [timeDimension]: timeBucket.start,
          source,
          bucketId: timeBucket.id ?? "",
          bucketName: timeBucket.name ?? "",
          bucketNameShort: timeBucket.short ?? ""
        });
        return;
      }
  
      resultMap[timeBucket.id].forEach(row => {
        filledResult.push({
          ...row,
          source,
          bucketId: timeBucket.id ?? "",
          bucketName: timeBucket.name ?? "",
          bucketNameShort: timeBucket.short ?? ""
        })
      })
    });
  
    return filledResult;
  }

  export default {fillMissingDates}