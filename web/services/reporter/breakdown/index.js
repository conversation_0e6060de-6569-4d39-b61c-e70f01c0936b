import { SOURCE_GOOGLE_ADS, SOURCE_FB } from '../../airbyte/constants.js';
import logger from '../../logger.js';
import integrations from '../../airbyte/integrations.js';
import errors from '../../errors.js';
import google_ads from '../google_ads/index.js';
import facebook from '../facebook/index.js';
import blended from '../blended/index.js';

const combinedData = async (shop_id, filters) => {
    let response = {
        data: {
            currency: "",
            current: [],
        }
    }

    const {preset, integration_request_ids} = filters;

    let integrations_to_filter = [];
    if (!!integration_request_ids && integration_request_ids.length > 0) {
        integrations_to_filter = integration_request_ids;
    }

    let intgs = await integrations.getIntegrations(shop_id, "", false, false)

    let fbIntgs = (intgs[SOURCE_FB] ?? []).filter(intg => integrations_to_filter.includes(intg.request_id))
    let facebookAdsData = await facebook.breakdownData(fbIntgs, filters)

    let googleAdsIntgs = (intgs[SOURCE_GOOGLE_ADS] ?? []).filter(intg => integrations_to_filter.includes(intg.request_id))
    let googleAdsData = await google_ads.breakdownData(googleAdsIntgs, filters)

    if (googleAdsData.error || facebookAdsData.error) {
        logger.error('breakdown.data error', {errors: [googleAdsData.error, facebookAdsData.error]})
        response.error = errors.client.generic;
        return response
    }

    if (preset == SOURCE_FB) {
        return facebookAdsData;
    } else if (preset == SOURCE_GOOGLE_ADS) {
        return googleAdsData;
    } else if (preset != "blended") {
        return response;
    }

    // TODO: Add currency conversion
    response.data.currency = googleAdsData.data?.currency
        || facebookAdsData.data?.currency
        || "";

    // Merging current and previous entries
    response.data.current = (googleAdsData.data?.current ?? []).map(data => {
        return {
            "ad:channel" : "Google Ads",
            "ad:campaign_id" : data["google-ads:campaign_id"] ?? "",
            "ad:campaign_name" : data["google-ads:campaign_name"] ?? "",
            ...blended.addCalculatedMetrics(blended.adMetrics, data)
        }
    }).concat((facebookAdsData.data?.current ?? []).map(data => {
        return {
            "ad:channel" : "Facebook Ads",
            "ad:campaign_id" : data["facebook-marketing:campaign_id"] ?? "",
            "ad:campaign_name" : data["facebook-marketing:campaign_name"] ?? "",
            ...blended.addCalculatedMetrics(blended.adMetrics, data)
        }
    }));

    return response;
}

const data = async (shop_id, filters) => {
    let response = await combinedData(shop_id, filters);
    return response;
};

export default {
    data
}