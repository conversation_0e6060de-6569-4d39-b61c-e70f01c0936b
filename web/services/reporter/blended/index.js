const blendedMetrics = {
    "blended:spend": {
        operation: "sum",
        operands: ["google-ads:cost", "facebook-marketing:spend"]
    },
    "blended:impressions": {
        operation: "sum",
        operands: ["google-ads:impressions", "facebook-marketing:impressions"]
    },
    "blended:clicks": {
        operation: "sum",
        operands: ["google-ads:clicks", "facebook-marketing:clicks"]
    },
    "blended:cac" : {
        operation: "divide",
        operands: ["blended:spend", "shopify:new_cust_count"]
    },
    "blended:ctr": {
        operation: "custom",
        operands: ["blended:clicks", "blended:impressions"],
        customOperation: (clicks, impressions) => impressions !== 0 ? (clicks / impressions) * 100 : 0
    },
    "blended:cpc": {
        operation: "divide",
        operands: ["blended:spend", "blended:clicks"]
    },
    "blended:roas": {
        operation: "divide",
        operands: ["shopify:total_price", "blended:spend"]
    },
    "blended:cpm": {
        operation: "custom",
        operands: ["blended:spend", "blended:impressions"],
        customOperation: (spend, impressions) => impressions !== 0 ? (spend / impressions) * 1000 : 0
    },
    "blended:conversions": {
        operation: "sum",
        operands: ["google-ads:conversions", "facebook-marketing:website_purchase"]
    },
    "blended:conversions_value": {
        operation: "sum",
        operands: ["google-ads:conversions_value", "facebook-marketing:website_purchase_value"]
    },
    "blended:order_conversion_rate" : {
        operation: "custom",
        operands: ["blended:conversions", "blended:clicks"],
        customOperation: (conversions, clicks) => clicks !== 0 ? (conversions / clicks) * 100 : 0
    },
};

const adMetrics = {
    "ad:spend": {
        operation: "sum",
        operands: ["google-ads:cost", "facebook-marketing:spend"]
    },
    "ad:impressions": {
        operation: "sum",
        operands: ["google-ads:impressions", "facebook-marketing:impressions"]
    },
    "ad:clicks": {
        operation: "sum",
        operands: ["google-ads:clicks", "facebook-marketing:clicks"]
    },
    "ad:conversions": {
        operation: "sum",
        operands: ["google-ads:conversions", "facebook-marketing:website_purchase"]
    },
    "ad:conversions_value": {
        operation: "sum",
        operands: ["google-ads:conversions_value", "facebook-marketing:website_purchase_value"]
    },
    "ad:ctr": {
        operation: "custom",
        operands: ["ad:clicks", "ad:impressions"],
        customOperation: (clicks, impressions) => impressions !== 0 ? (clicks / impressions) * 100 : 0
    },
    "ad:cpc": {
        operation: "divide",
        operands: ["ad:spend", "ad:clicks"]
    },
    "ad:cpm": {
        operation: "custom",
        operands: ["ad:spend", "ad:impressions"],
        customOperation: (spend, impressions) => impressions !== 0 ? (spend / impressions) * 1000 : 0
    },
    "ad:order_rate" : {
        operation: "custom",
        operands: ["ad:conversions", "ad:clicks"],
        customOperation: (conversions, clicks) => clicks !== 0 ? (conversions / clicks) * 100 : 0
    },
    "ad:roas" : {
        operation: "divide",
        operands: ["ad:conversions_value", "ad:spend"]
    },
    "ad:cac" : {
        operation: "divide",
        operands: ["ad:spend", "ad:conversions"]
    },
    "ad:aov" : {
        operation: "divide",
        operands: ["ad:conversions_value", "ad:conversions"]
    }
}

// Helper function to apply formulae with dependency management
const addCalculatedMetrics = (metrics, data) => {
    const result = { ...data };
    const computed = new Set();

    const computeMetric = (key) => {
        if (computed.has(key)) return result[key];
        const formula = metrics[key];
        if (!formula) return result[key];

        const values = formula.operands.map(op => {
            if (metrics[op] && !computed.has(op)) computeMetric(op);
            return result[op] || 0;
        });

        if (formula.operation === "sum") {
            result[key] = values.reduce((acc, val) => parseFloat(acc) + parseFloat(val), 0);
        } else if (formula.operation === "divide") {
            result[key] = values[1] !== 0 ? values[0] / values[1] : 0;
        } else if (formula.operation === "custom" && typeof formula.customOperation === "function") {
            result[key] = formula.customOperation(...values);
        }

        computed.add(key);
        return result[key];
    };

    for (const key of Object.keys(metrics)) {
        computeMetric(key);
    }

    return result;
};

export default {
    blendedMetrics,
    adMetrics,
    addCalculatedMetrics
};