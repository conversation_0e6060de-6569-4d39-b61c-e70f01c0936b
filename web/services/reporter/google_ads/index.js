import logger from '../../logger.js';
import data from './data.js';

const summary = async (integrations, filters) => {

    const {
        start_date,
        end_date,
        compare,
        compare_start_date,
        compare_end_date,
        time_frame
    } = filters;


    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    if (!integrations || integrations.length == 0) {
        return response;
    }

    let integration_ids = integrations.map(integration => integration.auto_id)
    try {

        let granularData = data.queryAccountMetrics(integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, time_frame)
        let aggregateData = data.queryAccountMetrics(integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date)

        let resultSet = await Promise.all([
            granularData,
            aggregateData
        ]);

        response.data.currency = resultSet[0]?.currency ?? resultSet[1]?.currency ?? ""
        response.data.current = resultSet[0].current ?? []
        response.data.previous = resultSet[0].previous ?? []
        response.data.currentAgg = resultSet[1].current?.[0] ?? {}
        response.data.previousAgg = resultSet[1].previous?.[0] ?? {}
        return response;
    } catch (e) {
        logger.error('google_ads.summary error', e)
        return response;
    }
}


const breakdownData = async (integrations, filters) => {
    const {
        start_date,
        end_date,
        time_frame,
        breakdown
    } = filters;

    let response = {
        data: {
            currency: "",
            current: [],
        }
    }

    if (!integrations || integrations.length == 0) {
        return response;
    }

    let integration_ids = integrations.map(integration => integration.auto_id)

    try {
        let result = await data.queryWithBreakdown(integration_ids, start_date, end_date, time_frame, breakdown)
        response.data.currency = result.currency ?? ""
        response.data.current = result.current ?? []
        return response;
    } catch (e) {
        logger.error('google_ads.breakdownData error', e)
        return response;
    }
}


export default {
    summary,
    breakdownData
}