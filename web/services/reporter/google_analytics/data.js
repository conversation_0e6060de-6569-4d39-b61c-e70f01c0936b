import { cubejsAdminInstance } from "../../cube/instance.js";
import util from "../../common/util.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import logger from "../../logger.js";
import reporterUtil from "../util.js";


const DIMENSIONS = [
  "ga_account_metrics.property_id",
  "ga_account_metrics.integration_id"
]

// parse result to remove prefix from keys
const parseResult = (result) => result.map(row =>
  Object.keys(row).reduce((acc, key) => {
    const newKey = key.replace("ga_account_metrics.", "google-analytics-data-api:");
    acc[newKey] = row[key] ?? 0;
    return acc;
  }, {})
);

const queryAccountMetrics = async (integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, granularity = "") => {

    let response = {
        currency : "",
        current: [],
        previous: []
    }

    if (!integration_ids || integration_ids.length == 0) {
      response.error = "Invalid integration_ids";
      return response
    }

    let timeDimensionFilter = {
        "dimension": "ga_account_metrics.date",
        "compareDateRange": [
            [start_date, dayjs(end_date).add(1, 'day').toDate()]
        ],
    }

    if (compare) {
        timeDimensionFilter.compareDateRange.push([compare_start_date, dayjs(compare_end_date).add(1, 'day').toDate()])
    }

    // Validate granularity
    if (granularity && !util.TIME_FRAMES[granularity]) {
        logger.error("Invalid granularity", {granularity})
        response.error = "Invalid granularity";
        return response;
    }

    if (granularity) {
        timeDimensionFilter.granularity = granularity;
    }

    let query = {
        "limit": 5000,
        "dimensions": [],
        "filters": [{
            "member": "ga_account_metrics.integration_id",
            "operator": "equals",
            "values": integration_ids
        }],
        "measures": [
            "ga_account_metrics.total_revenue",
            "ga_account_metrics.total_users",
            "ga_account_metrics.bounce_rate",
            "ga_account_metrics.new_users",
            "ga_account_metrics.daily_active_users",
            "ga_account_metrics.sessions",
            "ga_account_metrics.engaged_sessions",
            "ga_account_metrics.screen_page_views",
            "ga_account_metrics.total_items_added_to_cart",
            "ga_account_metrics.ecommerce_purchases",
            "ga_account_metrics.users_non_unique",
            "ga_account_metrics.user_engagement_duration_secs",
            "ga_account_metrics.conversions",
            "ga_account_metrics.transactions",
            "ga_account_metrics.avg_order_value",
            "ga_account_metrics.conversion_rate",
            "ga_account_metrics.revenue_per_user",
            "ga_account_metrics.engagement_rate",
            "ga_account_metrics.new_customer_rate",
            "ga_account_metrics.revenue_per_session",
            "ga_account_metrics.add_to_cart_rate",
            "ga_account_metrics.cart_abandonment_rate",
            "ga_account_metrics.engaged_conversion_rate",
            "ga_account_metrics.purchase_frequency",
            "ga_account_metrics.user_engagement_duration_mins",
            "ga_account_metrics.average_engagement_time",
            "ga_account_metrics.average_purchase_revenue",
            "ga_account_metrics.page_views_per_session",
            "ga_account_metrics.purchase_conversion_rate"
        ],
        "timeDimensions": [timeDimensionFilter],
        "order": [["ga_account_metrics.date", "asc"]]
    }

    try {
        const cubejsApi = cubejsAdminInstance();
        const resultSet = await cubejsApi.load(query);

        resultSet.decompose().forEach((currentResultSet, ind) => {
            let rawData = currentResultSet.rawData();
            if (rawData.length !== 0) {

                let parsedData = parseResult(rawData)
                response.currency = ""; // TODO
                if (ind == 0 && !granularity) {
                  response.current = parsedData
                } else if (ind == 1 && !granularity) {
                  response.previous = parsedData
                } else if (ind == 0 && !!granularity) {
                  const timeDimension = `google-analytics-data-api:date.${granularity}`;
                  response.current = reporterUtil.fillMissingDates('google-analytics-data-api', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)
                } else if (ind == 1 && !!granularity) {
                  const timeDimension = `google-analytics-data-api:date.${granularity}`;
                  response.previous = reporterUtil.fillMissingDates('google-analytics-data-api', parsedData, compare_start_date, compare_end_date, granularity, timeDimension, DIMENSIONS)
                }
            }
        });

        return response;
    } catch (error) {
        logger.error("Error in fetching google analytics account metrics", error)
        response.error = error.message ?? ""
        return response
    }
}

export default {
  queryAccountMetrics
}