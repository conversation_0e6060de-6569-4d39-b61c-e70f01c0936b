import { createRequire } from "module";
const require = createRequire(import.meta.url);
const Redis = require("ioredis");
import logger from './logger.js';

export var redis = new Redis(process.env.REDIS_URI);
redis.on('error', (e) => {
    logger.error('redis error - ', e)
})

const DEFAULT_CACHE_TTL = 86400 * 5;


// wrapper to add redis connection check
class RedisCache {
    constructor(redis_client) {
        this.redis_client = redis_client
    }

    async get(key) {
        try {
            var resp = await this.redis_client.get(key)
        } catch (err) {
            logger.error(`redis - ${key} error -`, err)
            return null
        }

        if (resp === null) {
            return null
        }

        try {
            var value = JSON.parse(resp)
            return value
        } catch (err) {
            logger.error(`redis - ${key} parse error -`, err)
            return null
        }
    }
    
    // set stores a value for a key in Redis, with an optional TTL
    async set(key, value, ttl = DEFAULT_CACHE_TTL) {
        try {
            if (ttl === null) {
                return await this.redis_client.set(key, JSON.stringify(value));
            } else {
                return await this.redis_client.set(key, JSON.stringify(value), "EX", ttl);
            }
        } catch (err) {
            logger.error(`redis - ${key} set error -`, err)
            return null
        }
    }

    // del removes a key from Redis
    async del(key) {
        try {
            return await this.redis_client.del(key)
        } catch (err) {
            logger.error(`redis - ${key} del error -`, err)
            return null
        }
    }
}

export var cache = new RedisCache(redis)

export default {redis, cache};
