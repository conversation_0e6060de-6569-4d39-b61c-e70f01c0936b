import { createRequire } from "module";
const require = createRequire(import.meta.url);
var mysql      = require('mysql');
import dotenv from 'dotenv';

dotenv.config();

console.log('mysql', process.env.MYSQL_DB_USER)

var mysql_config = {
    user     : process.env.MYSQL_DB_USER,
    password : process.env.MYSQL_DB_PASSWORD,
    database : process.env.MYSQL_DB_NAME
}

if (process.env.NODE_ENV !== "production") {
    mysql_config.host = process.env.MYSQL_DB_HOST;
} else {
    mysql_config.socketPath = `/cloudsql/${process.env.CLOUD_SQL_CONNECTION_NAME}`;
}

const pool = mysql.createPool(mysql_config);

const connection = () => {
    return new Promise((resolve, reject) => {
        pool.getConnection((err, connection) => {
            if (err) reject(err);
            const query = (sql, binding) => {
                return new Promise((resolve, reject) => {
                    connection.query(sql, binding, (err, result) => {
                        if (err) reject(err);
                        resolve(result);
                    });
                });
            };

            const release = () => {
                return new Promise((resolve, reject) => {
                    if (err) reject(err);
                    resolve(connection.release());
                });
            };

            resolve({ query, release });
        });
    });
};

/**
 * 
 * Using query function for single queries in most functions
 * Avoiding Connection Leaks: When ensuring that connections are always properly released is critical
 */
const query = (sql, binding) => {
    return new Promise((resolve, reject) => {
        pool.query(sql, binding, (err, result, fields) => {
            if (err) reject(err);
            resolve(result);
        });
  });
};

export default mysql = { pool, connection, query };