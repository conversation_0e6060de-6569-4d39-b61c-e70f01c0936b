export const INTEGRATION_STATUS_DRAFT = 0;
export const INTEGRATION_STATUS_CONNECTED = 1;
export const INTEGRATION_STATUS_DISCONNECTED = 2;
export const INTEGRATION_STATUS_SYNCED = 3;

export const PLATFORM_CLOUD = "airbyte_cloud";
export const CLOUD_BIGQUERY_DESTINATION_ID = "9b72b4ce-6727-44bd-beb3-6948cffb5759";
export const CLOUD_AIRBYTE_WORKSPACE_ID="6cf812a8-b9a5-41ca-ae57-fea2b2a96d41";

// Platform airbyte_oss
export const PLATFORM_OSS = "airbyte_oss";
export const OSS_AIRBYTE_WORKSPACE_ID = "60a37fe6-46e9-48c2-9bdf-f11eee3a69b2";
export const OSS_API_URL = 'https://ab.datadrew.io/api/public/v1';
export const OSS_SHOPIFY_DESTINATION_ID = "6ff56709-6cf6-49d8-8194-fec6fbaec24b";
export const OSS_GOOGLE_ANALYTICS_DESTINATION_ID = "04b1472a-d346-4ab0-aea3-94cf495592b9";
export const OSS_GOOGLE_ADS_DESTINATION_ID = "d283cd92-2e08-46a6-8d70-72be7da6cc1d";
export const OSS_KLAVIYO_DESTINATION_ID = "bb5148e2-7e2d-4091-90c7-20d0abde3524";

// Platform airbyte_oss_v1
export const V1_PLATFORM_OSS = "airbyte_oss_v1";
export const V1_OSS_AIRBYTE_WORKSPACE_ID = "6bc17fa0-d15e-4db5-91f5-ed212872899b";
export const V1_OSS_API_URL = 'https://airbyte.datadrew.io/api/public/v1';
export const V1_OSS_SHOPIFY_DESTINATION_ID = "a2637be5-70ca-4b82-8fdf-11bf7811ba28";
export const V1_OSS_DEFAULT_DESTINATION_ID = "e0f17e95-22dd-4b05-b4e4-00729b514577";
export const V1_OSS_GOOGLE_ANALYTICS_DESTINATION_ID = V1_OSS_DEFAULT_DESTINATION_ID;
export const V1_OSS_GOOGLE_ADS_DESTINATION_ID = V1_OSS_DEFAULT_DESTINATION_ID;
export const V1_OSS_KLAVIYO_DESTINATION_ID = V1_OSS_DEFAULT_DESTINATION_ID;

export const VALID_SOURCE_TYPES = ['facebook-marketing', 'google-ads', 'klaviyo', 'google-analytics-data-api', 'shopify'];
export const VALID_OSS_SOURCE_TYPES = ['google-ads', 'google-analytics-data-api', 'klaviyo', 'shopify'];
export const VALID_CLOUD_SOURCE_TYPES = ['facebook-marketing'];


/** Shopify */
export const SOURCE_SHOPIFY = "shopify";
export const CONNECTION_CONFIG_SHOPIFY_DEFAULT = {
  streams: [
    {
      name: "orders",
      syncMode: "incremental_append",
      cursorField: ["updated_at"],
      primaryKey: [["id"]]
    },
    // customer_address - Not used right now, removing to make syncs faster
    // {
    //   name: "customer_address",
    //   syncMode: "incremental_append",
    //   cursorField: ["id"],
    //   primaryKey: [["id"]]
    // },
    {
      name: "product_images",
      syncMode: "incremental_append",
      cursorField: ["updated_at"],
      primaryKey: [["id"]]
    },
    {
      name: "products",
      syncMode: "incremental_append",
      cursorField: ["updated_at"],
      primaryKey: [["id"]]
    },
    // {
    //   name: "products_graph_ql",
    //   syncMode: "incremental_append",
    //   cursorField: ["updatedAt"],
    //   primaryKey: [["id"]]
    // },
    {
      name: "customers",
      syncMode: "incremental_append",
      cursorField: ["updated_at"],
      primaryKey: [["id"]]
    },
    {
      name: "order_refunds",
      syncMode: "incremental_append",
      cursorField: ["created_at"],
      primaryKey: [["id"]]
    },
    {
      name: "shop",
      syncMode: "full_refresh_append",
      cursorField: [],
      primaryKey: [["id"]]
    }
  ]
};

/** Google Analytics */

export const SOURCE_GOOGLE_ANALYTICS = "google-analytics-data-api";
export const GOOGLE_ANALYTICS_SCOPES = [
  "https://www.googleapis.com/auth/analytics.readonly"
];
export const CONNECTION_CONFIG_GOOGLE_ANALYTICS_DEFAULT = {
  streams: [
    {
      name: "daily_active_users",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"]],
    },
    {
      name: "weekly_active_users",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"]],
    },
    {
      name: "four_weekly_active_users",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"]],
    },
    {
      name: "devices",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"], ["deviceCategory"], ["operatingSystem"],["browser"]],
    },
    {
      name: "locations",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["region"], ["country"], ["city"], ["date"]],
    },
    {
      name: "pages",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"], ["hostName"], ["pagePathPlusQueryString"]],
    },
    {
      name: "traffic_sources",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"], ["sessionSource"], ["sessionMedium"]],
    },
    {
      name: "website_overview",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"]],
    },
    {
      name: "traffic_acquisition_session_source_medium_report",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"], ["sessionSource"],["sessionMedium"]]
    },
    {
      name: "ecommerce_purchases_item_name_report",
      syncMode: "incremental_deduped_history",
      cursorField: ["date"],
      primaryKey: [["property_id"], ["date"], ["itemName"]]
    },
  ],
};

/** Google Ads */

export const SOURCE_GOOGLE_ADS = "google-ads";
export const GOOGLE_ADS_SCOPES = ["https://www.googleapis.com/auth/adwords"];
export const CONNECTION_CONFIG_GOOGLE_ADS_DEFAULT = {
  streams: [
    {
      name: "ad_group_ad",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [["ad_group.id"], ["ad_group_ad.ad.id"], ["segments.date"]],
    },
    {
      name: "campaign_budget",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [
        ["customer.id"],
        ["campaign_budget.id"],
        ["segments.date"],
        ["segments.budget_campaign_association_status.campaign"],
        ["segments.budget_campaign_association_status.status"],
      ],
    },
    {
      name: "campaign",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [
        ["campaign.id"],
        ["segments.date"],
        ["segments.hour"],
        ["segments.ad_network_type"],
      ],
    },
    {
      name: "campaign_bidding_strategy",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [["campaign.id"], ["bidding_strategy.id"], ["segments.date"]],
    },
    {
      name: "audience",
      syncMode: "full_refresh_overwrite",
      cursorField: [],
      primaryKey: [["customer.id"], ["audience.id"]],
    },
    {
      name: "campaign_criterion",
      syncMode: "incremental_deduped_history",
      cursorField: ["change_status.last_change_date_time"],
      primaryKey: [["campaign_criterion.resource_name"]],
    },
    {
      name: "campaign_label",
      syncMode: "full_refresh_overwrite",
      cursorField: [],
      primaryKey: [["campaign.id"], ["label.id"]],
    },
    {
      name: "customer",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [["customer.id"], ["segments.date"]],
    },
    {
      name: "account_performance_report",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [
        ["customer.id"],
        ["segments.date"],
        ["segments.ad_network_type"],
        ["segments.device"],
      ],
    },
    {
      name: "ad_group",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [["ad_group.id"], ["segments.date"]],
    },
    {
      name: "click_view",
      syncMode: "incremental_deduped_history",
      cursorField: ["segments.date"],
      primaryKey: [
        ["click_view.gclid"],
        ["segments.date"],
        ["segments.ad_network_type"],
      ],
    },
  ],
};


/** Klaviyo */

export const SOURCE_KLAVIYO = 'klaviyo';
export const CONNECTION_CONFIG_KLAVIYO_DEFAULT = {
  streams: [
    {
      name: "email_templates",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated"],
      primaryKey: [["id"]],
    },
    {
      name: "metrics",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated"],
      primaryKey: [["id"]],
    },
    {
      name: "campaigns",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_at"],
      primaryKey: [["id"]],
    },
    {
      name: "profiles",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated"],
      primaryKey: [["id"]],
    },
    {
      name: "lists",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated"],
      primaryKey: [["id"]],
    },
    {
      name: "flows",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated"],
      primaryKey: [["id"]],
    },
  ],
};

/** Facebook */

export const SOURCE_FB = 'facebook-marketing';
export const CONNECTION_CONFIG_FB_DEFAULT = {
  streams: [
    {
      name: "ad_account",
      syncMode: "full_refresh_overwrite",
    },
    {
      name: "ad_sets",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_time"],
    },
    {
      name: "ads",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_time"],
    },
    {
      name: "ads_insights",
      syncMode: "incremental_deduped_history",
      cursorField: ["date_start"],
    },
    // TODO - need this?
    // {
    //     "name": "ads_insights_action_type",
    //     "syncMode": "incremental_deduped_history",
    //     "cursorField": ["date_start"],
    //     "primaryKey": [["date_start"],["account_id"],["ad_id"]]
    // },
    {
      name: "campaigns",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_time"],
    },
    {
      name: "images",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_time"],
    },
    {
      name: "videos",
      syncMode: "incremental_deduped_history",
      cursorField: ["updated_time"],
    },
    // {
    //     "name": "ad_creatives",
    //     "syncMode": "full_refresh_append",
    //     "primaryKey": [["id"]]
    // },
    // TODO - temp disabling as error gets thrown - Please reduce the amount of data you're asking for
    // {
    //     "name": "custom_audiences",
    //     "syncMode": "full_refresh_append",
    //     "primaryKey": [["id"]]
    // },
  ],
};