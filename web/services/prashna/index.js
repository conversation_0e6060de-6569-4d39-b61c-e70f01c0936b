

/**
 * For Admin:
 *  Filter threads only with shop_id -> threads created by admin + threads created by non-admin users
 *
 * For Non-Admin User Flow:
 *  Filter threads with shop_id and user_email -> threads unique to the user
 *
 * For Non-Admin Shop Flow:
 *  Filter threads with shop_id and user_email = "" -> threads common to all users of the shop
 */


export function getMetadataForSearchThreads(shop_id, is_admin, is_user_flow = false, user_email = "") {
    if (is_admin) {
        return {
            shop_id: shop_id
        }
    } else if (is_user_flow) {
        return {
            shop_id: shop_id,
            user_email: user_email,
            is_admin: false
        }
    } else {
        return {
            shop_id: shop_id,
            user_email: "", // to ensure user flow threads are not shown in shop flow
            is_admin: false
        }
    }
}
