import { getUserByEmail } from '../../user.js';
import logger from '../../logger.js';
import WorkspaceUser, { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_MEMBER } from '../../workspace/workspace_user.js';

const FLOW_REGISTER = 'register';
const FLOW_LOGIN = 'login';

class Identification {

    static async handleEmailSubmit(email, shop_id) {
        try {
            const user = await getUserByEmail(email);
            if (!user) {
                logger.info(`User with email not found in shopify identification, please register.`, { email });
                return {
                    status: true,
                    message: 'User with email not found, please register.',
                    flow: FLOW_REGISTER
                }
            }

            let workspaceUsers = await WorkspaceUser.getAllByEmail(email);

            workspaceUsers = workspaceUsers.filter(workspaceUser => 
              workspaceUser.shop_ids && 
              workspaceUser.shop_ids.split(',').includes(shop_id)
            );

            let loginWorkspaceUser = workspaceUsers.find(workspaceUser => workspaceUser.role === WORKSPACE_ROLE_OWNER);

            if (!loginWorkspaceUser) {
                loginWorkspaceUser = workspaceUsers.find(workspaceUser => workspaceUser.role === WORKSPACE_ROLE_MEMBER);
            }

            return {
                status: true,
                message: 'User with email found, please continue with login.',
                flow: FLOW_LOGIN,
                config: {
                    workspace_exists: !!loginWorkspaceUser,
                    workspace_id: loginWorkspaceUser ? loginWorkspaceUser.workspace_id : null,
                    shop_id: shop_id
                }
            }
        } catch (error) {
            logger.error('ShopifyIdentification.handleEmailSubmit error : ', error);
            return {
                status: false,
                message: 'Error while checking user with email.',
            }
        }
    }
}

export default Identification;
