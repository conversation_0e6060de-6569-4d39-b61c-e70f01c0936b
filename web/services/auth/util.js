/**
 * Sets the selectedShop cookie, frontend reads this cookie and sets it as 
 * default shop in state and local storage and deletes the cookie.
 * 
 * This is done via backend so that the shop selection is switch only when 
 * we receive successful auth from Shopify
 */
export function setSelectedShopCookieInResponse(res, shop_domain) {
    res.cookie('selectedShop', shop_domain, {
        httpOnly: false, // Allow JavaScript access
        secure: process.env.NODE_ENV === 'production', // Use secure in production
        sameSite: 'lax'
    });
}

export default {
    setSelectedShopCookieInResponse
}