import admin from 'firebase-admin';

import user from '../../../services/user.js';
import logger from '../../logger.js';

class User {

    async #createUserInDB(uid, email) {
        try {
            await user.save({
                email: email,
                uid: uid
            });
            return true;
        } catch (err) {
            logger.error(`User::createUserInDB error: ${err}`, { email });
            return false
        }
    }

    async createNewAuthedUser(uid, email) {
        try {
            const userObj = await user.getUserByEmail(email);
            if (userObj) {
                logger.info(`User::createNewAuthedUser -> Duplicate user creation request.`, { email });
                return {
                    status: true,
                    message: 'User already exists'
                }
            }

            const userCreated = await this.#createUserInDB(uid, email);

            if (!userCreated) {
                return {
                    status: false,
                    message: 'Failed to create user, please try again later.'
                }
            }

            return {
                status: true,
                message: 'User created successfully.'
            }
        } catch (err) {
            logger.error(`User::createNewAuthedUser error: ${err}`, { email });
            return {
                status: false,
                message: 'Internal server error'
            }
        }
    }
}

export default User;