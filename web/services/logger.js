import { createRequire } from "module";
const require = createRequire(import.meta.url);

const winston = require('winston');
const { createLogger, format, transports } = winston;

// Imports the Google Cloud client library for Winston
const {LoggingWinston} = require('@google-cloud/logging-winston');

// Create a Google Cloud Logging client
const loggingWinston = new LoggingWinston({
  // timeout: 120000,
  projectId : process.env.PROJECT_ID ?? 'shopify-301218',
  redirectToStdout: true,
  useMessageField : false
});

let winstonTransports = [
    new transports.Console({
        format: format.combine(
          format.colorize(),
          format.simple()
        )
    })
];

if (process.env.NODE_ENV === 'production') {
  winstonTransports = [loggingWinston];
}

const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    format.errors({ stack: true }),
    format.splat(),
    format.json()
  ),
  transports: winstonTransports
});

// // Handle uncaught exceptions and unhandled rejections
// logger.exceptions.handle(
//   new transports.Console(),
//   loggingWinston
// );

// process.on('unhandledRejection', (reason, promise) => {
//   console.log('Unhandled Rejection at:', promise, 'reason:', reason);
//   // Application specific logging, throwing an error, or other logic here
// });

export default logger;