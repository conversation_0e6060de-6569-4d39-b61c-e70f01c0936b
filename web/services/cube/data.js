import { cubejsAdminInstance } from "./instance.js";
import logger from '../logger.js';
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek)
import util from '../common/util.js';
import numeral from 'numeral';
import shop from '../shop.js';

const MAX_LIMIT_CUBEJS = 500000; 

async function rfmSegmentExport(shop_id, segment, period = "all_time") {
  const filters = [
    {
      member: "RfmSegments.period",
      operator: "equals",
      values: [period]
    },
    {
      member: "RfmSegments.shopId",
      operator: "equals",
      values: [shop_id],
    }
  ];

  if (segment && segment !== "all") {
    filters.push({
      member: "RfmSegments.rfmSegment",
      operator: "equals",
      values: [segment]
    });
  }

  const query = {
      "limit": MAX_LIMIT_CUBEJS,
      "offset": 0,
      "dimensions": [
          "RfmSegments.customerId",
          "RfmSegments.rfmScore",
          "RfmSegments.rfmSegment",
          "RfmSegments.customerFirstName",
          "RfmSegments.customerEmail",
          "RfmSegments.customerPhone",
          "RfmSegments.customerLastName"
      ],
      "filters": filters,
      "measures": [
          "RfmSegments.lastOrderSince",
          "RfmSegments.totalOrders",
          "RfmSegments.totalSpend",
          "RfmSegments.lastOrderCreatedAt"
      ],
      "order": {
          "RfmSegments.lastOrderCreatedAt": "desc"
      }
  }

  const useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
  const cubejsApi = cubejsAdminInstance(useMigrationDataset);
  try {
    const resultSet = await cubejsApi.load(query);
    const result = resultSet.tablePivot();
    const formattedResponse = result.map((row) => {
      return {
        customer_id: row["RfmSegments.customerId"],
        rfm_score: row["RfmSegments.rfmScore"],
        email:  row["RfmSegments.customerEmail"],
        segment: row["RfmSegments.rfmSegment"],
        first_name: row["RfmSegments.customerFirstName"],
        last_name: row["RfmSegments.customerLastName"],
        phone: row["RfmSegments.customerPhone"],
        last_order_since: row["RfmSegments.lastOrderSince"],
        total_orders: row["RfmSegments.totalOrders"],
        total_spend: row["RfmSegments.totalSpend"],
        last_order_at: row["RfmSegments.lastOrderCreatedAt"]
      }
    });

    return {
      data : formattedResponse,
      fields: ["customer_id", "rfm_score", "email", "segment", "first_name", "last_name", "phone", "last_order_since", "total_orders", "total_spend", "last_order_at"]
    };
  } catch (error) {
    logger.error("Error fetching data:", error);
    return {error: "Error fetching RFM Segment Customers"};
  }
}

async function fetchRFMSegmentCustomers(shop_id, segment, period = "all_time") {
  const filters = [
    {
      member: "RfmSegments.period",
      operator: "equals",
      values: [period]
    },
    {
      member: "RfmSegments.shopId",
      operator: "equals",
      values: [shop_id],
    },
    {
      member: "RfmSegments.customerEmail",
      operator: "notEquals",
      values: [""]
    }
  ];

  if (segment && segment !== "all") {
    filters.push({
      member: "RfmSegments.rfmSegment",
      operator: "equals",
      values: [segment]
    });
  }

  const parameters = {
    limit: MAX_LIMIT_CUBEJS,
    offset: 0,
    dimensions: ["RfmSegments.customerEmail", "RfmSegments.rfmSegment"],
    filters: filters,
    order: {
      "RfmSegments.lastOrderCreatedAt": "desc"
    }
  };

  const useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
  const cubejsApi = cubejsAdminInstance(useMigrationDataset);
  const segmentEmailObject = {};
  try {
    const resultSet = await cubejsApi.load(parameters);
    const result = resultSet.tablePivot();
    result.forEach((row) => {
      const email = row["RfmSegments.customerEmail"];
      const segment = row["RfmSegments.rfmSegment"];

      if (!segmentEmailObject[segment]) {
        segmentEmailObject[segment] = [];
      }
    
      segmentEmailObject[segment].push(email);
    });
    return segmentEmailObject;
  } catch (error) {
    logger.error("Error fetching data:", error);
    return {};
  }
}

async function facebookMetrics(account_ids, start_date, end_date) {

  if (account_ids.length == 0) {
      return []
  }

  const cubejsApi = cubejsAdminInstance()
  try {
      let resultSet = await cubejsApi.load({
          "dimensions": [
            "ProcessedAdsInsights.accountId",
            "ProcessedAdsInsights.accountName",
            "ProcessedAdsInsights.accountCurrency"
          ],
          "filters": [],
          "measures": [
            "ProcessedAdsInsights.spendSum",
            "ProcessedAdsInsights.clicksSum",
            "ProcessedAdsInsights.impressionsSum",
            "ProcessedAdsInsights.linkClickSum",
            "ProcessedAdsInsights.addToCartSum",
            "ProcessedAdsInsights.purchaseSum"
          ],
          "timeDimensions": [
            {
              "dimension": "ProcessedAdsInsights.partitionDt",
              "dateRange": [
                start_date,
                end_date
              ]
            }
          ]
        })

      let resultData = resultSet.tablePivot()
      return resultData;
  } catch (error) {
      logger.error("Error in fetching facebook metrics", error)
      return []
  }
}

async function shopMetrics(shop_ids, start_date, end_date, useMigrationDataset = false) {

  if (shop_ids.length == 0) {
      return []
  }

  const cubejsApi = cubejsAdminInstance(useMigrationDataset);

  try {
      let resultSet = await cubejsApi.load({
          "dimensions": [
              "shop_metrics.shop_id",
              "shop_metrics.shop_currency"
          ],
          "measures": [
              "shop_metrics.order_count",
              "shop_metrics.total_price",
              "shop_metrics.aov",
              "shop_metrics.new_cust_count",
              "shop_metrics.returning_cust_count"
          ],
          "timeDimensions": [{
              "dimension": "shop_metrics.date",
              "dateRange": [
                  start_date,
                  end_date
              ]
          }],
          "filters": [{
              "member": "shop_metrics.shop_currency",
              "operator": "set"
          }, {
              "member": "shop_metrics.shop_id",
              "operator": "contains",
              "values": shop_ids
          }]
      });
      let resultData = resultSet.tablePivot();
      return resultData;
  } catch (error) {
      logger.error("Error in fetching shop metrics", error);
      return [];
  }
}

function parsedFacebookMetrics(rows) {
  let rowMap = {}
  for (var k in rows) {
      let r  = rows[k]
      rowMap[r["ProcessedAdsInsights.accountId"]] = {
          fb_spend : Math.round(parseFloat(r["ProcessedAdsInsights.spendSum"] ?? 0)),
          fb_spend_formatted : util.moneyFormatter(r["ProcessedAdsInsights.accountCurrency"])(Math.round(parseFloat(r["ProcessedAdsInsights.spendSum"] ?? 0))),
      }
  }

  return rowMap
}

function parsedShopMetrics(rows) {
  let rowMap = {}

  for (var k in rows) {
      let r = rows[k]
      let formatter = util.moneyFormatter(r["shop_metrics.shop_currency"] ?? "")

      rowMap[r["shop_metrics.shop_id"]] = {
          shop_id : r["shop_metrics.shop_id"] ?? "",
          shop_currency : r["shop_metrics.shop_currency"] ?? "",
          total_price : Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0)),
          total_price_formatted : formatter(Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0))),
          order_count : parseInt(r["shop_metrics.order_count"] ?? 0),
          order_count_formatted : numeral(r["shop_metrics.order_count"] ?? "0").format("0,0"),
          new_cust_count : parseInt(r["shop_metrics.new_cust_count"] ?? 0),
          new_cust_count_formatted : numeral(r["shop_metrics.new_cust_count"] ?? "0").format("0,0"),
          returning_cust_count : parseInt(r["shop_metrics.returning_cust_count"] ?? 0),
          returning_cust_count_formatted : numeral(r["shop_metrics.returning_cust_count"] ?? "0").format("0,0"),
          aov : Math.round(parseFloat(r["shop_metrics.aov"] ?? 0)),
          aov_formatted : formatter(Math.round(parseFloat(r["shop_metrics.aov"] ?? 0)))
      }
  }
  return rowMap
}

async function prepareWeeklyReport(recipients) {
  // getting week as per IST time
  let curr_start_date = dayjs.utc().utcOffset(330).subtract(1, "week").startOf("isoWeek").toDate();
  let curr_end_date =  dayjs.utc().utcOffset(330).subtract(1, "week").endOf("isoWeek").toDate();
  let prev_start_date = dayjs.utc().utcOffset(330).subtract(2, "week").startOf("isoWeek").toDate();
  let prev_end_date =  dayjs.utc().utcOffset(330).subtract(2, "week").endOf("isoWeek").toDate();

  let shopIds = recipients.map(r => (r.shop_id + ""));
  let account_ids = recipients.filter(r => !!r.source_config_id)

  // Split shopIds into two batches - 1. ShopIds with useMigrationDataset true 2. ShopIds with useMigrationDataset false
  let shopIdsOSS = [];
  let shopIdsNonOSS = [];
  for (let i = 0; i < shopIds.length; i++) {
      let shopId = shopIds[i];
      let useMigrationDataset = await shop.shouldUseMigrationDataset(shopId);
      if (useMigrationDataset) {
        shopIdsOSS.push(shopId);
      } else {
        shopIdsNonOSS.push(shopId);
      }
  }

  // Make shopMetrics calls concurrent
  let [currDataNonOSS, prevDataNonOSS, currDataOSS, prevDataOSS, currDataFB, prevDataFB] = await Promise.all([
      shopMetrics(shopIdsNonOSS, curr_start_date, curr_end_date, false),
      shopMetrics(shopIdsNonOSS, prev_start_date, prev_end_date, false),
      shopMetrics(shopIdsOSS, curr_start_date, curr_end_date, true),
      shopMetrics(shopIdsOSS, prev_start_date, prev_end_date, true),
      facebookMetrics(account_ids, curr_start_date, curr_end_date),
      facebookMetrics(account_ids, prev_start_date, prev_end_date)
  ]);

  currDataNonOSS = parsedShopMetrics(currDataNonOSS);
  prevDataNonOSS = parsedShopMetrics(prevDataNonOSS);
  currDataOSS = parsedShopMetrics(currDataOSS);
  prevDataOSS = parsedShopMetrics(prevDataOSS);
  currDataFB = parsedFacebookMetrics(currDataFB);
  prevDataFB = parsedFacebookMetrics(prevDataFB);

  let currData = {...currDataNonOSS, ...currDataOSS};
  let prevData = {...prevDataNonOSS, ...prevDataOSS};

  let content = {}
  for (var k in recipients) {
      let shop_id = recipients[k].shop_id
      if (!currData[shop_id] || !prevData[shop_id]) {
          logger.error("Data missing for shop", recipients[k])
          continue;
      }

      let fbData = {
          fb_spend_formatted : "Not connected",
          fb_spend_diff : 0
      }

      let source_config_id = recipients[k].source_config_id
      if (!!source_config_id && currDataFB[source_config_id]) {
          fbData = {
              fb_spend_formatted : currDataFB[source_config_id].fb_spend_formatted,
              fb_spend_diff : prevDataFB[source_config_id] && prevDataFB[source_config_id].fb_spend > 0
                  ? Math.round((currDataFB[source_config_id].fb_spend - prevDataFB[source_config_id].fb_spend) * 100 / prevDataFB[source_config_id].fb_spend)
                  : 0
          }
      }

      content[shop_id] = {
          week_start: dayjs(curr_start_date).format("MMM DD, YYYY"),
          week_end: dayjs(curr_end_date).format("MMM DD, YYYY"),
          week_str_short : dayjs(curr_start_date).format("MMM DD") + " - " + dayjs(curr_end_date).format("MMM DD, YYYY"),
          prev_week_start: dayjs(prev_start_date).format("MMM DD, YYYY"),
          prev_week_end: dayjs(prev_end_date).format("MMM DD, YYYY"),
          ...fbData,
          ...recipients[k],
          ...currData[shop_id],
          // find diff in percentages
          total_price_diff : prevData[shop_id].total_price > 0 ? Math.round((currData[shop_id].total_price - prevData[shop_id].total_price) * 100 / prevData[shop_id].total_price) : 0,
          new_cust_count_diff : prevData[shop_id].new_cust_count > 0 ? Math.round((currData[shop_id].new_cust_count - prevData[shop_id].new_cust_count) * 100 / prevData[shop_id].new_cust_count) : 0,
          returning_cust_count_diff : prevData[shop_id].returning_cust_count > 0 ? Math.round((currData[shop_id].returning_cust_count - prevData[shop_id].returning_cust_count) * 100 / prevData[shop_id].returning_cust_count) : 0,
          order_count_diff : prevData[shop_id].order_count > 0 ? Math.round((currData[shop_id].order_count - prevData[shop_id].order_count) * 100 / prevData[shop_id].order_count) : 0,
          aov_diff : prevData[shop_id].aov > 0 ? Math.round((currData[shop_id].aov - prevData[shop_id].aov) * 100 / prevData[shop_id].aov) : 0
      }
  }

  return content;
}

export { fetchRFMSegmentCustomers, prepareWeeklyReport, rfmSegmentExport};
