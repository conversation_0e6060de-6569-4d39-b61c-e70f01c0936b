import cubejs from '@cubejs-client/core';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
const jwt = require('jsonwebtoken');
import {DBT_PROD_DATASET_OSS} from '../dbt/index.js';

export const cubejsInstance = function (shop_id, cube = "Shops", useMigrationDataset = false) {

    // Switching between two cubejs instances
    const urls = [
        'https://rest.datadrew.io/cubejs-api/v1',
        'https://emerald-narwhal.gcp-us-central1.cubecloudapp.dev/cubejs-api/v1'
    ];

    const randomIndex = Math.floor(Math.random() * urls.length);
    const apiUrl =  urls[randomIndex];

    return cubejs.default(
        () => {

            let payload = {
                shop_id,
                cube,
            }

            if (useMigrationDataset) {
                payload.overrideDataset = DBT_PROD_DATASET_OSS
            }

            return jwt.sign(payload, process.env.CUBEJS_API_SECRET, { expiresIn: '1d'})
        },
        { apiUrl: apiUrl }
    );
}


// cubejsAdminInstance - only available on backend
// with admin role - shop_id filter is not automatically added in each query
// so need explicity shop_ids in dimensions and filters etc.
export const cubejsAdminInstance = function (useMigrationDataset = false) {

    // Switching between two cubejs instances
    const urls = [
        'https://rest.datadrew.io/cubejs-api/v1',
        'https://emerald-narwhal.gcp-us-central1.cubecloudapp.dev/cubejs-api/v1'
    ];

    const randomIndex = Math.floor(Math.random() * urls.length);
    const apiUrl =  urls[randomIndex];

    return cubejs.default(
        () => {

            let payload = {role: "admin"}
            if (useMigrationDataset) {
                payload.overrideDataset = DBT_PROD_DATASET_OSS
            }

            return jwt.sign(payload, process.env.CUBEJS_API_SECRET, { expiresIn: '1d'})
        },
        { apiUrl: apiUrl }
    );
}