import logger from "./logger.js";
import mysql from './database.js';

export default class TokenService {
    constructor(shop_id) {
        this.shop_id = shop_id;
    }

    async getToken() {
        try {
            let existingToken = await mysql.query('SELECT access_token FROM access_tokens WHERE shop_id = ?', [this.shop_id + '']);
            // logger.info('shop.getToken', existingToken)
            if (existingToken.length > 0) {
                return existingToken[0].access_token;
            } else {
                return null;
            }
        } catch (err) {
            logger.error('error in getToken ', err);
            return null
        }
    }

    async saveToken(domain, accessToken, scope) {
        const connection = await mysql.connection();
        try {
            let existingToken = await connection.query('SELECT access_token FROM access_tokens WHERE shop_id = ?', [this.shop_id + '']);
            logger.info('shop.saveToken', existingToken)
            if (existingToken.length > 0) {
                await connection.query("UPDATE access_tokens SET domain=?, access_token=?, scope=? WHERE shop_id=?", [
                    domain,
                    accessToken,
                    scope,
                    this.shop_id + ''
                ]);
            } else {
                await connection.query("INSERT INTO access_tokens (shop_id, domain, access_token, scope) VALUES (?, ?, ?, ?)", [
                    this.shop_id + '',
                    domain,
                    accessToken,
                    scope
                ]);
            }
            return true;
        } catch (err) {
            logger.error('error in saveToken ', err);
            return false
        } finally {
            await connection.release();
        }
    }
}