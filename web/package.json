{"name": "shopify-app-template-node", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "tsup react-email-src/index.js --format esm,cjs --dts --external react && cross-env NODE_ENV=development nodemon --experimental-specifier-resolution=node index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node --experimental-specifier-resolution=node -r newrelic index.js", "build": "tsup react-email-src/index.js --format esm,cjs --dts --external react", "worker": "cross-env NODE_ENV=production node --experimental-specifier-resolution=node -r newrelic worker.js", "emaildev": "email dev --dir react-email-src/emails --port 3001", "lint": "TIMING=1 eslint react-email-src/**/*.{ts,tsx}* --fix", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "format:check": "prettier --check \"**/*.{ts,tsx}\"", "format": "prettier --write \"**/*.{ts,tsx}\""}, "type": "module", "engines": {"node": ">=14.13.1"}, "dependencies": {"@aws-sdk/client-sesv2": "^3.592.0", "@cubejs-client/core": "^0.31.46", "@google-cloud/bigquery": "^7.3.0", "@google-cloud/bigquery-data-transfer": "^4.3.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/run": "^1.4.0", "@google-cloud/tasks": "^3.0.5", "@langchain/langgraph-sdk": "^0.0.45", "@newrelic/native-metrics": "^10.2.0", "@react-email/components": "^0.0.7", "@react-email/render": "^0.0.7", "@sendgrid/mail": "^7.7.0", "@shopify/shopify-app-express": "^5.0.9", "@shopify/shopify-app-session-storage-redis": "^4.2.2", "api": "^6.1.2", "axios": "^1.2.4", "compression": "^1.7.4", "cron-parser": "^4.9.0", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dayjs": "^1.11.7", "dinero.js": "^1.9.1", "dotenv": "^16.0.3", "firebase-admin": "^11.5.0", "google-ads-api": "^16.0.0-rest-beta3", "google-auth-library": "^9.11.0", "googleapis": "^140.0.0", "graphql": "^16.6.0", "intercom-client": "^4.0.0", "ioredis": "^5.2.6", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.0", "mixpanel": "^0.17.0", "mysql": "^2.18.1", "newrelic": "^11.22.0", "numeral": "^2.0.6", "react": "^18.2.0", "react-email": "^2.1.4", "react-icons": "^4.11.0", "serve-static": "^1.14.1", "shopify-api-node": "^3.14.1", "tailwindcss-animate": "^1.0.7", "unleash-client": "^5.5.5", "winston": "^3.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.22", "eslint": "8.23.1", "eslint-config-custom": "*", "nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3", "tsconfig": "*", "tsup": "6.2.3", "typescript": "4.8.3"}}