// @ts-check
import express from "express";
import dotenv from 'dotenv';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "./services/logger.js";
import automation from "./services/automation/index.js";
import dbt from "./services/dbt/index.js";

dotenv.config();
import shopify from "./shopify.js";
import shop from './services/shop.js';
import tracker from './services/tracker.js';
const jwt = require('jsonwebtoken');
import admin from 'firebase-admin';
import GDPRWebhookHandlers from "./gdpr.js";
import integrations from "./services/airbyte/integrations.js";

if (process.env.NODE_ENV !== 'production') {
    var serviceAccount = require("./shopify-301218-firebase-adminsdk-w8pxp-42fc438efb.json");
    admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
    });
} else {
    admin.initializeApp();
}

const PORT = parseInt(process.env.BACKEND_PORT || process.env.PORT, 10);

const app = express();


app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: GDPRWebhookHandlers })
);

// userAuthMiddleware()  is used where only user session is required - otherwise 401
// userAuthMiddleware(true) is used where user session is optional - but then shopify session is required - otherwise 401
let userAuthMiddleware = function (optional = false) {
  return async function (req, res, next) {
    // idToken is JWT token that comes from the client app
    let idToken = req.headers["AuthIdToken"] ?? req.headers["authidtoken"] ?? req.headers["Authidtoken"];

    // custom auth token is JWT token that are used for all non-user invocations (ex. from cloud scheduler)
    const customAuthToken = req.headers["CustomAuthToken"] ?? req.headers["customauthtoken"];

    if (!idToken && !customAuthToken) {
      if (optional) {
        return next();
      } else {
        return res.status(401).send('Unauthorized');
      }
    }

    try {
      if (idToken) {
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        if (decodedToken) {
          res.locals.user = decodedToken;
        }
      } else if (customAuthToken) {
        const decodedToken = jwt.verify(customAuthToken, process.env.CUSTOM_JWT_SECRET, {'algorithm':'HS256'});
        if (decodedToken) {
          res.locals.user = decodedToken;
        }
      }
    } catch (err) {
      logger.error(`userAuth error - ${err}`, {headers : req.headers})
      if (optional) {
        return await next();
      } else {
        return res.status(401).send('Unauthorized');
      }
    }

    await next();
  }
}

// All endpoints /api/* will require user or shopify session
// except /api/user/* & some other will require user session only
app.use(express.json());


// Webhook endpoint - https://worker.datadrew.io/api/airbyte/webhook?token=airbyte_secret_2024
app.post('/api/airbyte/webhook', async (req, res) => {
  const payload = req.body;
  // console.log(req)

  const token = req.query.token;

  // Validate the webhook secret
  if (token !== "airbyte_secret_2024") {
      logger.warn('Invalid webhook secret');
      return res.status(403).send('Forbidden');
  }

  // Log the incoming payload
  logger.info('api/airbyte/webhook: Webhook received:', payload);

  // Check if the sync was successful
  if (payload && payload.data) {
      const connectionId = payload.data?.connection?.id;
      const success = payload.data?.success;
      const connectionName = payload.data?.connection.name;
      const recordsExtracted = payload.data?.recordsEmitted;
      const recordsLoaded = payload.data?.recordsCommitted;
      const duration = payload.data?.durationFormatted;

      logger.info(`api/airbyte/webhook: Sync: ${connectionId} : ${connectionName}`);
      logger.info(`api/airbyte/webhook: Sync status: ${success}`);
      logger.info(`api/airbyte/webhook: Records extracted: ${recordsExtracted}, Records loaded: ${recordsLoaded}`);
      logger.info(`api/airbyte/webhook: Duration: ${duration}`);

      await integrations.handleConnectionWebhook(connectionId, success);
      res.status(200).send('Ok');
  } else {
      logger.error('api/airbyte/webhook: Sync was not successful or payload is missing data.');
      res.status(200).send('Ok');
  }
});


// manual run of automation job
// Cloud Scheduler will call this endpoint to run the job_type automation
app.post('/api/automation/run', userAuthMiddleware(), async (req, res) => {

  if (res.locals.user.uid !== "e6L8KNCWX3fU2G9KdWabOiwQgRq1") {
    return res.sendStatus(401);
  }

  const { job_id, job_type } = req.body;
  if (!job_id && !job_type) {
    res.json({error: "No Job provided"});
    return ;
  }

  if (!!job_id) {
    let response = await automation.run({job_id});
    res.json(response);
    return;
  }

  if (!!job_type) {
    let response = await automation.runJobType(job_type);
    res.json(response);
    return;
  }

  res.json({error: "Invalid Job Type"});
  return;
});

app.post("/api/dbt", userAuthMiddleware(), async (req, res) => {
  if (res.locals.user.uid !== "e6L8KNCWX3fU2G9KdWabOiwQgRq1") {
    return res.sendStatus(401);
  }

  if (!req.body.commands || !Array.isArray(req.body.commands)) {
    return res.status(400).send("Invalid request");
  }

  try {
    const results = await Promise.all(req.body.commands.map(async (command) => {
      try {
        let response = await dbt(command);
        logger.info(`dbt command: ${command}`, response);
        return { command, response };
      } catch (error) {
        logger.error(`Error initiating dbt command: ${command}`, error);
        return { command, error: error.message };
      }
    }));

    res.json({ message: "Commands initiated", results });
  } catch (error) {
    logger.error("Error initiating dbt commands", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});


app.post("/api/metricsync", userAuthMiddleware(), async (req, res) => {
  if (res.locals.user.uid !== "e6L8KNCWX3fU2G9KdWabOiwQgRq1") {
    return res.sendStatus(401);
  }

  let updateIntercom = 'updateIntercom' in req.body ? req.body.updateIntercom : true

  switch (req.body.source) {
    case "cube":
      await tracker.syncCubeMetrics(updateIntercom);
      break;
    case "db":
      await tracker.syncDBMetrics(updateIntercom);
      break;
    default:
      await tracker.syncDBMetrics(updateIntercom);
      await tracker.syncCubeMetrics(updateIntercom);
  }

  return res.sendStatus(200);
});

app.post("/api/async-push", userAuthMiddleware(), async (req, res) => {
  if (res.locals.user.uid !== "e6L8KNCWX3fU2G9KdWabOiwQgRq1") {
    return res.sendStatus(401);
  }

  try {
    let response = await shop.processAsyncTask(req.body)
    if (!response) {
      return res.sendStatus(500)
    }
    return res.sendStatus(200)
  } catch (error) {
    logger.error("Error in /api/async-push", error)
    return res.sendStatus(500)
  }
});

app.get('*', (req, res) => {
    res.send('OK').end();
});

app.listen(PORT);
