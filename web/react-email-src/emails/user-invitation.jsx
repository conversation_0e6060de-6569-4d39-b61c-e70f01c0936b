import {
    <PERSON>,
    <PERSON><PERSON>,
    Container,
    Head,
    Hr,
    Html,
    Img,
    Link,
    Heading,
    Preview,
    Section,
    Text,
} from '@react-email/components';
import * as React from 'react';

const baseUrl = "https://storage.googleapis.com/datadrew-public";

export const InviteToDatadrew = (props) => {

    const { inviteLink } = props;

    return (
        <Html>
            <Head />
            <Preview>You're invited to collaborate on Datadrew Analytics</Preview>
            <Body style={main}>
                <Container style={container}>
                    <Section style={box}>
                        <Container key="logo">
                            <Img
                                alt="DataDrew Analytics"
                                style={{ width: "250px", height: "68px", margin: "auto" }}
                                src={`${baseUrl}/dd-logo-full.png`}
                                width="49"
                                height="21"
                            />
                        </Container>
                        <Hr style={hr} />
                        <Container style={{ width: "100%" }} key="heading">
                            <Heading as="h1" style={{ color: "white", textAlign: 'center' }}>
                                Join Datadrew Analytics
                            </Heading>
                            <Text style={{ color: "#a0a3bd", fontSize: "18px", textAlign: 'center' }}>
                                You've been invited to collaborate on Datadrew Analytics. Click below to accept the invitation and start working together!
                            </Text>
                        </Container>

                        <Section style={section} key="invite-button">
                            <Button pX={10} pY={20} style={button} href={inviteLink}>
                                🚀 Accept Invitation
                            </Button>
                        </Section>

                        <Hr style={hr} />
                        <Text style={footer}>
                            Copyright © 2025 DataDrew, All rights reserved.
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};

export default InviteToDatadrew;

const main = {
    backgroundColor: 'transparent',
    fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'
};

const container = {
    backgroundColor: '#151b37',
    margin: '0 auto',
    padding: '20px 0 48px',
    marginBottom: '64px',
};

const box = {
    padding: '0 48px',
};

const hr = {
    borderColor: '#e6ebf1',
    margin: '20px 0',
};

const section = {
    padding: '24px',
    borderRadius: '5px',
    textAlign: 'center',
};

const button = {
    backgroundColor: '#095bc6',
    borderRadius: '5px',
    color: '#fff',
    fontSize: '16px',
    fontWeight: 'normal',
    textDecoration: 'none',
    padding: '10px 20px',
    display: 'inline-block',
};

const footer = {
    color: '#8898aa',
    fontSize: '12px',
    textAlign: "center",
    lineHeight: '16px',
};
