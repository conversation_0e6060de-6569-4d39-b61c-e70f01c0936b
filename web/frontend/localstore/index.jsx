import { useState } from 'react';

export const SELECTED_SHOP_KEY = 'selectedShop';
export const SELECTED_WORKSPACE_ID_KEY = 'selectedWorkspaceId';

// hook for using inside react components
export const useLocalStorage = (key, initialValue) => {
  // Get from local storage then parse stored json or return initialValue
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error('Error reading localStorage key "' + key + '": ', error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage.
  const setValue = value => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to local storage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error('Error setting localStorage key "' + key + '": ', error);
    }
  };

  return [storedValue, setValue];
};

// function to get the value from local storage in regular js functions
export const getLocalStorageValue = (key) => {
  try {
    const value = localStorage.getItem(key);
    if (value === null) return null;
    
    // Try to parse the value as JSON
    try {
      return JSON.parse(value);
    } catch {
      // If parsing fails, return the raw value
      return value;
    }
  } catch (error) {
    console.error(`Error reading localStorage key "${key}":`, error);
    return null;
  }
};

// function to set the value in local storage in regular js functions
export const setLocalStorageValue = (key, value) => {
  try {
    const valueToStore = JSON.stringify(value);
    localStorage.setItem(key, valueToStore);
  } catch (error) {
    console.error(`Error setting localStorage key "${key}":`, error);
  }
};

export default { useLocalStorage, getLocalStorageValue, setLocalStorageValue };
