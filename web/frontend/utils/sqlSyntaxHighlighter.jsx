/**
 * SQL Syntax Highlighter
 * 
 * A lightweight utility to add syntax highlighting to SQL queries without external dependencies.
 * 
 * Usage:
 * import { highlightSQL, sqlHighlightStyles } from "@/utils/sqlSyntaxHighlighter";
 * 
 * // In your component:
 * <div 
 *   style={sqlHighlightStyles.container}
 *   dangerouslySetInnerHTML={{ __html: highlightSQL(sqlQuery) }}
 * />
 */

/**
 * Highlights SQL syntax using CSS classes
 * @param {string} sql - The SQL query to highlight
 * @returns {string} - HTML string with syntax highlighting classes applied
 */
export const highlightSQL = (sql) => {
  if (!sql) return '';
  
  // Replace SQL keywords with styled spans
  const formattedSQL = sql
    // Keywords (SELECT, FROM, WHERE, etc.)
    .replace(/\b(SELECT|FROM|WHERE|JOIN|ON|AND|OR|AS|IN|BETWEEN|INNER|LEFT|RIGHT|OUTER|ORDER BY|GROUP BY|HAVING|LIMIT|OFFSET|UNION|ALL|DISTINCT|COUNT|SUM|AVG|MIN|MAX|ROUND|DATE|INTERVAL|CASE|WHEN|THEN|ELSE|END|IS|NULL|NOT|TRUE|FALSE)\b/gi, 
      match => `<span class="sql-keyword">${match}</span>`)
    // Functions and operators
    .replace(/\b(AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MIN|MID|NOW|ROUND|SUM|UCASE)\s*\(/gi,
      match => `<span class="sql-function">${match}</span>`)
    // Strings
    .replace(/'([^']*)'/g, 
      match => `<span class="sql-string">${match}</span>`)
    // Numbers
    .replace(/\b(\d+(\.\d+)?)\b/g,
      match => `<span class="sql-number">${match}</span>`)
    // Comments
    .replace(/--(.*)$/gm,
      match => `<span class="sql-comment">${match}</span>`);
  
  return formattedSQL;
};

/**
 * MUI style objects for SQL syntax highlighting
 */
export const sqlHighlightStyles = {
  container: {
    backgroundColor: "#282c34", // Dark code background
    padding: "16px",
    borderRadius: "8px",
    border: "1px solid rgba(0,0,0,0.12)",
    color: "#abb2bf", // Light text for dark background
    fontSize: "0.875rem",
    fontFamily: "'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace",
    whiteSpace: "pre-wrap",
    wordBreak: "break-word",
    lineHeight: 1.6,
    maxHeight: "400px",
    overflowY: "auto",
  },
  
  // These can be used with the sx prop in Material-UI components
  sxStyles: {
    "& .sql-keyword": {
      color: "#c678dd", // Purple for keywords
      fontWeight: "bold"
    },
    "& .sql-function": {
      color: "#61afef" // Blue for functions
    },
    "& .sql-string": {
      color: "#98c379" // Green for strings
    },
    "& .sql-number": {
      color: "#d19a66" // Orange for numbers
    },
    "& .sql-comment": {
      color: "#7f848e", // Grey for comments
      fontStyle: "italic"
    }
  }
};

/**
 * Alternative one-dark theme color palette for customization
 */
export const oneDarkPalette = {
  background: "#282c34",
  foreground: "#abb2bf",
  keyword: "#c678dd",
  function: "#61afef",
  string: "#98c379", 
  number: "#d19a66",
  comment: "#7f848e"
};

// Export a reusable React component if needed in the future 