import { app } from "@/firebase-config";
import { getAuth, signOut } from 'firebase/auth';
import { setLocalStorageValue, SELECTED_SHOP_KEY, SELECTED_WORKSPACE_ID_KEY } from "@/localstore";
import { axiosInstance } from "@/context";

// Helper function to delete a cookie by name
const deleteCookie = (name) => {
  document.cookie = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
  document.cookie = `${name}.sig=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
};

// Function to handle shop flow logout
const shopFlowLogout = () => {
  // Clear the selected shop from localStorage
  setLocalStorageValue(SELECTED_SHOP_KEY, null);

  // Delete Shopify session cookies
  deleteCookie('shopify_app_session');
  deleteCookie('shopify_app_state');

  // Redirect to Shopify admin
  window.location.href = '/sign-in';
};

// Main sign out function that handles both user flow and shop flow
export const signOutUser = (navigate, isShopFlow = false) => {
  if (isShopFlow) {
    shopFlowLogout();
    return;
  }

  // User flow logout
  setLocalStorageValue(SELECTED_SHOP_KEY, null);
  setLocalStorageValue(SELECTED_WORKSPACE_ID_KEY, null);
  const auth = getAuth(app);

  signOut(auth)
    .then(() => {
      navigate('/sign-in', { replace: true });
    })
    .catch((error) => {
      console.error('Error signing out:', error);
    });
};