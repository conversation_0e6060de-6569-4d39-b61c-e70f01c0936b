/** 
  All of the routes for the Material Dashboard 2 PRO React are added here,
  You can add a new route, customize the routes and delete the routes here.

  Once you add a new route on this file it will be visible automatically on
  the Sidenav.

  For adding a new route you can follow the existing routes in the routes array.
  1. The `type` key with the `collapse` value is used for a route.
  2. The `type` key with the `title` value is used for a title inside the Sidenav. 
  3. The `type` key with the `divider` value is used for a divider between Sidenav items.
  4. The `name` key is used for the name of the route on the Sidenav.
  5. The `key` key is used for the key of the route (It will help you with the key prop inside a loop).
  6. The `icon` key is used for the icon of the route on the Sidenav, you have to add a node.
  7. The `collapse` key is used for making a collapsible item on the Sidenav that contains other routes
  inside (nested routes), you need to pass the nested routes inside an array as a value for the `collapse` key.
  8. The `route` key is used to store the route location which is used for the react router.
  9. The `href` key is used to store the external links location.
  10. The `title` key is only for the item with the type of `title` and its used for the title text on the Sidenav.
  10. The `component` key is used to store the component of its route.
*/

// Material Dashboard 2 PRO React layouts
import FacebookAds from "@/layouts/integrations/facebook";
import MetricDashboard from "@/layouts/dashboards/metrics"
import IntegrationsDashboard from "@/layouts/integrations";
import CohortAnalysis from "@/layouts/lifetimevalue/cohortanalysis";
import CohortFrequency from "@/layouts/lifetimevalue/cohortfrequency";
import RepurchaseRate from "@/layouts/product/repurchaserate";
import CustomerSegments from "@/layouts/customer/customersegments";
import NewVsExistingCustomers from "@/layouts/customer/newvsexisting";
import CustomerRFM from "@/layouts/customer/rfm";
import CartAnalysis from "@/layouts/product/cartanalysis";
import Benchmarks from "@/layouts/benchmarks";
import AskPrashna from "@/layouts/askprashna";
import MDBadge from "@/components/MDBadge";
import FeatureRequests from "@/layouts/featurerequests";
import BlendedAds from "@/layouts/ads/blended";
import {GoogleAds, GoogleAnalytics} from "@/layouts/integrations/google";
// import Analytics from "@/layouts/dashboards/analytics";
// import Sales from "@/layouts/dashboards/sales";
// import ProfileOverview from "@/layouts/pages/profile/profile-overview";
// import AllProjects from "@/layouts/pages/profile/all-projects";
// import NewUser from "@/layouts/pages/users/new-user";
import Settings from "@/layouts/pages/account/settings";
import UserSettings from "@/layouts/pages/account/settings/UserSettings";
// import Billing from "@/layouts/pages/account/billing";
// import Invoice from "@/layouts/pages/account/invoice";
// import Timeline from "@/layouts/pages/projects/timeline";
// import PricingPage from "@/layouts/pages/pricing-page";
// import Widgets from "@/layouts/pages/widgets";
// import RTL from "@/layouts/pages/rtl";
// import Charts from "@/layouts/pages/charts";
// import Notifications from "@/layouts/pages/notifications";
// import Kanban from "@/layouts/applications/kanban";
// import Wizard from "@/layouts/applications/wizard";
// import DataTables from "@/layouts/applications/data-tables";
// import Calendar from "@/layouts/applications/calendar";
// import NewProduct from "@/layouts/ecommerce/products/new-product";
// import EditProduct from "@/layouts/ecommerce/products/edit-product";
// import ProductPage from "@/layouts/ecommerce/products/product-page";
// import OrderList from "@/layouts/ecommerce/orders/order-list";
// import OrderDetails from "@/layouts/ecommerce/orders/order-details";
// import SignInBasic from "@/layouts/authentication/sign-in/basic";
// import SignInCover from "@/layouts/authentication/sign-in/cover";
// import SignInIllustration from "@/layouts/authentication/sign-in/illustration";
// import SignUpCover from "@/layouts/authentication/sign-up/cover";
// import ResetCover from "@/layouts/authentication/reset-password/cover";

// Material Dashboard 2 PRO React components
// import MDAvatar from "@/components/MDAvatar";

// @mui icons
import SettingsIcon from '@mui/icons-material/Settings';
import StackedBarChart from "@mui/icons-material/StackedBarChart";
import ImportantDevices from '@mui/icons-material/ImportantDevices';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SupervisedUserCircle from '@mui/icons-material/SupervisedUserCircle';
import AssistantIcon from '@mui/icons-material/Assistant';
// import GoogleIcon from '@mui/icons-material/Google';
import Insights from '@mui/icons-material/Insights';
import ShoppingBasketIcon from '@mui/icons-material/ShoppingBasket';
import Hub from '@mui/icons-material/Hub';
import WebIcon from '@mui/icons-material/Web';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';

import { useTranslation } from 'react-i18next';
import WorkspaceDashboard from "@/layouts/workspaces";

const GoogleAdsIconBasic = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    width="24px"
    height="24px"
    viewBox="0 0 24 24">
    <path fill="#ffffff" d="M11.14 14.42L10 16.36c-.56-2.03-2.43-3.52-4.64-3.52-.37 0-.74.04-1.08.13l3.09-5.24C7.48 8.1 7.63 8.47 7.83 8.82L11.14 14.42zM5.36 14.344000000000001A3.328 3.328 0 1 0 5.36 21 3.328 3.328 0 1 0 5.36 14.344000000000001zM15.79 19.336c.919 1.592 2.994 2.122 4.546 1.218 1.589-.925 2.137-2.954 1.218-4.546L14.898 4.744c-.919-1.592-2.988-2.122-4.546-1.218C8.762 4.448 8.215 6.48 9.134 8.072L15.79 19.336z"></path>
  </svg>
);


const GoogleAdsIconColor = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    width="32px"
    height="32px"
    viewBox="0 0 50 50">
    <polygon fill="#ffc107" points="30.129,15.75 18.871,9.25 5.871,31.25 17.129,37.75"></polygon><path fill="#1e88e5" d="M31.871,37.75c1.795,3.109,5.847,4.144,8.879,2.379c3.103-1.806,4.174-5.77,2.379-8.879l-13-22 c-1.795-3.109-5.835-4.144-8.879-2.379c-3.106,1.801-4.174,5.77-2.379,8.879L31.871,37.75z"></path><circle cx="11.5" cy="34.5" r="6.5" fill="#43a047"></circle>
  </svg>
);


const NewBadge = () => {
  const { t, i18n } = useTranslation();

  // Return null if the current language is not a variant of English
  if (!i18n.language || !i18n.language.startsWith('en')) {
    return null;
  }

  
  return <MDBadge badgeContent={t("new")} color="primary" container size="xs"/>
}

const BetaBadge = () => {
  const { t, i18n } = useTranslation();
  return <MDBadge indicator={false} badgeContent={`${t("beta")}`} color="secondary" container size="xs" variant="gradient" />
}

const routes = [
  // {
  //   type: "collapse",
  //   name: "Brooklyn Alice",
  //   key: "brooklyn-alice",
  //   icon: <MDAvatar src={profilePicture} alt="Brooklyn Alice" size="sm" />,
  //   collapse: [
  //     {
  //       name: "My Profile",
  //       key: "my-profile",
  //       route: "/pages/profile/profile-overview",
  //       component: <ProfileOverview />,
  //     },
  //     {
  //       name: "Settings",
  //       key: "profile-settings",
  //       route: "/pages/account/settings",
  //       component: <Settings />,
  //     },
  //     {
  //       name: "Logout",
  //       key: "logout",
  //       route: "/authentication/sign-in/basic",
  //       component: <SignInBasic />,
  //     },
  //   ],
  // },
  {
    type: "profile-collapse",
    stick: "top",
    key: "profile-collapse"
  },
  { type: "divider", stick: "top", key: "divider-1" },
  {
    type: "collapse",
    name: <>Dashboard</>,
    stick: "top",
    key: "dashboard",
    icon: <DashboardIcon fontSize="small"/>,
    hoverIcon : <DashboardIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    route: "/dashboard",
    component: <MetricDashboard />,
    noCollapse: true
  },
  {
    type: "collapse",
    name: <>Ask Prashna</>,
    stick: "top",
    feature: "prashna_ai",
    key: "ask-prashna-ai",
    icon: <AssistantIcon fontSize="small"/>,
    hoverIcon : <AssistantIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    route: "/ask-prashna-ai",
    component: <AskPrashna />,
    noCollapse: true
  },
  {
    type: "collapse",
    name: <>Manage Workspace</>,
    stick: "none",
    key: "workspace-settings",
    icon: <SettingsIcon fontSize="small"/>,
    hoverIcon : <SettingsIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    route: "/workspace-settings",
    component: <WorkspaceDashboard />,  
    noCollapse: true,
  },
  {
    type: "collapse",
    name: "Cohort Analysis",
    stick: "top",
    key: "cohort-analysis",
    icon: <StackedBarChart size="small" className="rotated" />,
    hoverIcon : <StackedBarChart fontSize="medium" className="rotated" sx={{fill: `url(#iconGradient)` }} />,
    collapse: [
      {
        name : "LTV Cohorts",
        key : "ltv-cohorts",
        route : "/cohort-analysis/ltv-cohorts",
        component : <CohortAnalysis preset="ltv-cohorts" />,
      },
      {
        name: "Product Cohorts",
        key: "product-cohorts",
        route: "/cohort-analysis/product-cohorts",
        component: <CohortAnalysis preset="product-cohorts" />,
      },
      {
        name: "Location Cohorts",
        key: "location-cohorts",
        route: "/cohort-analysis/location-cohorts",
        component: <CohortAnalysis preset="location-cohorts" />,
      },
      {
        name: "Custom Cohorts",
        key: "custom-cohorts",
        route: "/cohort-analysis/custom-cohorts",
        component: <CohortAnalysis preset="custom-cohorts" />,
      },
    ],
  },
  // {
  //   type: "collapse",
  //   name: "Lifetime Value",
  //   key: "lifetimevalue",
  //   icon: <Icon fontSize="medium" className="rotated">stacked_bar_chart</Icon>,
  //   collapse: [
  //     {
  //       name: "Cohort Analysis",
  //       key: "cohort-analysis",
  //       route: "/lifetime-value/cohort-analysis",
  //       component: <CohortAnalysis metric={"acc_total_sales_per_customer"} />,
  //     },
  //     {
  //       name: "Cohort Frequency",
  //       key: "cohort-frequency",
  //       route: "/lifetime-value/cohort-frequency",
  //       component: <CohortFrequency />,
  //     },
  //   ],
  // },
  {
    type: "collapse",
    name: "Customer",
    key: "customer",
    stick: "top",
    icon: <SupervisedUserCircle size="small" />,
    hoverIcon : <SupervisedUserCircle fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    collapse: [
      {
        name: <>RFM Segments &nbsp; <MDBadge size="xs" badgeContent="New" variant="gradient" container color="info"	/></>,
        key: "rfm-segments",
        route: "/customer/rfm-segments",
        component: <CustomerRFM />,
      },
      {
        name: "Segments",
        key: "new-vs-returning-customers",
        route: "/customer/new-vs-returning-customers",
        component: <NewVsExistingCustomers />,
      },
      {
        name: "Segments",
        key: "customer-segments",
        route: "/customer/customer-segments",
        component: <CustomerSegments />,
      },
      {
        name: "Transaction Frequency",
        key: "transaction-frequency",
        route: "/customer/transaction-frequency",
        component: <CohortFrequency />,
      }
    ],
  },
  {
    type: "collapse",
    name: "Product",
    key: "product",
    stick: "top",
    icon: <ShoppingBasketIcon fontSize="small" />,
    hoverIcon : <ShoppingBasketIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    collapse: [
      {
        name: "Basket Analysis",
        key: "basket-analysis",
        route: "/product/basket-analysis",
        component: <CartAnalysis />,
      },
      {
        name: "Repurchase Rate",
        key: "repurchase-rate",
        route: "/product/repurchase-rate",
        component: <RepurchaseRate />,
      }
    ],
  },
  {
    type: "collapse",
    name: "Ads",
    key: "ads",
    stick: "top",
    icon: <TrackChangesIcon fontSize="small"/>,
    hoverIcon : <TrackChangesIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    collapse: [
      {
        name: <>Blended summary</>,
        key: "blended-summary",
        route: "/ads/blended-summary",
        component: <BlendedAds />
      },
      {
        name: <>Google Ads</>,
        key: "google-ads",
        badge: <BetaBadge />,
        route: "/ads/google-ads",
        component: <GoogleAds />
      },
      {
        name: <>Facebook Ads</>,
        key: "facebook-ads",
        route: "/ads/facebook-ads",
        component: <FacebookAds />,
      }
    ],
  },
  {
    type: "collapse",
    name: <>Webstie Traffic</>,
    stick: "top",
    feature: "google_analytics_overview",
    key: "website-traffic",
    // badge: <BetaBadge />,
    icon: <WebIcon fontSize="small"/>,
    hoverIcon : <WebIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    route: "/website-traffic",
    component: <GoogleAnalytics />,
    noCollapse: true
  },
  // {
  //   type: "collapse",
  //   name: <>Facebook Ads</>,
  //   stick: "none",
  //   key: "facebook-ads",
  //   icon: <FacebookIcon fontSize="small"/>,
  //   // badge: <MDBadge badgeContent={"Beta"} color="info" container size="xs"/>,
  //   hoverIcon : <FacebookIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
  //   route: "/facebook-ads",
  //   component: <FacebookAds />,
  //   noCollapse: true
  // },
  // {
  //   type: "collapse",
  //   name: <>Google Ads</>,
  //   stick: "none",
  //   feature: "google_ads_overview",
  //   key: "google-ads",
  //   icon: <GoogleAdsIconBasic/>,
  //   hoverIcon : <GoogleAdsIconColor/>,
  //   route: "/google-ads",
  //   component: <GoogleAds />,
  //   noCollapse: true
  // },
  {
    type: "collapse",
    name: "Industry Benchmarks",
    stick: "top",
    key: "benchmarks",
    route: "/benchmarks",
    component: <Benchmarks />,
    icon: <Insights fontSize="small" />,
    hoverIcon : <Insights fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    noCollapse: true
  },
  // {
  //   type: "collapse",
  //   name: "Klaviyo",
  //   key: "klaviyo",
  //   route: "/klaviyo",
  //   stick: "none",
  //   component: <KlaviyoOverview />,
  //   icon: <ImportantDevices fontSize="small"/>,
  //   hoverIcon : <ImportantDevices fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
  //   noCollapse: true
  // },
  // { type: "title", title: "Pages", key: "title-pages"},
  // {
  //   type: "collapse",
  //   name: "Pages",
  //   hide: true,
  //   key: "pages",
  //   icon: <Icon fontSize="medium">image</Icon>,
  //   collapse: [
  //     {
  //       name: "Profile",
  //       key: "profile",
  //       collapse: [
  //         {
  //           name: "Profile Overview",
  //           key: "profile-overview",
  //           route: "/pages/profile/profile-overview",
  //           component: <ProfileOverview />,
  //         },
  //         {
  //           name: "All Projects",
  //           key: "all-projects",
  //           route: "/pages/profile/all-projects",
  //           component: <AllProjects />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Users",
  //       key: "users",
  //       collapse: [
  //         {
  //           name: "New User",
  //           key: "new-user",
  //           route: "/pages/users/new-user",
  //           component: <NewUser />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Account",
  //       key: "account",
  //       collapse: [
  //         {
  //           name: "Settings",
  //           key: "settings",
  //           route: "/pages/account/settings",
  //           component: <Settings />,
  //         },
  //         {
  //           name: "Billing",
  //           key: "billing",
  //           route: "/pages/account/billing",
  //           component: <Billing />,
  //         },
  //         {
  //           name: "Invoice",
  //           key: "invoice",
  //           route: "/pages/account/invoice",
  //           component: <Invoice />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Projects",
  //       key: "projects",
  //       collapse: [
  //         {
  //           name: "Timeline",
  //           key: "timeline",
  //           route: "/pages/projects/timeline",
  //           component: <Timeline />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Pricing Page",
  //       key: "pricing-page",
  //       route: "/pages/pricing-page",
  //       component: <PricingPage />,
  //     },
  //     { name: "RTL", key: "rtl", route: "/pages/rtl", component: <RTL /> },
  //     { name: "Widgets", key: "widgets", route: "/pages/widgets", component: <Widgets /> },
  //     { name: "Charts", key: "charts", route: "/pages/charts", component: <Charts /> },
  //     {
  //       name: "Notfications",
  //       key: "notifications",
  //       route: "/pages/notifications",
  //       component: <Notifications />,
  //     },
  //   ],
  // },
  // {
  //   type: "collapse",
  //   name: "Applications",
  //   key: "applications",
  //   icon: <Icon fontSize="medium">apps</Icon>,
  //   collapse: [
  //     {
  //       name: "Kanban",
  //       key: "kanban",
  //       route: "/applications/kanban",
  //       component: <Kanban />,
  //     },
  //     {
  //       name: "Wizard",
  //       key: "wizard",
  //       route: "/applications/wizard",
  //       component: <Wizard />,
  //     },
  //     {
  //       name: "Data Tables",
  //       key: "data-tables",
  //       route: "/applications/data-tables",
  //       component: <DataTables />,
  //     },
  //     {
  //       name: "Calendar",
  //       key: "calendar",
  //       route: "/applications/calendar",
  //       component: <Calendar />,
  //     },
  //   ],
  // },
  // {
  //   type: "collapse",
  //   name: "Ecommerce",
  //   hide : true,
  //   key: "ecommerce",
  //   icon: <Icon fontSize="medium">shopping_basket</Icon>,
  //   collapse: [
  //     {
  //       name: "Products",
  //       key: "products",
  //       collapse: [
  //         {
  //           name: "New Product",
  //           key: "new-product",
  //           route: "/ecommerce/products/new-product",
  //           component: <NewProduct />,
  //         },
  //         {
  //           name: "Edit Product",
  //           key: "edit-product",
  //           route: "/ecommerce/products/edit-product",
  //           component: <EditProduct />,
  //         },
  //         {
  //           name: "Product Page",
  //           key: "product-page",
  //           route: "/ecommerce/products/product-page",
  //           component: <ProductPage />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Orders",
  //       key: "orders",
  //       collapse: [
  //         {
  //           name: "Order List",
  //           key: "order-list",
  //           route: "/ecommerce/orders/order-list",
  //           component: <OrderList />,
  //         },
  //         {
  //           name: "Order Details",
  //           key: "order-details",
  //           route: "/ecommerce/orders/order-details",
  //           component: <OrderDetails />,
  //         },
  //       ],
  //     },
  //   ],
  // },
  // {
  //   type: "collapse",
  //   name: "Authentication",
  //   hide : true,
  //   key: "authentication",
  //   icon: <Icon fontSize="medium">content_paste</Icon>,
  //   collapse: [
  //     {
  //       name: "Sign In",
  //       key: "sign-in",
  //       collapse: [
  //         {
  //           name: "Basic",
  //           key: "basic",
  //           route: "/authentication/sign-in/basic",
  //           component: <SignInBasic />,
  //         },
  //         {
  //           name: "Cover",
  //           key: "cover",
  //           route: "/authentication/sign-in/cover",
  //           component: <SignInCover />,
  //         },
  //         {
  //           name: "Illustration",
  //           key: "illustration",
  //           route: "/authentication/sign-in/illustration",
  //           component: <SignInIllustration />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Sign Up",
  //       key: "sign-up",
  //       collapse: [
  //         {
  //           name: "Cover",
  //           key: "cover",
  //           route: "/authentication/sign-up/cover",
  //           component: <SignUpCover />,
  //         },
  //       ],
  //     },
  //     {
  //       name: "Reset Password",
  //       key: "reset-password",
  //       collapse: [
  //         {
  //           name: "Cover",
  //           key: "cover",
  //           route: "/authentication/reset-password/cover",
  //           component: <ResetCover />,
  //         },
  //       ],
  //     },
  //   ],
  // },
  // { type: "divider", stick: "bottom", key: "divider-1" },
  {
    type: "collapse",
    name: "Integrations",
    key: "integrations",
    stick: 'bottom',
    route: "/integrations",
    component: <IntegrationsDashboard />,
    icon: <Hub fontSize="small" />,
    hoverIcon : <Hub fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    noCollapse: true
  },
  {

    type: "collapse",
    name: "Store Settings",
    key: "Store Settings",
    route: "/settings",
    stick: "bottom",
    component: <Settings />,
    icon: <SettingsIcon fontSize="small" />,
    hoverIcon : <SettingsIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    noCollapse: true
  },
  // User settings is accessed from the user menu, not from the sidebar
  {
    type: "hidden",
    name: "User Settings",
    key: "User Settings",
    route: "/user-settings",
    component: <UserSettings />,
  },
  // {
  //   type: "profile-collapse",
  //   stick: "bottom",
  //   key: "profile-collapse"
  // },
  {
    type: "collapse",
    name: "Feature Requests",
    key: "feature-requests",
    route: "/feature-requests",
    stick: "none",
    component: <FeatureRequests />,
    icon: <ImportantDevices fontSize="small"/>,
    hoverIcon : <ImportantDevices fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
    noCollapse: true
  }
];

export default routes;
