/**
  This file is used for controlling the global states of the components,
  you can customize the states for the different components here.
*/

import { createContext, useContext, useMemo, useReducer, useState, useEffect} from "react";
import dayjs from 'dayjs';
import cubejs from '@cubejs-client/core';
import {app} from "@/firebase-config";
import { getAuth } from 'firebase/auth';
import { useTranslation } from "react-i18next";
import { getLocalStorageValue, setLocalStorageValue, SELECTED_SHOP_KEY, SELECTED_WORKSPACE_ID_KEY } from '@/localstore';
import { getCookieFromDocument } from '@/utils/common';

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// The Material Dashboard 2 PRO React main context
const MaterialUI = createContext();

import axios from "axios";
const auth = getAuth(app);

// intercept all requests on this axios instance
function addInterceptor(instance, language = "en") {
  instance.interceptors.request.use(function (config) {
      // console.log(config.url)

      config.headers["Accept-Language"] = language;
      if (!auth || !auth.currentUser) {
        return config
      }

      return auth.currentUser.getIdToken(/* forceRefresh */ true)
        .then(function(idToken) {
            // append your request headers with an authenticated token
            config.headers["AuthIdToken"] = idToken;
            return config;
        }).catch(function(error) {
            // Handle error
            console.log('firebase - getIdToken - ', error)
        });
  });
}

const axiosInstance = axios.create();
addInterceptor(axiosInstance); // default language

// Custom hook for axios instance with cancellable requests
function useCancellableAxios() {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language;

  const axiosSource = useMemo(() => axios.CancelToken.source(), []);
  const axiosIns = useMemo(() => {
      const instance = axios.create();
      instance.defaults.cancelToken = axiosSource.token;
      addInterceptor(instance, currentLanguage);
      return instance;
  }, [axiosSource, currentLanguage]);

  useEffect(() => {
      return () => {
          axiosSource.cancel('Component unmounted');
      };
  }, [axiosSource]);

  return axiosIns;
}

import mixpanel from "mixpanel-browser";
mixpanel.init("93d9fbb11310729b4c8e6f60723ae10d", {
    protocol: "https",
    ignore_dnt: true,
    opt_out_tracking_by_default: false,
});

// Set your APP_ID
const APP_ID = "gjo3rv2i";

// Loads Intercom with the snippet
// This must be run before boot, it initializes window.Intercom
// prettier-ignore
const loadIntercom = () => {
    (function () { var w = window; var ic = w.Intercom; if (typeof ic === "function") { ic('reattach_activator'); ic('update', w.intercomSettings); } else { var d = document; var i = function () { i.c(arguments); }; i.q = []; i.c = function (args) { i.q.push(args); }; w.Intercom = i; var l = function () { var s = d.createElement('script'); s.type = 'text/javascript'; s.async = true; s.src = 'https://widget.intercom.io/widget/' + APP_ID; var x = d.getElementsByTagName('script')[0]; x.parentNode.insertBefore(s, x); }; if (document.readyState === 'complete') { l(); } else if (w.attachEvent) { w.attachEvent('onload', l); } else { w.addEventListener('load', l, false); } } })();
}

// Initializes Intercom
const bootIntercom = (options = {}) => {
    window &&
        window.Intercom &&
        window.Intercom("boot", { app_id: APP_ID, ...options });
};

const updateIntercom = (options = {}) => {
    window && window.Intercom && window.Intercom("update", options);
};

const trackIntercom = (eventMsg, metadata = {}) => {
    window &&
        window.Intercom &&
        window.Intercom("trackEvent", eventMsg, metadata);
}

const showIntercom = () => {
    window && window.Intercom && window.Intercom("show");
};

const customClarityKey = (key, value) => {
  console.log(key, value)
    window && window.clarity && window.clarity("set", key, value);
};

const tracker = {
  mixpanel : mixpanel,
  intercom : {
    load : loadIntercom,
    boot : bootIntercom,
    update : updateIntercom,
    track : trackIntercom,
    show : showIntercom
  },
  clarity : {
    track : customClarityKey
  },
  event: (eventName, metadata = {}) => {
    mixpanel.track(eventName, metadata);
    trackIntercom(eventName, metadata);
  }
}


let apiTokenPromise = {};

const cubejsApi = function (shop = "", cube = "", integrationRequestId = "") {

  let queryParams = {
    cube : cube
  }

  if (!!shop) {
    queryParams.selectedShop = shop
  }

  if (!!integrationRequestId) {
    queryParams.integration_request_id = integrationRequestId
  }

  // Switching between two cubejs instances
  const urls = [
    'https://rest.datadrew.io/cubejs-api/v1',
    'https://emerald-narwhal.gcp-us-central1.cubecloudapp.dev/cubejs-api/v1'
  ];

  const randomIndex = Math.floor(Math.random() * urls.length);
  const apiUrl =  urls[randomIndex];

  return cubejs(
      () => {
        let key  = shop + cube + integrationRequestId;
        if (!(key in apiTokenPromise)) {
          apiTokenPromise[key] = axiosInstance
            .get("/api/token", {params: queryParams})
            .then((response) => {
                if (response.data) {
                    return response.data.token
                }
            })
            .catch((err) => {
                console.log("error", err);
            });
        }
        return apiTokenPromise[key];
      },
      {apiUrl : apiUrl}
  );
}



// Setting custom name for the context which is visible on react dev tools
MaterialUI.displayName = "MaterialUIContext";

// Material Dashboard 2 PRO React reducer
function reducer(state, action) {
  switch (action.type) {
    case "MINI_SIDENAV": {
      return { ...state, miniSidenav: action.value };
    }
    case "TRANSPARENT_SIDENAV": {
      return { ...state, transparentSidenav: action.value };
    }
    case "WHITE_SIDENAV": {
      return { ...state, whiteSidenav: action.value };
    }
    case "SIDENAV_COLOR": {
      return { ...state, sidenavColor: action.value };
    }
    case "TRANSPARENT_NAVBAR": {
      return { ...state, transparentNavbar: action.value };
    }
    case "FIXED_NAVBAR": {
      return { ...state, fixedNavbar: action.value };
    }
    case "OPEN_CONFIGURATOR": {
      return { ...state, openConfigurator: action.value };
    }
    case "DIRECTION": {
      return { ...state, direction: action.value };
    }
    case "LAYOUT": {
      return { ...state, layout: action.value };
    }
    case "DARKMODE": {
      return { ...state, darkMode: action.value };
    }
    case "SELECTED_SHOP" : {
      return { ...state, selectedShop: action.value };
    }
    case "SELECTED_WORKSPACE_ID" : {
      return { ...state, selectedWorkspaceId: action.value };
    }
    case "SHOP_CONFIG" : {
      let newShopConfig = {...state.shopConfig, ...action.value}
      return { ...state, shopConfig: newShopConfig };
    }
    case "LOGIN_CONFIG" : {
      let newLoginConfig = {...state.loginConfig, ...action.value}
      newLoginConfig.isLoggedIn = Boolean((newLoginConfig.shop && newLoginConfig.shop.domain)
        || (newLoginConfig.user && newLoginConfig.user.email))
      return { ...state, loginConfig:  newLoginConfig};
    }
    case "SELECTED_FILTERS" : {
      let newSelectedFilters = {...state.selectedFilters, ...action.value}
      return { ...state, selectedFilters:  newSelectedFilters};
    }
    case "INTEGRATIONS" : {
      return { ...state, integrations:  action.value};
    }
    default: {
      throw new Error(`Unhandled action type: ${action.type}`);
    }
  }
}

// Material Dashboard 2 PRO React context provider
function MaterialUIControllerProvider({ children }) {
  const initialState = {
    miniSidenav: false,
    transparentSidenav: false,
    whiteSidenav: false,
    sidenavColor: "dark",
    transparentNavbar: true,
    fixedNavbar: true,
    openConfigurator: false,
    direction: "ltr",
    layout: "dashboard",
    darkMode: false,
    selectedFilters : {
        request_type : "initial",
        start_date: dayjs().subtract(3, "month").startOf("month").toDate(),
        end_date: dayjs().toDate(),
        compare_start_date: dayjs().subtract(7, "month").startOf("month").toDate(),
        compare_end_date: dayjs().subtract(4, "month").toDate(),
        time_frame : "month",
        compare : true,
        applied_filters : {},
        filter_version : 1
    },
    // loginConfig does not change after login except mentioned keys below
    loginConfig : {
      loading: true,
      isLoggedIn: false,
      admin: false,
      feedbacks : {},
      adminShopOptions : [],
      shopOptions: [], // regular shopOptions - valid for both user and shop

      // firebase user that authenticated
      user : {},
      userData : {}, // db details of user
      userOnboarded : false, // mutable after initial loginConfig
      linkUserShop : {}, // mutable after initial loginConfig

      // shopify account that authenticated
      shop : {},
      shopOnboarded : false, // mutable after initial loginConfig
    },
    selectedShop : null,
    // single shop params for selectedShop
    shopConfig: {
      loading: false,
      subscription: {},
      subscription_enabled : false,
      syncDone: false,
      syncPercentage : 1
    },
    integrations : {
      loading: false,
    },
    selectedWorkspaceId : null,
  };

  const [controller, dispatch] = useReducer(reducer, initialState);

  // Handle selectedShop cookie after Shopify auth and initialize from localStorage
  useEffect(() => {
    // First check for cookie from backend auth
    const selectedShopCookie = getCookieFromDocument('selectedShop');
    if (selectedShopCookie) {
      // Use setSelectedShop to handle both localStorage and state
      setSelectedShop(dispatch, selectedShopCookie);
      // Clear the cookie
      document.cookie = 'selectedShop=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    } else {
      // If no cookie, try to get from localStorage
      const selectedShop = getLocalStorageValue(SELECTED_SHOP_KEY);
      if (selectedShop) {
        setSelectedShop(dispatch, selectedShop);
      }
    }
  }, []);

  const value = useMemo(() => [controller, dispatch], [controller, dispatch]);

  return <MaterialUI.Provider value={value}>{children}</MaterialUI.Provider>;
}

// Material Dashboard 2 PRO React custom hook for using context
function useMaterialUIController() {
  const context = useContext(MaterialUI);

  if (!context) {
    throw new Error(
      "useMaterialUIController should be used inside the MaterialUIControllerProvider."
    );
  }

  return context;
}

// Typechecking props for the MaterialUIControllerProvider
MaterialUIControllerProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

// Context module functions
const setMiniSidenav = (dispatch, value) => dispatch({ type: "MINI_SIDENAV", value });
const setTransparentSidenav = (dispatch, value) => dispatch({ type: "TRANSPARENT_SIDENAV", value });
const setWhiteSidenav = (dispatch, value) => dispatch({ type: "WHITE_SIDENAV", value });
const setSidenavColor = (dispatch, value) => dispatch({ type: "SIDENAV_COLOR", value });
const setTransparentNavbar = (dispatch, value) => dispatch({ type: "TRANSPARENT_NAVBAR", value });
const setFixedNavbar = (dispatch, value) => dispatch({ type: "FIXED_NAVBAR", value });
const setOpenConfigurator = (dispatch, value) => dispatch({ type: "OPEN_CONFIGURATOR", value });
const setDirection = (dispatch, value) => dispatch({ type: "DIRECTION", value });
const setLayout = (dispatch, value) => dispatch({ type: "LAYOUT", value });
const setDarkMode = (dispatch, value) => dispatch({ type: "DARKMODE", value });
const setSelectedShop = (dispatch, value) => {
  // Update localStorage
  setLocalStorageValue(SELECTED_SHOP_KEY, value);
  // Update state
  dispatch({ type: "SELECTED_SHOP", value });
};
const setSelectedWorkspaceId = (dispatch, value) => {
  setLocalStorageValue(SELECTED_WORKSPACE_ID_KEY, value);
  dispatch({ type: "SELECTED_WORKSPACE_ID", value })
};
const setIntegrations = (dispatch, value) => dispatch({ type: "INTEGRATIONS", value });
const setShopConfig = (dispatch, value) => dispatch({ type: "SHOP_CONFIG", value });
const setLoginConfig = (dispatch, value) => dispatch({ type: "LOGIN_CONFIG", value });
const setSelectedFilters = (dispatch, value) => dispatch({ type: "SELECTED_FILTERS", value });

const fetchLoginConfig = (dispatch, should_track = false, callback = null) => {
  const selectedShopLS = getLocalStorageValue(SELECTED_SHOP_KEY);
  const selectedWorkspaceIdLS = getLocalStorageValue(SELECTED_WORKSPACE_ID_KEY);

  if (should_track) {
    setLoginConfig(dispatch, { loading: true })
  }
  axiosInstance.post("/api/login", {
    selectedShop: selectedShopLS,
    selectedWorkspaceId: selectedWorkspaceIdLS
  })
    .then(function (response) {
        if (response.data) {

          if (mixpanel && response.data.mixpanelIdentify) {
              mixpanel.identify(response.data.mixpanelIdentify);
              customClarityKey('identity', response.data.mixpanelIdentify)
          }

          if (response.data.planName) {
              customClarityKey('plan', response.data.planName)
          }

          if (mixpanel && response.data.trackableMixpanel) {
              mixpanel.people.set(response.data.trackableMixpanel);
          }

          if (response.data.trackableIntercom) {
              bootIntercom(response.data.trackableIntercom);
              updateIntercom(response.data.trackableIntercom);
          }

          const isUserFlow = response.data.userData && response.data.userData.email;

          if (!isUserFlow) {
            const priorityShop = selectedShopLS ?? response.data.initSelectedShop;
            setSelectedShop(dispatch, priorityShop);
          } else {
            const [priorityShop, priorityWorkspaceId] = selectedShopLS && selectedWorkspaceIdLS ? [selectedShopLS, selectedWorkspaceIdLS] : [response.data.initSelectedShop, response.data.initSelectedWorkspaceId];

            if (priorityShop && priorityWorkspaceId && ((response?.data?.workspaces ?? []).find((w) => w.workspace_id === priorityWorkspaceId)?.shops ?? []).find((s) => s.myshopify_domain === priorityShop)) {
              setSelectedShop(dispatch, priorityShop);
              setSelectedWorkspaceId(dispatch, priorityWorkspaceId);
            } else if (priorityShop && (response?.data?.workspaces ?? []).length > 0) {
              // Find the workspace that contains the priority shop
              const workspaceWithShop = (response?.data?.workspaces ?? []).find((workspace) =>
                (workspace?.shops ?? []).some((shop) => shop.myshopify_domain === priorityShop)
              );

              if (workspaceWithShop) {
                setSelectedShop(dispatch, priorityShop);
                setSelectedWorkspaceId(dispatch, workspaceWithShop.workspace_id);
              } else if ((response?.data?.workspaces ?? []).length > 0 && (response?.data?.workspaces[0]?.shops ?? []).length > 0) {
                // Fallback to first workspace and first shop
                setSelectedShop(dispatch, response.data.workspaces[0].shops[0].myshopify_domain);
                setSelectedWorkspaceId(dispatch, response.data.workspaces[0].workspace_id);
              }
            } else if ((response?.data?.workspaces ?? []).length > 0 && (response?.data?.workspaces[0]?.shops ?? []).length > 0) {
              // Fallback to first workspace and first shop when no priority shop
              setSelectedShop(dispatch, response.data.workspaces[0].shops[0].myshopify_domain);
              setSelectedWorkspaceId(dispatch, response.data.workspaces[0].workspace_id);
            }
          }

          if (response.data.initShopConfig) {
              setShopConfig(dispatch, response.data.initShopConfig);
          }

          if (mixpanel && should_track) {
            tracker.event("App Opened", {sync_done: response.data.syncDone})
          }

          setLoginConfig(dispatch, { ...response.data, loading: false })
        } else {
          setLoginConfig(dispatch, { loading: false })
        }

        if (!!callback && typeof callback === 'function') {
          callback()
        }
    })
    .catch((err) => {
        setLoginConfig(dispatch, { loading: false })
        if (!!callback && typeof callback === 'function') {
          callback()
        }
        console.log("error in loginconfig ", err);
    });
};

const fetchShopConfig = (dispatch, selectedShop) => {

  let reqData = {}
  if (!!selectedShop) {
    reqData.selectedShop = selectedShop
  }

  setShopConfig(dispatch, { loading: true })
  axiosInstance.post("/api/shop/config", reqData)
    .then(function (response) {
        if (response.data) {
          setShopConfig(dispatch, { ...response.data, loading: false })
        } else {
          setShopConfig(dispatch, { loading: false })
        }
    })
    .catch((err) => {
        setShopConfig(dispatch, { loading: false })
        console.log("error in shopconfig ", err);
    });
}

const fetchIntegrations = (dispatch, selectedShop) => {

  let reqData = {};
  if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
  }

  setIntegrations(dispatch, { loading: true });
  axiosInstance.post('/api/integrations', reqData)
      .then((response) => {
          if (response.data) {
            setIntegrations(dispatch, { ...response.data, loading: false })
          } else {
              setIntegrations(dispatch, { loading: false });
          }
      })
      .catch((error) => {
          setIntegrations(dispatch, { loading: false });
          console.log(error)
      })
}

export {
  MaterialUIControllerProvider,
  useMaterialUIController,
  setMiniSidenav,
  setTransparentSidenav,
  setWhiteSidenav,
  setSidenavColor,
  setTransparentNavbar,
  setFixedNavbar,
  setOpenConfigurator,
  setDirection,
  setLayout,
  setDarkMode,
  setSelectedShop,
  setSelectedWorkspaceId,
  setShopConfig,
  setLoginConfig,
  setIntegrations,
  setSelectedFilters,
  fetchIntegrations,
  fetchShopConfig,
  fetchLoginConfig,
  useCancellableAxios,
  mixpanel,
  axiosInstance,
  cubejsApi, // function
  // intercom
  loadIntercom,
  bootIntercom,
  updateIntercom,
  tracker
};
