import { useState, useEffect, useMemo } from "react";

// react-router components
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
// import TagManager from 'react-gtm-module';
import { tracker } from "@/context";
import { Link } from "react-router-dom";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// @mui material components
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import PricingPage from "@/layouts/pages/pricing-page";
import DataSyncStatus from "@/components/DataSyncStatus";
import { useSearchParams } from 'react-router-dom'

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import AppBar from "@mui/material/AppBar";
import AppLoader from "@/components/AppLoader/DLogo";
import Sidenav from "@/examples/Sidenav";
import Configurator from "@/examples/Configurator";
import SignInCover from "@/layouts/authentication/sign-in/cover";
import SignUpCover from "@/layouts/authentication/sign-up/illustration";
import ResetPassword from "@/layouts/authentication/reset-password/cover";
import EmailVerification from "@/layouts/authentication/email-verification";
import AddNewStore from "@/layouts/authentication/add-store";
import LinkStore from "@/layouts/authentication/link-store";
import OnboardingSurvey from "@/components/Onboarding";
import BookCall from "@/layouts/pages/book-call";
import { app } from '@/firebase-config';
import { getAuth, signInWithCustomToken } from 'firebase/auth';
import SubscriptionCallbackDialog from "@/components/SubscriptionCallbackDialog";
import Invitation from "@/components/Invitation";
import Identification from "@/layouts/authentication/identification";
import { isShopifyIdentificationEnabled } from "@/utils/featuresupport";

// Material Dashboard 2 PRO React themes
import theme from "@/assets/theme";

// Material Dashboard 2 PRO React Dark Mode themes
import themeDark from "@/assets/theme-dark";

// Material Dashboard 2 PRO React routes
import routes from "@/router";

// Material Dashboard 2 PRO React contexts
import { useMaterialUIController, setMiniSidenav, fetchLoginConfig, fetchShopConfig, fetchIntegrations, setLoginConfig } from "@/context";
import "@/assets/custom.css";
import "@/assets/custom.scss";

// Images
import brandShort from "@/assets/images/brand-short-2.png";
import MDTypography from "@/components/MDTypography";

import MDAlert from "@/components/MDAlert";
import premiumTag from "@/assets/images/premium-tag.png";
import { useTranslation } from "react-i18next";

const HomePage = () => {
  return <Navigate to="/dashboard" />
}

// All protected routes need loginConfig to be loaded
const ProtectedRoute = ({ children }) => {
  const [controller, dispatch] = useMaterialUIController();
  const { loginConfig, shopConfig } = controller;
  let location = useLocation();
  const { t } = useTranslation();

  // App level loader
  if (loginConfig.loading) {
    return <AppLoader />;
  }

  if (!loginConfig.isLoggedIn) {
    return <Navigate to="/sign-in" state={{ from: location }} replace />
  }

  // User flow handling for ProtectedRoute
  if (!!loginConfig.user && !!loginConfig.user.email) {
    if (!loginConfig.user.emailVerified) {
      return <EmailVerification />
    }

    if (!loginConfig.userData || !loginConfig.userData.uid || !loginConfig.userOnboarded) {
      return <OnboardingSurvey user={loginConfig.user} t={t} onOnboardingFinish={() => setLoginConfig(dispatch, { userOnboarded: true })} />
    }

    if (!!loginConfig.pendingInvitation && !!loginConfig.pendingInvitation.invitation_id) {
      return <Invitation t={t} pendingInvitation={loginConfig.pendingInvitation} />
    }

    // Used to link first store and subsequent stores with user account
    if (!!loginConfig.linkUserShop && !!loginConfig.linkUserShop.shop_id) {
      return <LinkStore />
    }


    let storeOptionsCount = 0;
    if (!!loginConfig.shopOptions && loginConfig.shopOptions.length > 0) {
      storeOptionsCount = loginConfig.shopOptions.filter((s) => s.is_store).length
    }
    // Not a single shopify store mapped with user or authenticated right now
    if (storeOptionsCount == 0) {
      return <AddNewStore />
    }

    // initShopConfig is used to check if first shop's data Sync is done or not
    if (!shopConfig.syncDone) {
      return (<DataSyncStatus />);
    }

    return children
  }

  // Shop flow handling for ProtectedRoute
  if (!!loginConfig.shop && !!loginConfig.shop.myshopify_domain) {

    // if (isShopifyIdentificationEnabled()) {
    //   // Once we identify the user, the user will always land in the loginConfig.user block above
    //   return <Identification />
    // }

    if (!loginConfig.shopOnboarded) {
      return (<OnboardingSurvey t={t} onOnboardingFinish={() => setLoginConfig(dispatch, { shopOnboarded: true })} />);
    }

    if (!shopConfig.syncDone) {
      return (<DataSyncStatus />);
    }

    return children
  }

  return children
};

export default function App() {
  const [controller, dispatch] = useMaterialUIController();
  const {
    miniSidenav,
    layout,
    sidenavColor,
    transparentSidenav,
    whiteSidenav,
    darkMode,
    shopConfig,
    loginConfig,
    selectedShop,
  } = controller;
  const [onMouseEnter, setOnMouseEnter] = useState(false);
  const [isFirstAuthCheckDone, setIsFirstAuthCheckDone] = useState(false);
  const [isSecondAuthCheckDone, setIsSecondAuthCheckDone] = useState(false);
  const { pathname } = useLocation();
  const { t } = useTranslation();
  let showSubscriptionBanner = shopConfig.subscription_enabled
    && shopConfig.planDetails?.planType == "free";

  const [searchParams, setSearchParams] = useSearchParams();
  let modalType = searchParams.get('mt')

  useEffect(() => {
    if (!!modalType) {
      switch (modalType) {
        case "sub":
        case "suberr":
          NiceModal.show(SubscriptionCallbackDialog, { mt: modalType })
          break;
      }
    }
  }, []);

  let firstName = "there";
  if (!!loginConfig.userData && !!loginConfig.userData.onboard_name) {
    let nameParts = loginConfig.userData.onboard_name.split(" ");
    firstName = nameParts[0];
  } else if (!!loginConfig.shop && !!loginConfig.shop.onboard_name) {
    let nameParts = loginConfig.shop.onboard_name.split(" ");
    firstName = nameParts[0];
  }

  useEffect(() => {
    // TagManager.initialize({gtmId: 'GTM-KPSD95V'})
    tracker.intercom.load();
  }, []);

  useEffect(() => {
    const auth = getAuth(app);
    const unsubscribe = auth.onAuthStateChanged(user => {
      setLoginConfig(dispatch, { user: user });

      if (!!user && !!user.email) {
        fetchLoginConfig(dispatch, true, function () {
          setIsFirstAuthCheckDone(true);
        });
      } else {
        setIsFirstAuthCheckDone(true);
      }
    });
    // return unregister observer
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (loginConfig.loading && isFirstAuthCheckDone && !isSecondAuthCheckDone) {
      setIsSecondAuthCheckDone(true);
      fetchLoginConfig(dispatch, true)
    }
  }, [loginConfig.loading, isFirstAuthCheckDone, isSecondAuthCheckDone]);

  // For the first time when shopConfig is loaded fetch integrations for this shop
  useEffect(() => {
    if (!!shopConfig.shop?.myshopify_domain) {
      fetchIntegrations(dispatch, shopConfig.shop?.myshopify_domain)
    }
  }, [!!shopConfig.shop?.myshopify_domain]);

  useEffect(() => {
    if (!!selectedShop) {
      fetchShopConfig(dispatch, selectedShop)
      fetchIntegrations(dispatch, selectedShop)
    }
  }, [selectedShop]);

  // Open sidenav when mouse enter on mini sidenav
  const handleOnMouseEnter = () => {
    if (miniSidenav && !onMouseEnter) {
      setMiniSidenav(dispatch, false);
      setOnMouseEnter(true);
    }
  };

  // Close sidenav when mouse leave mini sidenav
  const handleOnMouseLeave = () => {
    if (onMouseEnter) {
      setMiniSidenav(dispatch, true);
      setOnMouseEnter(false);
    }
  };

  // Setting page scroll to 0 when changing the route
  useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
    tracker.event("App Pageview", { page: pathname });
  }, [pathname]);

  const getRoutes = (allRoutes) =>
    allRoutes.map((route) => {
      if (route.collapse) {
        return getRoutes(route.collapse);
      }

      // All sidenav routes are protected for now
      if (route.route) {
        return (
          <Route exact path={route.route} key={route.key}
            element={<ProtectedRoute>{route.component}</ProtectedRoute>} />
        );
      }

      return null;
    });

  let filteredRoutes = []
  for (var k in routes) {
    if ('permission' in routes[k]) {
      switch (routes[k].permission) {
        case 'admin':
          if (!loginConfig.admin) {
            continue
          }
          break;
      }
    }

    filteredRoutes.push(routes[k])
  }

  return (
    <ThemeProvider theme={darkMode ? themeDark : theme}>
      <NiceModal.Provider>
        <CssBaseline />
        {!loginConfig.loading && layout === "dashboard" && showSubscriptionBanner && (
          <AppBar>
            <MDAlert color="info" onClick={() => {
              tracker.event("Subscription Banner Clicked", { page: pathname });
              NiceModal.show(PaywallDialog, { feature: "" })
            }} sx={{
              minHeight: "1rem",
              cursor: "pointer",
              padding: "0.5rem !important",
              borderRadius: "0 !important",
              display: "flex !important",
              justifyContent: "center !important",
              alignItems: "center !important",
            }}>
              <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" mx={0.8} />
              <MDTypography variant="button" color="white" fontWeight="regular">
                {t("subscription-bar-title", { firstName: firstName })} &nbsp;
                <MDButton size="small" color="dark" variant="contained" sx={{
                  minHeight: "1rem !important",
                  padding: "0.3rem",
                  paddingLeft: "0.5rem",
                  paddingRight: "0.5rem",
                  borderRadius: "0 !important",
                  fontSize: "0.65rem !important",
                }}>{t("subscription-bar-btn")}</MDButton>
              </MDTypography>
            </MDAlert>
          </AppBar>
        )}
        {!loginConfig.loading && layout === "dashboard" && (
          <>
            <Sidenav
              className="sidenav"
              color={sidenavColor}
              brand={brandShort}
              brandName="DATADREW"
              brandNameTagline="Analytics"
              routes={filteredRoutes}
              onMouseEnter={handleOnMouseEnter}
              onMouseLeave={handleOnMouseLeave}
            />
            {loginConfig.admin && <Configurator />}
          </>
        )}
        {!loginConfig.loading && layout === "dashboard" && pathname != "/feature-requests" &&
          <MDBox bgColor="dark" className="feature-banner" component={Link} to="/feature-requests" onClick={() => {
            tracker.event("Feature Request Clicked", { page: pathname })
          }}>
            <MDTypography variant="button" color="white" textTransform="none" fontWeight="regular" p={1.2}>
              Feature Request ?
            </MDTypography>
          </MDBox>}
        <Routes>
          {getRoutes(filteredRoutes)}
          {/* routes other than Sidenav */}
          <Route path={"/sign-up"} element={<SignUpCover />} key="signup" />
          <Route path={"/sign-in"} element={<SignInCover />} key="signin" />
          <Route path={"/identify"} element={<Identification />} key="identify" />
          <Route path={"/reset-password"} element={<ResetPassword />} key="reset-password" />
          <Route exact path={"/book-call"} key={"book-call"} element={<ProtectedRoute><BookCall /></ProtectedRoute>} />
          <Route exact path={"/pricing"} key={"pricing"} element={<ProtectedRoute><PricingPage /></ProtectedRoute>} />
          <Route exact path={"/"} element={<ProtectedRoute><HomePage /></ProtectedRoute>} key={"home"} />
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
        <ToastContainer
          className={"toast-msg"}
          position="bottom-right"
          autoClose={2000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light" />
      </NiceModal.Provider>
    </ThemeProvider>
  );
}
