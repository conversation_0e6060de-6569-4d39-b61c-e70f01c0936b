<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="/assets/images/brand-short-3.png" />
    <meta name="theme-color" content="#1A73E8" />
    <meta name="robots" content="noindex, nofollow">
    <!-- <link rel="manifest" href="assets/manifest.json" /> -->
    <title>DataDrew Analytics</title>

    <!-- Ensures that the UI is properly scaled in the Shopify Mobile app -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    /> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link
      rel="stylesheet"
      href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
      integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
      crossorigin=""
    />
    <link
      href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/@mdi/font@7.1.96/css/materialdesignicons.min.css"
      rel="stylesheet"
    />
    <script type="text/javascript">
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "g2qzt806av");
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-2ZWB0VQEVF"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-2ZWB0VQEVF');
    </script>

    <!-- <script>
      !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
      posthog.init('phc_YitdxKp3KSDsbRJNPu0TnSDDY1mJaULwIqcM1dGBTxT',{api_host:'https://app.posthog.com'})
    </script> -->

    <!-- <link href="https://cdn.jsdelivr.net/npm/antd@5.1.6/dist/reset.min.css" rel="stylesheet" /> -->
    <script type="module">
      if (!import.meta.env || !import.meta.env.PROD) {
        // Because the base HTML is rendered in the server side, we need to manually embed the code to enable HMR in our
        // code, so that the vite server is properly enabled to run HMR
        const script = document.createElement('script');
        script.setAttribute('type', "module");
        script.setAttribute('src', "/dev_embed.js");
        document.getElementsByTagName('head')[0].append(script);
      }
    </script>

    <!-- Meta Pixel Code -->
    <script>
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '644289124461808');
      fbq('track', 'PageView');
      fbq('trackCustom', 'AppPageView');
    </script>
    <!-- End Meta Pixel Code -->
  </head>
  <body>
    <div id="app"><!--index.jsx injects App.jsx here--></div>
    <script type="module" src="/index.jsx"></script>
    <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=644289124461808&ev=PageView&noscript=1"
      /></noscript>
  </body>
</html>
