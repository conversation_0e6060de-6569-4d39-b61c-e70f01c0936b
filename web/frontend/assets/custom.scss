@use "sass:color";

.d-spinner {
  $color-one: #3c4c68;
  $color-two: #8593aa;
  $color-three: #cfd4e3;
  $color-four: #f6f6f9;
  animation-duration: 2.4s;
  animation-timing-function: cubic-bezier(0, 1, 0.3, 1);
  animation-direction: normal;
  animation-iteration-count: infinite;
  transform-origin: left bottom;
  &.d-spinner__one   { animation-name: dSpinnerOne; fill: $color-one; }
  &.d-spinner__two   { animation-name: dSpinnerTwo; fill: $color-two; }
  &.d-spinner__three { animation-name: dSpinnerThree; fill: $color-three; }
  &.d-spinner__four  { animation-name: dSpinnerFour; fill: $color-four; }

  @keyframes dSpinnerOne {
    0% {
      opacity: 0;
      fill: color.adjust($color-one, $lightness: 20%);
      transform: rotateZ(-65deg) scale(0.6);
    }
    7% {
      opacity: 1;
      fill: color.adjust($color-one, $lightness: 20%);
      transform: rotateZ(0) scale(1.0);
    }
    57% {
      animation-timing-function: cubic-bezier(0, 0, 0, 1);
      fill: $color-one;
    }
    74% {
      opacity: 1;
      transform: rotateZ(0) scale(1.0);
      animation-timing-function: cubic-bezier(0, 0, 1, 0);
    }
    83% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
    100% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
  }

  @keyframes dSpinnerTwo {
    0% {
      opacity: 0;
      transform: rotateZ(-65deg) scale(0.6);
    }
    3% {
      opacity: 0;
      fill: color.adjust($color-two, $lightness: 20%);
      transform: rotateZ(-65deg) scale(0.6);
    }
    10% {
      opacity: 1;
      fill: color.adjust($color-two, $lightness: 20%);
      transform: rotateZ(0) scale(1.0);
    }
    60% {
      animation-timing-function: cubic-bezier(0, 0, 0, 1);
      fill: $color-two;
    }
    71% {
      opacity: 1;
      transform: rotateZ(0) scale(1.0);
      animation-timing-function: cubic-bezier(0, 0, 1, 0);
    }
    79% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
    100% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
  }

  @keyframes dSpinnerThree {
    0% {
      opacity: 0;
      transform: rotateZ(-65deg) scale(0.6);
    }
    6% {
      opacity: 0;
      fill: color.adjust($color-three, $lightness: 20%);
      transform: rotateZ(-65deg) scale(0.6);
    }
    13% {
      opacity: 1;
      fill: color.adjust($color-three, $lightness: 20%);
      transform: rotateZ(0) scale(1.0);
    }
    63% {
      fill: $color-three;
      animation-timing-function: cubic-bezier(0, 0, 0, 1);
    }
    68% {
      opacity: 1;
      transform: rotateZ(0) scale(1.0);
      animation-timing-function: cubic-bezier(0, 0, 1, 0);
    }
    76% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
    100% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
  }

  @keyframes dSpinnerFour {
    0% {
      opacity: 0;
      transform: rotateZ(-65deg) scale(0.6);
    }
    9% {
      opacity: 0;
      fill: color.adjust($color-four, $lightness: 20%);
      transform: rotateZ(-65deg) scale(0.6);
    }
    16% {
      opacity: 1;
      fill: color.adjust($color-four, $lightness: 20%);
      transform: rotateZ(0) scale(1.0);
    }
    64% {
      fill: $color-four;
      animation-timing-function: cubic-bezier(0, 0, 0, 1);
    }
    65% {
      opacity: 1;
      transform: rotateZ(0) scale(1.0);
      animation-timing-function: cubic-bezier(0, 0, 1, 0);
    }
    73% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
    100% {
      opacity: 0;
      transform: rotateZ(45deg) scale(0.61);
    }
  }  
}

.gradient-primary {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg { overflow: visible; }
  background-image: -webkit-radial-gradient(0% bottom,   rgba(239, 102, 148, 0.7) 0%, rgba(239, 102, 148, 0) 60%), -webkit-radial-gradient(90% bottom,   #523f8c 0%, rgba(82, 63, 140, 0) 40%), -webkit-radial-gradient(50% top,   rgba(84, 90, 182, 0.6) 0%, rgba(84, 90, 182, 0) 75%), -webkit-radial-gradient(right top,   #794aa2 0%, rgba(121, 74, 162, 0) 57%);
  background-image: radial-gradient(  at 0% bottom, rgba(239, 102, 148, 0.7) 0%, rgba(239, 102, 148, 0) 60%), radial-gradient(  at 90% bottom, #523f8c 0%, rgba(82, 63, 140, 0) 40%), radial-gradient(  at 50% top, rgba(84, 90, 182, 0.6) 0%, rgba(84, 90, 182, 0) 75%), radial-gradient(  at right top, #794aa2 0%, rgba(121, 74, 162, 0) 57%);
  background-size: auto, auto, 100% 1000px, 100% 1000px;
  background-repeat: no-repeat; 
}