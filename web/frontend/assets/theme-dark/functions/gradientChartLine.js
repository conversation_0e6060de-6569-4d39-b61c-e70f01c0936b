/**
  The gradientChartLine() function helps you to create a gradient color for the chart line
 */

// Material Dashboard 3 PRO React helper functions
import rgba from "@/assets/theme/functions/rgba";

function gradientChartLine(chart, color, opacity = 0.2) {
  const gradientStroke = chart.createLinearGradient(0, 230, 0, 50);
  const primaryColor = rgba(color, opacity).toString();

  gradientStroke.addColorStop(1, primaryColor);
  gradientStroke.addColorStop(0.2, "rgba(72, 72, 176, 0.0)");
  gradientStroke.addColorStop(0, "rgba(203, 12, 159, 0)");

  return gradientStroke;
}

export default gradientChartLine;