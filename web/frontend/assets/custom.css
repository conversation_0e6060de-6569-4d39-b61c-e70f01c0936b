/* @import "~antd/dist/antd.css"; */

:root {
    --toastify-color-light: #f0f2f5;
    --toastify-color-dark: #344767;
    --toastify-color-info: #1A73E8;
    --toastify-color-success: #4CAF50;
    --toastify-color-warning: #fb8c00;
    --toastify-color-error: #f44335;
    --toastify-color-transparent: rgba(255, 255, 255, 0.7);

    --toastify-toast-width: 330px;
    --toastify-toast-background: #fff;
    --toastify-toast-min-height: 54px;
    --toastify-toast-max-height: 800px;
    --toastify-font-family: Poppins, Helvetica, Arial, sans-serif;
    --toastify-z-index: 9999;
}

.toast-msg {
    font-size: 14px;
}

.stat-title {
    font-family: Poppins, Helvetica, Arial, sans-serif;
    text-transform: capitalize;
    color: rgb(52, 71, 103);
}

.custom-ant-card {
    border-radius: 3px;
    box-shadow: 0 0 0 1px rgb(63 63 68 / 5%), 0 1px 1px 0 rgb(63 63 68 / 15%);
    outline: 1px solid transparent;
    background-color: #fff;
    border: 1px solid rgba(46, 41, 78, 0.05);
    border-radius: 20px;
    box-shadow: 0 10px 10px rgb(0 0 0 / 2%);
}

.report-chart-container {
    padding: 15px 20px 30px;
    min-height: 400px;
    position: relative;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: row-reverse;
    flex-direction: row-reverse;
}

.custom-ant-card.topless-border {
    border-top-color: #fff;
    border-top: 0;
    outline: 0;
}

.custom-ant-card .ant-card-head-title {
    margin: 0 15px 0 0;
    font-weight: 400;
    min-width: 250px;
    color: #2e294e;
}

.custom-tooltip {
    background: rgba(255, 255, 255, 0.7);
    padding: 5px;
    width: 80%;
}

.custom-tooltip-full {
    background: rgba(255, 255, 255, 0.8);
    padding: 5px;
    width: 100%;
    user-select: none;
}

.ant-card-no-padding .ant-card-body {
    padding: 0;
}

.ant-tooltip-inner {
    color: black;
    background-color: white;
}

.ant-tooltip-arrow-content {
    background-color: white;
}

.font-weight-light-bold {
    font-weight: 250;
}

.font-weight-light-bold b {
    font-weight: 500 !important;
}

.custom-ant-tooltip-basic {
    font-size: 13px;
    cursor: pointer;
    padding: 10px;
    line-height: initial;
    text-align: center;
}

#filter-form .ant-form-item-label {
    padding-bottom: 3px;
}

.ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--background-color);
    border-color: var(--border-color);
}

.no-border-radius .ant-select-selector {
    border-radius: 0 !important;
}

.no-border-all .ant-select-selector {
    border: 0 !important;
}

.no-focus-color .ant-select-selector {
    border-color: #d9d9d9 !important;
    box-shadow: none !important;
}

/** Report config panel **/
.report-config-panel {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    transition: 0.1s;
    border-bottom: 1px solid transparent;
    border-radius: 20px;
    z-index: 1;
    background-color: initial;
    padding: 5px 0;
    z-index: 111;
}

.report-config-panel.is-sticky {
    border-color: rgba(46, 41, 78, 0.05);
    background-color: #fff;
    margin: 0 -40px 10px;
    padding: 15px 40px;
    border-radius: 0;
}

/** Custom AntD button **/

.pillbar-btn, .pillbar-btn:hover, .pillbar-btn:focus {
    display: -webkit-flex;
    display: flex;
    border-radius: 10px;
    background-color: rgba(46, 41, 78, 0.03);
    border: 1px solid rgba(46, 41, 78, 0.03);
    position: relative;

    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 16px;
    white-space: nowrap;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;

    padding: 7px 10px;
    border-radius: 8px;
    margin: 3px;
    cursor: pointer;
    transition: 0.2s;
    position: relative;
    color: rgba(46, 41, 78, 0.6);
}

.pillbar-btn:hover, .pillbar-btn:focus {
    color: #2e294e;
}

.ant-spin-container {
    font-size: 14px;
}

/** Custom Ant date picker **/

.report-config-panel .ant-space-item {
    display: flex;
    border-radius: 8px;
    background-color: rgba(46, 41, 78, 0.03);
    border: 0;
    position: relative;
}

.report-config-panel .ant-picker {
    display: -webkit-flex;
    display: flex;
    border-radius: 8px;
    padding-left: 0;
    padding-right: 4px;
    background-color: #f8f8f9;
    border: 0;
    position: relative;
}

.report-config-panel .ant-picker-input-active {
    transition-delay: 0.2s;
    background-color: transparent;
    border : 0;
    box-shadow: none !important;
    color: #2e294e;
}

.ant-picker-dropdown {
    z-index:  10001 !important;
}

.report-config-panel .ant-picker-outlined, .report-config-panel .ant-picker-outlined:focus-within {
    border: 0;
    box-shadow: none !important;
}

.hide-input .ant-picker-input, .hide-input .ant-picker-range-separator {
    display: none;
}

.report-config-panel .ant-picker-input input {
    color: rgba(46, 41, 78, 0.6);
    cursor: pointer;
    font-size: 13px;
    text-align: center;
    font-weight: 500;
    padding: 9px 0px;
    color: #344767;
    line-height: 16px;
    background-color: initial;
}

.report-config-panel .input-label-text {
    color: rgba(46, 41, 78, 0.6);
    cursor: pointer;
    text-transform: capitalize;
    font-weight: 500;
    padding: 8px 4px;
    color: #344767;
    line-height: 16px;
    background-color: initial;
}

.report-config-panel  .date-picker-preset {
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    background-color: white;
}

/** custom extra ant button **/

.ant-btn-card-extra, .ant-btn-card-extra:hover, .ant-btn-card-extra:focus {
    border-radius: 10px;
    color: rgba(46, 41, 78, 0.6);
    cursor: pointer;
    background-color: rgba(46, 41, 78, 0.03);
}

.ant-btn-card-extra:hover, .ant-btn-card-extra:focus {
    color: #2e294e;
}

/** PILLBAR **/
.pillbar {
    display: -webkit-flex;
    display: flex;
    border-radius: 10px;
    background-color: rgba(46, 41, 78, 0.03);
    border: 1px solid rgba(46, 41, 78, 0.03);
    position: relative;
}
.pillbar-indicator {
    position: absolute;
    top: 2px;
    bottom: 2px;
    background-color: #fff;
    cursor: pointer;
    display: -webkit-flex;
    display: flex;
    z-index: 0;
    opacity: 0;
}
.pillbar-indicator.initialized {
    opacity: 1;
}
.pillbar-indicator-left,
.pillbar-indicator-center,
.pillbar-indicator-right {
    position: absolute;
    top: 0;
    bottom: 0;
    background-color: #fff;
    width: 10px;
}
.pillbar-indicator-left {
    border-radius: 8px 0 0 8px;
    border: 1px solid rgba(46, 41, 78, 0.06);
    border-right: none;
}
.pillbar-indicator-right {
    border-radius: 0 8px 8px 0;
    border: 1px solid rgba(46, 41, 78, 0.06);
    border-left: none;
}
.pillbar-indicator-center {
    left: 10px;
    width: 1px;
    -webkit-transform-origin: left;
    transform-origin: left;
    will-change: transform;
    border: 1px solid rgba(46, 41, 78, 0.06);
    border-left: none;
    border-right: none;
}
.pillbar .pill {
    padding: 7px 10px;
    border-radius: 8px;
    margin: 3px;
    cursor: pointer;
    transition: 0.2s;
    position: relative;
    color: rgba(46, 41, 78, 0.6);
}
.pillbar .pill:hover {
    color: #2e294e;
}
.pillbar .pill:hover .tooltip {
    opacity: 1;
    visibility: visible;
}
.pillbar .pill:hover svg * {
    stroke: #2e294e;
}
.pillbar .pill.active {
    transition-delay: 0.2s;
    background-color: transparent;
    color: #2e294e;
}
.pillbar .pill.active svg * {
    stroke: #2e294e;
}
.pillbar .pill.selected {
    transition-delay: 0.2s;
    background-color: #fff;
    color: #ff0761;
}
.pillbar .pill.selected svg * {
    stroke: #2e294e;
}
.pillbar .pill.disabled {
    color: #ccc !important;
    background-color: transparent !important;
}
.pillbar .pill svg * {
    stroke: rgba(46, 41, 78, 0.6);
}
.pillbar .pill-content {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 16px;
    white-space: nowrap;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.pillbar .pill input {
    display: none;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    left: -9999px;
}
.pillbar .tooltip {
    top: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
}

.pillbar-vertical {
    position: relative;
    background-color: transparent;
    border: none;
}
.pillbar-vertical .pillbar-options {
    height: 38px;
    overflow: hidden;
    background-color: rgba(46, 41, 78, 0.03);
    border: 1px solid rgba(46, 41, 78, 0.03);
    border-radius: 10px;
    z-index: 1000;
}
.pillbar-vertical .pillbar-options:hover {
    overflow: visible;
}
.pillbar-vertical .pillbar-options-inner {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    background-color: #f8f8f9;
    border: 1px solid rgba(46, 41, 78, 0.03);
    z-index: 1000;
    border-radius: 10px;
    margin: 0 -1px;
}
.pillbar-vertical .pillbar-options .pill.active {
    transition-delay: 0;
    background-color: #fff;
    color: #2e294e;
}
.pillbar-vertical .pillbar-options .pill.active svg * {
    stroke: #2e294e;
}

/** Pillbar select styles **/

.select-style-1.with-background .select-style-1__control {
    cursor: pointer;
    background-color: rgba(46, 41, 78, 0.03) !important;
    border: 1px solid rgba(46, 41, 78, 0.03) !important;
}

.select-style-1.with-icon .select-style-1__value-container {
    padding: 2px 12px 2px 36px !important;
}

.select-style-1__control {
    cursor: pointer;
    min-width: 200px; /* changed */
    min-height: 38px !important;
    background-color: #fff;
    border: 1px solid rgba(46, 41, 78, 0.06) !important;
    border-radius: 10px !important;
    font-size: 13px;
}

.select-style-1__value-container {
    /* font-weight: 500; */
    padding: 2px 12px 2px 12px !important;
    color: #2e294e !important;
}

.select-style-1__indicator-separator {
    display: none !important;
}

.select-style-1__dropdown-indicator {
    padding: 6px 12px 6px 0 !important;
}

.select-style-1__clear-indicator {
    padding: 0 6px 0 !important;
}

.select-style-1__single-value {
    color: #2e294e !important;
}

.select-style-1__option {
    cursor: pointer !important;
}

.select-style-1__option--is-selected {
    background-color: rgba(52, 71, 103, 0.9) !important;
}

.select-style-1__multi-value {
    border-radius: 6px !important;
    background-color: #e4e8ed !important;
    color: #2e294e !important;
}

.select-style-1__multi-value__remove {
    border-radius: 6px !important;
    padding-left: 3px !important;
    padding-right: 3px !important;
    cursor: pointer;
}

.select-style-1__menu {
    border-radius: 10px !important;
    border: 1px solid rgba(46, 41, 78, 0.06) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    padding-top: 0px;
    padding-bottom: 0px;
    cursor: pointer;
    width: 200px;
}

.select-style-1 .flat-virtualized-item {
    padding: 0px 6px 4px 5px;
}

.select-style-1 .fast-option {
    overflow: hidden;
    white-space: nowrap;
    color: #2e294e !important;
    border-radius: 8px;
    border: 1px solid transparent;
    line-height: 34px !important;
}
.select-style-1 .fast-option:hover {
    background-color: rgba(255, 7, 97, 0.03) !important;
    border-color: rgba(255, 7, 97, 0.03) !important;
    color: #ff0761 !important;
}

.select-style-1 .fast-option-focused {
    background-color: transparent !important;
    border-color: transparent !important;
    color: #2e294e !important;
}

.select-style-1 .fast-option-selected {
    background-color: rgba(255, 7, 97, 0.03) !important;
    border-color: rgba(255, 7, 97, 0.03) !important;
    color: #ff0761 !important;
}


/** Welcome user **/
.slider-logo {
    transition: all 0.2s;
    background: rgba(255, 255, 255, 0.3);
}

.slider-logo.collapsed {
    height: 45px;
    margin: auto;
    margin-bottom: 12px;
    margin-top: 12px;
    width: 70%;
}

.slider-logo.uncollapsed {
    height: 100px;
    width: 50%;
    margin: auto;
    margin-bottom: 40px;
    margin-top: 40px;
}

.site-layout .site-layout-background {
  background: #fff;
}

.custom-ant-select-dark .ant-select-selector {
    color: rgba(255, 255, 255, 0.65) !important;
    background: #001529 !important;
    width:230;
}

.ant-layout-sider-children {
    position: relative;
}

.ant-layout-sider-children .slider-options-2 {
    position: absolute;
    bottom: 0;
}

.slider-option-switch-shop.ant-menu-item-active {
    color:orange !important;
} 

*:focus {
    outline: none;
}

.explore-items::-webkit-scrollbar {
    display: none;
}

.sidenav  *::-webkit-scrollbar {
    display: none;
}

.no-scrollbar::-webkit-scrollbar {
    display: none !important;
}

.explore-items {
    overflow-y: scroll;
    max-height: 300px;
    scrollbar-width: none;
}

.feature-banner {
    position: fixed;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    top: 50vh;
    right: -35px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: top;
    -moz-transform-origin: top;
    -o-transform-origin: top;
    -ms-transform-origin: top;
    transform-origin: top;
    height: 35px;
    transition: all 200ms linear;
    cursor: pointer;
}

.feature-banner:hover {
    height : 75px;
    right : 0px;
}

.feature-banner>h6 {
    cursor: pointer;
    font-size: 13px;
    padding-left: 15px;
    padding-right: 15px;
}


.compare-date-picker {
    height: auto;
    width: auto;
    border: none;
    border-radius: 0px;
    cursor: pointer;
    font-size: 17px;
    margin: 0px;
    padding: 0px;
}

.ant-picker-footer-extra .ant-picker-dropdown {
    left: 0 !important;
    top: 0 !important;
}

.compare-date-picker-popup .ant-picker-range-arrow {
    display: none !important;
}

.compare-date-picker-popup .ant-picker-panel-container {
    border-radius: 0 !important;
    box-shadow: none !important;
}

.compare-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner, .compare-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    background-color: hsla(33, 100%, 49%, 0.8);
}

.compare-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-range-start::before, .compare-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-range-end::before {
    background-color: initial !important;
} 

.compare-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
    background-color: hsla(33, 100%, 49%, 0.2);
}

.compare-date-picker .ant-picker-cell-range-hover, .compare-date-picker .ant-picker-cell-range-hover::before, .compare-date-picker .ant-picker-cell-range-hover-end::before {
    background-color: hsla(33, 100%, 49%, 0.5) !important;
}

#date-popup > div {
    position: relative !important;
}

.rotate-tag {
    transition: all 200ms linear;
    cursor: pointer;
}

.rotate-tag-parent:hover .rotate-tag {
    -webkit-transform: rotate(144deg);
    -moz-transform: rotate(144deg);
    -o-transform: rotate(144deg);
    -ms-transform: rotate(144deg);
    transform: rotate(144deg);
}

.rotated {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.rotated-180 {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.rt-tbody {
    overflow: visible !important;
}

.first-col-sticky {
    position: sticky !important;
    left: 0;
    top: 0;
    z-index: 1;
}

.basic-table .tr :last-child .td {
  border-bottom: 0;
}
.basic-table .th,
.basic-table .td {
  padding: 0px;
  /* border-bottom: 4px solid #fff; */
  border-right: 4px solid #fff;
  background-color: #fff;
  overflow: hidden;
  border-bottom: 0;
}

.basic-table .th > div {
    padding: 10px 15px;
    border: 1px solid rgb(234, 233, 255);
    white-space: nowrap;
    color: rgb(75, 75, 126);
    background-color : rgb(239, 241, 250);
}

.basic-table .td > div {
  width: 100%;
  height: 100%;
  padding: 10px 15px;
  border: 1px solid rgb(234, 233, 255);
  white-space: nowrap;

  margin-top: 0px;
  position: relative;
  height: 26px;
  box-sizing: border-box;
  background: rgb(250, 251, 254);
  border-radius: 2px;
  padding: 6px 8px;
  font-weight: 400;
  font-size: 13px;
  line-height: 18px;
  color: rgb(108, 108, 156);
  text-align: right;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
  border: 1px solid rgb(234, 233, 255);
  height: 100%;
}

.basic-table .priority-cell > div {
    color: rgb(75, 75, 126);
    background-color : rgb(239, 241, 250);
}

/* .basic-table .th :last-child,
.basic-table .td :last-child {
  border-right: 0;
} */
.basic-table.sticky {
  overflow: scroll;
}
.basic-table.sticky .header,
.basic-table.sticky .footer {
  position: -webkit-sticky;
  position: sticky;
  z-index: 1;
  width: fit-content;
}
.basic-table.sticky .header {
  top: 0;
  /* box-shadow: 0px 3px 3px #ccc; */
}
.basic-table.sticky .footer {
  bottom: 0;
  /* box-shadow: 0px -3px 3px #ccc; */
}
.basic-table.sticky .body {
  position: relative;
  z-index: 0;
}
.basic-table.sticky [data-sticky-td] {
  position: sticky;
}

.embedded-card-header {
    background-color : rgb(250, 251, 254) !important;
}

.embedded-card-header-title {
    color :rgb(108, 108, 156) !important;
}

.embedded-card {
    background-color: transparent !important;
    box-shadow: none !important;
    border: 1px solid rgb(234, 233, 255) !important;
    border-radius: 0 !important;
    font-weight: 500;
    font-size: 24px;
}

.blurBlock {
    filter: blur(5px);
    position: "relative" !important;
    pointer-events: none !important;
}

.center-aln {
    text-align: center !important;
}

.right-aln {
    text-align: right !important;
}

.cube-render {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-size: 16px;
  
    color-scheme: light dark;
    color: rgba(20, 20, 70, 1);
    background-color: #FFFFFF;
  
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .cube-render {
    margin: 0;
    padding: 16px;
  }
  

.rfm-container {
    display: grid;
    grid-template-rows: repeat(5, 1fr);
    grid-template-columns: repeat(5, 1fr);
    gap: 0px;
    width: 100%;
}

.recharts-wrapper svg{
    overflow: inherit !important;
}

.table-breakdown-default {
    font-weight: 400;
}

.card-title-default {
    letter-spacing: -0.02em !important;
    color: rgb(85, 85, 125) !important;
    font-weight: 500 !important;
    font-size: 18px !important;
    line-height: 27px !important;
}

.pivot-chart-box {
    border: 1px solid rgb(234, 233, 255);
    border-radius: 12px;
    padding: 0px;
    position: relative;
    padding-top: 0px !important;
}

.animate-blink {
    animation: blink 0.8s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}