/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./*.{js,jsx}", // For files in the root directory
    "./components/**/*.{js,jsx}", // For all JS and JSX files in components folder
    "./layouts/**/*.{js,jsx}", // For all JS and JSX files in layouts folder
    "./hooks/**/*.{js,jsx}", // For all JS and JSX files in hooks folder
    "./examples/**/*.{js,jsx}", // For all JS and JSX files in examples folder
    "./router/**/*.{js,jsx}", // For all JS and JSX files in router folder
    "./context/**/*.{js,jsx}", // For all JS and JSX files in context folder
    "./assets/**/*.{js,jsx}", // If you have any JS or JSX files in assets folder
    "./public/**/*.{js,jsx}", // If you have any JS or JSX files in public folder
  ],
  theme: {
    extend: {},
  },
  plugins: [
    require("tailwindcss-animate"), // make sure to "npm install tailwindcss-animate"
    require("@assistant-ui/react/tailwindcss")({
      components: ["thread"],
      components: ["assistant-modal"],
    }),
  ],
};
