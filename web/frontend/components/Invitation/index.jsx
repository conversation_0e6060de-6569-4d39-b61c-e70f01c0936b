import React, { useState } from "react";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import { axiosInstance, tracker, useMaterialUIController, fetchLoginConfig } from "@/context";
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";

export function Invitation({ t, pendingInvitation }) {
    const [controller, dispatch] = useMaterialUIController();
    const [loading, setLoading] = useState(false);

    const updateInvitation = (values) => {
        tracker.event(`Invitation Accepted / Rejected`);

        setLoading(true);
        axiosInstance
            .post("/api/update-invitation", values)
            .then(function (response) {
                setLoading(false);
                if (response && response.data.status) {
                    fetchLoginConfig(dispatch, true);
                }
            })
            .catch((err) => {
                console.log("error", err);
                setLoading(false);
            });
    };

    const formattedShopNames = pendingInvitation.shops.length > 1
        ? pendingInvitation.shops.slice(0, -1).map(shop => shop.name).join(", ") + " and " + pendingInvitation.shops[pendingInvitation.shops.length - 1].name
        : pendingInvitation.shops.length === 1
            ? pendingInvitation.shops[0].name
            : "";

    return (
        <BasicLayout image={bgImage}>
            <MDBox p={4}>
                <Card
                    m={4}
                    display='flex'
                    flexDirection='column'
                    alignItems='center'
                    justifyContent='center'
                    title={t("invitation-title")}
                    sx={{
                        width: "30vw",
                        maxWidth: "900px",
                        mx: "auto",
                        p: 3,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <CardContent>
                        <MDBox
                            p={4}
                            gap={4}
                        >
                            <MDTypography
                                variant="body1"
                                fontWeight="medium"
                                textAlign="center"
                                color="textPrimary"
                            >
                                {pendingInvitation.name}, you've been invited to join
                                <MDTypography component="span" fontWeight="bold" color="primary">
                                    {` ${pendingInvitation.inviter_name ? pendingInvitation.inviter_name : pendingInvitation.inviter_email}`}
                                </MDTypography>
                                's workspace on Datadrew. <br /><br />This will give you access to
                                <MDTypography component="span" fontWeight="bold" color="secondary">
                                    {` ${formattedShopNames}`}
                                </MDTypography>
                                .
                            </MDTypography>
                        </MDBox>

                        <MDBox display="flex" justifyContent="center" gap={2} mt={3}>
                            <MDButton
                                variant="outlined"
                                color="error"
                                size="large"
                                sx={{
                                    px: 4,
                                    borderRadius: "8px",
                                    fontWeight: "bold",
                                    textTransform: "none",
                                    borderWidth: "2px",
                                    "&:hover": { backgroundColor: "#ffebee", borderColor: "#d32f2f" }
                                }}
                                onClick={() => updateInvitation({
                                    invitation_id: pendingInvitation.invitation_id,
                                    status: "rejected"
                                })}
                            >
                                Reject
                            </MDButton>
                            <MDButton
                                variant="contained"
                                color="success"
                                size="large"
                                sx={{
                                    px: 4,
                                    borderRadius: "8px",
                                    fontWeight: "bold",
                                    textTransform: "none",
                                    "&:hover": { backgroundColor: "#2e7d32" }
                                }}
                                onClick={() => updateInvitation({
                                    invitation_id: pendingInvitation.invitation_id,
                                    status: "accepted"
                                })}
                            >
                                Accept
                            </MDButton>
                        </MDBox>
                    </CardContent>
                </Card>
            </MDBox>
        </BasicLayout>
    );
}

export default Invitation;
