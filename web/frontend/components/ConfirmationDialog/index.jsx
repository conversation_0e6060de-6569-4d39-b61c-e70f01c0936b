import React from 'react';
import Button from "@mui/material/Button";
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import MDButton from "@/components/MDButton";

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Box from "@mui/material/Box";
import { useTranslation } from 'react-i18next';

const ConfirmationDialog = NiceModal.create(({title, message, onConfirm, onCancel, confirmColor}) => {
  const modal = useModal();
  const {t} = useTranslation();
  return (
    <Dialog
        open={modal.visible}
        onClose={onCancel}
        width="300px"
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
      <DialogTitle>{title}</DialogTitle>
      {!!message && <DialogContent>{message}</DialogContent>}
        <DialogActions>
        <MDButton onClick={onCancel} size="small" fullWidth variant="outlined" color="secondary">
            {t("cancel")}
        </MDButton>
        <MDButton onClick={onConfirm} size="small" fullWidth autoFocus variant="gradient" color={!!confirmColor ? confirmColor : "error"}>
            {t("confirm")}
        </MDButton>
        </DialogActions>
    </Dialog>
  );
});

export default ConfirmationDialog;