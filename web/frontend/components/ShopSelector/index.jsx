import React from "react";

import TextField from '@mui/material/TextField';
import MDBox from "@/components/MDBox";
import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';
import MDTypography from "@/components/MDTypography";

export default class ShopSelector extends React.Component {
    render = () => {
        const { shopOptions, handleShopChange } = this.props;

        let optionList = shopOptions.map((s) => {
            return {label : s.name, value : s.myshopify_domain, active_flag: s.active_flag}
        })

        const filterOptions = createFilterOptions({
            matchFrom: 'any',
            stringify: (option) => option.label + ' ' + option.value,
        });

        return (
            <Autocomplete
                defaultValue={optionList[0]}
                onChange={(event, newValue) => {
                    if (newValue && newValue.value) {
                        handleShopChange(newValue.value);
                    } else {
                        handleShopChange(null);
                    }
                }}
                getOptionLabel={(option) => {
                    return option.label
                }}
                id="admin-shop-selection"
                blurOnSelect
                isOptionEqualToValue={(option, value) => {
                    return option.value === value.value;
                }}
                options={optionList}
                sx={{ width: 300 }}
                renderInput={(params) => <TextField {...params} />}
                renderOption={(props, option) => {
                    return (
                        <li {...props} key={option.value}>
                            <MDBox display="flex" flexDirection="column">
                                <MDTypography variant="button" color={option.active_flag ? "dark" : "error"}>{option.label}</MDTypography>
                                <MDTypography variant="caption" fontWeight="regular" color={option.active_flag ? "dark" : "error"}>{option.value}</MDTypography>
                            </MDBox>                            
                        </li>
                    );
                }}
                filterOptions={filterOptions}
            />
        );
    };
}