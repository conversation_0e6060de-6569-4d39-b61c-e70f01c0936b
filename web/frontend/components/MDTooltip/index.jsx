import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';

const MDTooltipRoot = ({ className, variant, title, description, logo, arrow, logos,  ...props }) => {

    if (variant === "basic") {
        return (<Tooltip {...props} title={title} arrow={arrow} classes={{ popper: className }} />)
    }

    let renderTitle = title
    let logoImages = logos || [logo]
    if (description) {
        renderTitle = (
            <MDBox display="flex" flexDirection="column" alignItems="flex-start">
                <MDBox display="flex" alignItems="center" flexDirection="row" mb={1.3}>
                    <>{logoImages.map((l, ind) => (<MDBox component="img" src={l} alt="shopify" width="1rem" mx={0.2} key={ind}/>))}</>
                    <MDTypography variant="button" fontWeight="regular" ml={0.4} fontSize="13.5px">
                        {title}
                    </MDTypography>
                </MDBox>
                <MDTypography align="left" variant="caption" color="secondary" fontWeight="medium" fontSize="12px">
                    {description}
                </MDTypography>
            </MDBox>
        )
    }

    return (
        <Tooltip arrow={arrow} title={renderTitle} classes={{ popper: className }} {...props}/>
    );
}

const MDTooltip = styled(MDTooltipRoot)(({ theme, variant }) => (
    (variant === "basic") ? {} : {
        [`& .${tooltipClasses.tooltip}`]: {
          backgroundColor: theme.palette.common.white,
          color: 'rgba(0, 0, 0, 0.87)',
          boxShadow: theme.shadows[1],
          maxWidth : "250px",
          fontSize: 13,
          padding: "15px 20px",
        },
        [`& .${tooltipClasses.arrow}`]: {
            display : "none"
        },
    }
));


export default MDTooltip;