import Grid from "@mui/material/Grid";


// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import {BarLoader, PropagateLoader} from "react-spinners";

const AppLoader = () => {
  return (
    <MDBox px={1} width="100%" height="100vh" mx="auto" bgColor="light">
        <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%">
            <BarLoader color="#1A73E8" />
        </Grid>
    </MDBox>
  );
}


export const DashboardLoader = ({height}) => {
  return (
    <MDBox px={1} width="100%" height={height ? height : "77vh"} mx="auto" bgColor="light">
        <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%">
          <BarLoader color="#1A73E8" />
        </Grid>
    </MDBox>
  )
}

export const CardLoader = ({height}) => {
  return (
    <MDBox px={1} width="100%" height={height ? height : "77vh"} mx="auto">
        <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%">
          <BarLoader color="#1A73E8" />
        </Grid>
    </MDBox>
  )
}

export default AppLoader;