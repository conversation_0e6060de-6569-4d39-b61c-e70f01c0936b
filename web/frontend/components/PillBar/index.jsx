import React, { Component } from "react";
import PillbarSelect from "./PillBarSelect";
import classnames from "classnames";
import MDTypography from "@/components/MDTypography";

export default class PillBar extends Component {
  constructor() {
    super();

    this.activePill = null;
    this.indicator = null;
    this.indicatorCenter = null;
    this.indicatorRight = null;

    this.options = null;

    this.state = {
      initialized: false,
    };
  }

  componentDidMount() {
    this.updateInidicatorPosition();
  }

  componentDidUpdate(pp) {
    if (pp.value !== this.props.value) {
      this.updateInidicatorPosition();
    }
  }

  updateInidicatorPosition() {
    if (this.props.value !== undefined) {
      this.setState({
        initialized: true,
      });
    }

    if (
      this.indicator &&
      this.activePill &&
      this.indicatorCenter &&
      this.indicatorRight
    ) {
      if (this.state.initialized) {
        this.indicator.style.transition = "0.2s cubic-bezier(0.87, 0, 0.13, 1)";
        this.indicatorCenter.style.transition =
          "0.2s cubic-bezier(0.87, 0, 0.13, 1)";
        this.indicatorRight.style.transition =
          "0.2s cubic-bezier(0.87, 0, 0.13, 1)";
      } else {
        this.indicator.style.transition = "none";
        this.indicatorCenter.style.transition = "none";
        this.indicatorRight.style.transition = "none";
      }

      this.indicator.style.transform = `translateX(${
        this.activePill.offsetLeft - 1
      }px)`;

      this.indicatorCenter.style.transform = `scaleX(${
        this.activePill.offsetWidth - 18
      })`;

      this.indicatorRight.style.transform = `translateX(${
        this.activePill.offsetWidth - 8
      }px)`;
    }
  }

  renderOptions() {
    const { name, options, onChange, disabled, tooltips, value } = this.props;
    return options.map((o) => (
      <label
        ref={(ref) => {
          if (o.value === value) this.activePill = ref;
        }}
        key={o.value}
        className={classnames("pill", {
          active: o.value === value,
          disabled: disabled && disabled.indexOf(o.value) > -1,
        })}
      >
        <input
          type="radio"
          name={name}
          value={o.value}
          checked={o.value === value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled && disabled.indexOf(o.value) > -1}
        />
        <div className="pill-content">
          <MDTypography variant="inherit" textTransform="uppercase">
            {o.label}
          </MDTypography>
        </div>
        {tooltips && tooltips[o.value] && (
          <div className="tooltip">
            <div className="tooltip-content">{tooltips[o.value]}</div>
          </div>
        )}
      </label>
    ));
  }

  render() {
    const {
      value,
      options,
      onChange,
      tooltips,
      disabled,
      isDropdown,
    } = this.props;
    const { initialized } = this.state;

    let selectedOption = undefined;
    for (var ok in options) {
      if (options[ok] && "value" in options[ok] && options[ok].value == value) {
        selectedOption = options[ok];
      }
    }

    if (isDropdown) {
      return (
        <PillbarSelect
          value={selectedOption}
          options={options.map((o) => ({
            ...o,
            tooltip: tooltips && tooltips[o.value],
            disabled: disabled && disabled.indexOf(o.value) > -1,
          }))}
          className="select-style-1 with-background"
          classNamePrefix="select-style-1"
          onChange={(option) => onChange(option.value)}
          isOptionDisabled={(option) => option.disabled}
        />
      );
    }

    return (
      <div className="pillbar">
        {value !== null && value !== undefined && (
          <div
            ref={(ref) => (this.indicator = ref)}
            className={classnames("pillbar-indicator", {
              initialized: initialized,
            })}
          >
            <div className="pillbar-indicator-left"></div>
            <div
              ref={(ref) => (this.indicatorCenter = ref)}
              className="pillbar-indicator-center"
            ></div>
            <div
              className="pillbar-indicator-right"
              ref={(ref) => (this.indicatorRight = ref)}
            ></div>
          </div>
        )}
        {this.renderOptions()}
      </div>
    );
  }
}
