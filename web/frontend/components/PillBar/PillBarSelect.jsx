import React from 'react';
import Select, { components } from 'react-select';
import AsyncSelect from 'react-select/async';
import AsyncCreatableSelect from 'react-select/async-creatable';
import MDTypography from '@/components/MDTypography';

import MDBox from '@/components/MDBox';
import premiumTag from "@/assets/images/premium-tag.png";

const Menu = (props) => {
  if (props.options.length) {
    return <components.Menu {...props}></components.Menu>;
  }

  return null;
};

const ClearIndicator = (props) => {
  return (
    <components.ClearIndicator {...props}>
      <svg
        height="12"
        width="12"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g fill="#2e294e" stroke="#2e294e" strokeOpacity="0.4" strokeWidth="1">
          <line
            fill="none"
            stroke="#2e294e"
            strokeLinecap="round"
            x1="1.5"
            x2="10.5"
            y1="1.5"
            y2="10.5"
          />
          <line
            fill="none"
            stroke="#2e294e"
            strokeLinecap="round"
            x1="10.5"
            x2="1.5"
            y1="1.5"
            y2="10.5"
          />
        </g>
      </svg>
    </components.ClearIndicator>
  );
};

const DropdownIndicator = (props) => {
  return (
    <components.DropdownIndicator {...props}>
      <svg
        height="12"
        width="12"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g fill="#2e294e" stroke="#2e294e" strokeOpacity="0.4" strokeWidth="1">
          <polyline
            fill="none"
            points="0.5 4.5 6 8.5 11.5 4.5"
            stroke="#2e294e"
            strokeLinecap="round"
          />
        </g>
      </svg>
    </components.DropdownIndicator>
  );
};

const Option = (props) => {
  const { data } = props;

  if (!!data.isPremiumOption) {
    return (
        <MDTypography variant="button" fontWeight="regular" textTransform="capitalize" color="dark" width="100%">
          <components.Option {...props}>
          <MDBox component="img" src={premiumTag} alt="premium" width="1rem" mr={0.8} mb={-0.3}/>
            {data.label}
          </components.Option>
        </MDTypography>
    )
  }

  return (<MDTypography variant="button" fontWeight="regular" textTransform="capitalize" color="dark">
    <components.Option {...props}></components.Option>
    </MDTypography>)
};

const PillbarSelect = (props) => {
  const customStyles = {
    control: (provided, state) => {
      let borderColor = state.isFocused ? '#00234c' : '#d5d8de';
      borderColor = props.touched && props.error ? '#ea4335' : borderColor;
      return {
        ...provided,
        minHeight: props.size === 'xl' ? 45 : provided.minHeight,
        fontSize: props.size === 'xl' ? 15 : provided.fontSize,
        borderRadius: 2,
        borderColor: borderColor,
        boxShadow: undefined,
        '&:hover': {
          borderColor: borderColor,
        },
      };
    },
    menuPortal: (base) => ({ ...base, zIndex: 99999 }),
    ...props.styles,
  };

  if (props.virtualized && !props.isMulti) {
    console.log('virtualized Select not supported')
  }

  if (props.async && props.creatable) {
    return (
      <AsyncCreatableSelect
        styles={customStyles}
        {...props}
        components={{
          DropdownIndicator,
          ClearIndicator,
          Menu,
          ...props.components,
        }}
      />
    );
  }

  if (props.async) {
    return (
      <AsyncSelect
        styles={customStyles}
        {...props}
        components={{
          DropdownIndicator,
          ClearIndicator,
          ...props.components,
        }}
      />
    );
  }

  return (
    <Select
      styles={customStyles}
      {...props}
      isSearchable={false}
      menuPortalTarget={document.body}
      components={{
        DropdownIndicator,
        ClearIndicator,
        Option,
        ...props.components,
      }}
    />
  );
};


export default PillbarSelect;