
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { useTranslation } from "react-i18next";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { useMaterialUIController } from "@/context";

const FeatureBlurBox = ({feature, children}) => {
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;

    if (!feature) {
        return children;
    }

    // check if upgrade is needed to access the feature
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[feature] ?? false);

    if (!isSubscriptionInActive) {
        return children;
    }

    return (
        <MDBox
            sx={{
                filter: "blur(5px)",
                pointerEvents: "none !important",
                backgroundColor: "transparent",
                zIndex: 2
            }}
        >
            {/* <MDButton variant="contained" color="info" size="small" onClick={() => {
                tracker.event("Paywall", {type: "benchmarks"});
                NiceModal.show(PaywallDialog, {type : ""})
            }}>
                {t("start-trial-get-access")}
                <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={1} />
            </MDButton> */}
            {children}
        </MDBox>
    );
}

export default FeatureBlurBox;