import React, { useEffect, useState } from 'react';
import Confetti from 'react-confetti';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import MDTypography from '@/components/MDTypography';
import MDButton from "@/components/MDButton";
import Icon from '@mui/material/Icon';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import MDBox from "@/components/MDBox";
import { useTranslation } from 'react-i18next';
import VerifiedIcon from '@mui/icons-material/Verified';
import CancelIcon from '@mui/icons-material/Cancel';
import { useNavigate } from 'react-router-dom';
import { useWindowSize } from 'react-use';

const SubscriptionCallbackDialog = NiceModal.create(({ mt }) => {
  const modal = useModal();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { width, height } = useWindowSize(); // To make confetti responsive
  const [showConfetti, setShowConfetti] = useState(false);

  // Show confetti effect on successful events
  useEffect(() => {
    if (modal.visible && mt !== "suberr") {
      setShowConfetti(true);
      setTimeout(() => setShowConfetti(false), 4000); // Hide confetti after 3 seconds
    }
  }, [modal.visible, mt]);

  let title = t("plan-active");
  let description = t("plan-active-desc2");
  let description_head = t("plan-active-desc-heading");
  let description_li1 = t("plan-active-desc-li1");
  let description_li2 = t("plan-active-desc-li2");
  let btnText2 = t("start-exploring");
  let btnText = t("book-my-call");
  let btnIcon = <Icon sx={{ fontWeight: "bold" }}>arrow_forward</Icon>;
  let color = "success";
  let titleIcon = <VerifiedIcon color={color} sx={{ fontSize: "80px !important" }} />;

  if (mt === "suberr") {
    color = "error";
    titleIcon = <CancelIcon color={color} sx={{ fontSize: "80px !important" }} />;
    title = t("failed");
    description = t("something-went-wrong-di2");
    description_head = t("something-went-wrong-head");
    description_li1 = t("something-went-wrong-li1");
    description_li2 = t("something-went-wrong-li2");
    btnText = t("ok");
    btnIcon = null;
  }

  return (
    <>
      {/* Show confetti if enabled */}
      {showConfetti && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 9999, // Ensures confetti is in front of everything
            pointerEvents: 'none', // Prevents interaction blocking
          }}
        >
          <Confetti width={width} height={height} numberOfPieces={300} recycle={false} />
        </div>
      )}
      
      <Dialog
        open={modal.visible}
        onClose={modal.hide}
        TransitionProps={{
          onExited: () => modal.remove(),
        }}
      >
        <MDBox p={2} bgColor="white" variant="gradient">
          <DialogContent>
            <MDBox display="flex" justifyContent="center" alignItems="center" my={1}>
              {titleIcon}
            </MDBox>
            <MDTypography color={color} variant="h4" textAlign="center" mt={3} mb={2}>
              {title}
            </MDTypography>

            <MDTypography color="dark" variant="body2" textAlign="center" mt={2} fontWeight="regular">
              {description}
            </MDTypography>

            <MDTypography color="dark" variant="h6" textAlign="left" mt={3} mb={1} sx={{ fontWeight: "normal" }}>
              {description_head}
            </MDTypography>

            <MDBox component="ul" pl={4} mt={1} sx={{ listStylePosition: "outside" }}>
              <MDBox component="li" color="dark" variant="body2" fontWeight="regular" mb={1}>
                {description_li1}
              </MDBox>
              <MDBox component="li" color="dark" variant="body2" fontWeight="regular">
                {description_li2}
              </MDBox>
            </MDBox>
          </DialogContent>

          <DialogActions
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              gap: "8px",
              width: "100%",
            }}
          >
            <MDButton
              onClick={modal.hide}
              fullWidth
              size="medium"
              variant="contained"
              color="info"
              sx={{
                color: "#fff",
                padding: "12px 20px",
                fontSize: "16px",
                fontWeight: "bold",
                borderRadius: "10px",
                transition: "0.3s ease",
                boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                "&:hover": {
                  boxShadow: "0px 6px 10px rgba(0, 0, 0, 0.2)",
                },
              }}
            >
              {btnText} &nbsp; {btnIcon}
            </MDButton>

            {mt !== "suberr" && (
              <MDButton
                onClick={() => {
                  modal.hide();
                  navigate('/');
                }}
                fullWidth
                size="medium"
                variant="outlined"
                sx={{
                  flex: 1,
                  border: "2px solid",
                  borderColor: "secondary.main",
                  color: "secondary.main",
                  padding: "8px 16px",
                  fontSize: "14px",
                  fontWeight: "bold",
                  borderRadius: "8px",
                  height: "45px",
                  transition: "0.3s ease",
                  "&:hover": {
                    background: "rgba(156, 39, 176, 0.1)",
                    borderColor: "secondary.dark",
                    color: "secondary.dark",
                  },
                }}
              >
                {btnText2}
              </MDButton>
            )}
          </DialogActions>
        </MDBox>
      </Dialog>
    </>
  );
});

export default SubscriptionCallbackDialog;