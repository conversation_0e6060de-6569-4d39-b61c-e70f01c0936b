import React from "react";
import PropTypes from "prop-types";
import FixedBox from "./FixedBox";
import {useTranslation} from "react-i18next";

function XLabels({ labels, originNode, prependXLabels, appendXLabels, width, labelsVisibility, squares, height, yWidth, xLabelsBottom }) {
    const {t} = useTranslation();

    if (!!xLabelsBottom && xLabelsBottom.length != 0) {
        return (
            <div style={{ display: "flex" }}>
                <FixedBox width={yWidth} underline>
                    <div
                    style={{
                        height,
                        border: "1px solid rgb(0,0,0,0.05)",
                        zIndex:10,
                        backgroundColor:"white",
                        fontWeight: "500",
                        position: "absolute",
                        textAlign: "center",
                        paddingRight: "5px",
                        paddingTop: `${height / 3.7}px`,
                        width: `${yWidth}px`
                    }}
                    >
                    {t("average")}
                    </div>
                </FixedBox>
                {[...prependXLabels].map((x, i) => (
                    <div
                    key={i}
                    style={{
                        height,
                        border: "1px solid rgb(0,0,0,0.05)",
                        paddingTop: `${height / 3.7}px`,
                        width: squares ? `${height}px` : undefined,
                        flex: squares ? "none" : 1,
                        textAlign: "center",
                        visibility:"visible"
                    }}
                    >
                    </div>
                ))}

                {[...appendXLabels].map((x, i) => (
                    <div
                    key={i}
                    style={{
                        height,
                        border: "1px solid rgb(0,0,0,0.05)",
                        paddingTop: `${height / 3.7}px`,
                        width: squares ? `${height}px` : undefined,
                        flex: squares ? "none" : 1,
                        textAlign: "center",
                        visibility:"visible"
                    }}
                    >
                    </div>
                ))}

                {[...xLabelsBottom].map((x, i) => (
                    <div
                    key={i}
                    style={{
                        height,
                        border: "1px solid rgb(0,0,0,0.05)",
                        paddingTop: `${height / 3.7}px`,
                        width: squares ? `${height}px` : undefined,
                        flex: squares ? "none" : 1,
                        textAlign: "center",
                        visibility:"visible"
                    }}
                    >
                    {x}
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div style={{ display: "flex" }}>
            <FixedBox width={yWidth} underline >
                <div style={{
                        height: 2*height,
                        borderLeft: "1px solid rgb(0,0,0,0.05)",
                        borderRight: "1px solid rgb(0,0,0,0.05)",
                        borderTop: "1px solid rgb(0,0,0,0.05)",
                        zIndex:10,
                        backgroundColor:"white",
                        position: "absolute",
                        textAlign: "center",
                        paddingRight: "5px",
                        paddingTop: `${height / 3.7}px`,
                        width: `${yWidth}px`
                    }}>
                    {originNode}
                </div>
            </FixedBox>
            {[...prependXLabels, ...appendXLabels, ...labels].map((x, i) => (
                <div
                key={i}
                style={{
                    height,
                    flex: squares ? "none" : 1,
                    textAlign: "center",
                    borderLeft: "1px solid rgb(0,0,0,0.05)",
                    borderRight: "1px solid rgb(0,0,0,0.05)",
                    borderTop: "1px solid rgb(0,0,0,0.05)",
                    // paddingTop: `${height / 3.7}px`,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    width: squares ? `${height + 1}px` : width,
                    visibility:
                    labelsVisibility && !labelsVisibility[i] ? "hidden" : "visible",
                }}
                >
                    <div style={{ fontWeight: "500", marginBottom: "5px" }}>{x}</div>
                </div>
            ))}
        </div>
  );
}

XLabels.propTypes = {
  labels: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.object])
  ).isRequired,
  labelsVisibility: PropTypes.arrayOf(PropTypes.bool),
  width: PropTypes.number.isRequired,
  squares: PropTypes.bool,
  height: PropTypes.number,
};

XLabels.defaultProps = {
  labelsVisibility: null,
  squares: false,
  height: 30,
};

export default XLabels;

