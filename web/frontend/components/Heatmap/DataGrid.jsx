import React from "react";
import PropTypes from "prop-types";
import FixedBox from "./FixedBox";
import {useTranslation} from "react-i18next";

const DataGrid = ({
  xLabels,
  yLabels,
  data,
  xLabelWidth,
  yLabelWidth,
  background,
  height,
  yLabelTextAlign,
  unit,
  displayYLabels,
  onClick,
  cursor,
  squares,
  cellRender,
  cellStyle,
  title,
  prependTable,
  appendTable,
  totalRow
}) => {
  const {t} = useTranslation();
  const flatArray = data.reduce((i, o) => [...o, ...i], []);
  const max = Math.max(...flatArray);
  const min = Math.min(...flatArray);

  var renderTableColumn = (yi, columnData, ci) => {
        let tableCStyle = {
            height,
            width: squares ? `${height}px` : undefined,
            flex: squares ? "none" : 1,
            textAlign: "center",
            filter: (columnData[yi].is_blur ?? false) ? "blur(3px)" : 'none',
            userSelect: (columnData[yi].is_blur ?? false) ? "none" : 'initial',
            border: "1px solid rgb(0,0,0,0.05)"
        }

        return (
            <div className="table-cell" style={tableCStyle} key={`table_${ci}_${yi}`} >
                <div style={{ paddingTop: `${height / 5}px` }}>
                    {columnData[yi].val ?? '-'}
                </div>
            </div>
        )
    }

  return (
    <div>
      {yLabels.map((y, yi) => (
        <div key={yi} style={{ display: "flex" }}>
          <FixedBox width={yLabelWidth} underline>
            <div
              style={{
                position: "absolute",
                textAlign: yLabelTextAlign,
                border: "1px solid rgb(0,0,0,0.05)",
                height,
                zIndex:10,
                backgroundColor:"white",
                paddingRight: "5px",
                paddingTop: `${height / 5}px`,
                width: `${yLabelWidth}px`
              }}
            >
              {displayYLabels && y}
            </div>
          </FixedBox>
          {prependTable.map(renderTableColumn.bind(this, yi))}
          {appendTable.map(renderTableColumn.bind(this, yi))}
          {xLabels.map((x, xi) => {
            const value = data[yi][xi];
            const style = Object.assign(
              {
                cursor: `${cursor}`,
                border: "1px solid white",
                height,
                width: squares ? `${height}px` : undefined,
                flex: squares ? "none" : 1,
                textAlign: "center",
              },
              cellStyle(background, value, min, max, data, xi, yi)
            );
            return (
              <div
                className="heatmap-cell"
                onClick={onClick.bind(this, xi, yi)}
                title={title(value, unit, xi, yi)}
                key={`${xi}_${yi}`}
                style={style}
              >
                <div style={{ paddingTop: `${height / 5}px` }}>
                  {cellRender(value, xi, yi)}
                </div>
              </div>
            );
          })}
        </div>
      ))}
        {totalRow.length > 0 && <div style={{ display: "flex" }}>
          <FixedBox width={yLabelWidth} underline>
            <div
              style={{
                position: "absolute",
                textAlign: yLabelTextAlign,
                paddingRight: "5px",
                height,
                border: "1px solid rgb(0,0,0,0.05)",
                zIndex:10,
                backgroundColor:"white",
                fontWeight: "500",
                paddingTop: `${height / 5}px`,
                width: `${yLabelWidth}px`
              }}
            >
             {t("total")}
            </div>
          </FixedBox>
          {totalRow.map((x, xi) => {
            const style = {
                height,
                border: "1px solid rgb(0,0,0,0.05)",
                width: squares ? `${height}px` : undefined,
                flex: squares ? "none" : 1,
                textAlign: "center",
            };
            if (x == "") {
                style.visibility = "hidden"
            }
            return (
              <div
                key={`${xi}_${"sum"}`}
                style={style}
              >
                <div style={{ paddingTop: `${height / 5}px` }}>
                    {x}
                </div>
              </div>
            );
          })}
        </div>}
    </div>
  );
};

DataGrid.propTypes = {
  xLabels: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.object])
  ).isRequired,
  yLabels: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.object])
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.array).isRequired,
  background: PropTypes.string.isRequired,
  height: PropTypes.number.isRequired,
  xLabelWidth: PropTypes.number.isRequired,
  yLabelWidth: PropTypes.number.isRequired,
  yLabelTextAlign: PropTypes.string.isRequired,
  unit: PropTypes.string.isRequired,
  displayYLabels: PropTypes.bool,
  onClick: PropTypes.func,
  cursor: PropTypes.string,
  squares: PropTypes.bool,
  cellRender: PropTypes.func.isRequired,
  cellStyle: PropTypes.func.isRequired,
  title: PropTypes.func
};

DataGrid.defaultProps = {
  displayYLabels: true,
  cursor: "",
  onClick: () => {},
  squares: false,
  title: (value, unit) => (value || value === 0) && `${value} ${unit}`
};

export default DataGrid;
