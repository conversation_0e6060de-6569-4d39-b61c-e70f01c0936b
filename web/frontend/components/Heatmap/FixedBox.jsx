import React from "react";
import PropTypes from "prop-types";

const FixedBox = ({ children, width, underline }) => {
  let style = { flex: `0 0 ${width}px`}
  return <div style={style}> {children} </div>;
};

FixedBox.defaultProps = {
  children: " ",
};

FixedBox.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  width: PropTypes.number.isRequired,
};

export default FixedBox;

