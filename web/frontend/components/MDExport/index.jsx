import React, {useState} from "react";
import { useCancellableAxios, tracker } from "@/context";
import PropTypes from "prop-types";
// Material Dashboard 2 PRO React components
import Icon from "@mui/material/Icon";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

import {useMaterialUIController} from "@/context";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import axios from "axios";



const FileCSVIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" id="csv" width="30" height="30">
            <path fill="#eee" d="M55 18H42V5a1 1 0 0 0-1-1H13a1 1 0 0 0-1 1v28a1 1 0 0 0 1 1h27v16H13a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h42a1 1 0 0 0 1-1V19a1 1 0 0 0-1-1Z"></path>
            <rect width="34" height="20" x="8" y="32" fill="#66bb6a" rx="1" ry="1"></rect>
            <path fill="#e0e0e0" d="m55.71 18.29-14-14A1 1 0 0 0 40 5v14a1 1 0 0 0 1 1h14a1 1 0 0 0 .71-1.71Z"></path>
            <path fill="#fff" d="M19 43.32h1.8a3.15 3.15 0 0 1-.6 1.52 3.22 3.22 0 0 1-1.2.96 3.86 3.86 0 0 1-1.61.35A3.58 3.58 0 0 1 14.62 45a4.19 4.19 0 0 1-1.08-3 4.29 4.29 0 0 1 1.06-3 3.47 3.47 0 0 1 2.69-1.18A3.52 3.52 0 0 1 20 39a3.43 3.43 0 0 1 .79 1.76H19a1.57 1.57 0 0 0-.59-.88 1.91 1.91 0 0 0-1.1-.31 1.86 1.86 0 0 0-1.49.68 2.66 2.66 0 0 0-.56 1.75 2.6 2.6 0 0 0 .58 1.75 1.88 1.88 0 0 0 1.5.67 1.66 1.66 0 0 0 1.66-1.1zm3.35.3H24a1.37 1.37 0 0 0 1.49 1.08 1.42 1.42 0 0 0 .88-.25.8.8 0 0 0 .33-.67.77.77 0 0 0-.55-.78q-.11 0-1-.3a5.89 5.89 0 0 1-1.78-.7 2 2 0 0 1-.82-1.7 2.27 2.27 0 0 1 .75-1.78 2.85 2.85 0 0 1 2-.67 3.15 3.15 0 0 1 2 .62 2 2 0 0 1 .82 1.6h-1.67a1 1 0 0 0-1.13-.78 1.25 1.25 0 0 0-.77.22.71.71 0 0 0-.29.59.65.65 0 0 0 .36.62 5.64 5.64 0 0 0 1.25.36 4.82 4.82 0 0 1 1.58.56 2.17 2.17 0 0 1 1 1.91 2.38 2.38 0 0 1-1.13 2.12 3.41 3.41 0 0 1-1.87.46A3 3 0 0 1 22.83 45a2.9 2.9 0 0 1-.48-1.38zM32.26 46l-3-8h1.79L33 43.4l1.67-5.4h1.75l-2.65 8z"></path>
        </svg>
    )
}

const FileXLSIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 48 48">
            <path fill="#169154" d="M29,6H15.744C14.781,6,14,6.781,14,7.744v7.259h15V6z"></path>
            <path fill="#18482a" d="M14,33.054v7.202C14,41.219,14.781,42,15.743,42H29v-8.946H14z"></path>
            <path fill="#0c8045" d="M14 15.003H29V24.005000000000003H14z"></path>
            <path fill="#17472a" d="M14 24.005H29V33.055H14z"></path>
            <g>
                <path fill="#29c27f" d="M42.256,6H29v9.003h15V7.744C44,6.781,43.219,6,42.256,6z"></path>
                <path fill="#27663f" d="M29,33.054V42h13.257C43.219,42,44,41.219,44,40.257v-7.202H29z"></path>
                <path fill="#19ac65" d="M29 15.003H44V24.005000000000003H29z"></path>
                <path fill="#129652" d="M29 24.005H44V33.055H29z"></path>
            </g>
            <path fill="#0c7238" d="M22.319,34H5.681C4.753,34,4,33.247,4,32.319V15.681C4,14.753,4.753,14,5.681,14h16.638 C23.247,14,24,14.753,24,15.681v16.638C24,33.247,23.247,34,22.319,34z"></path><path fill="#fff" d="M9.807 19L12.193 19 14.129 22.754 16.175 19 18.404 19 15.333 24 18.474 29 16.123 29 14.013 25.07 11.912 29 9.526 29 12.719 23.982z">
            </path>
        </svg>
    )
}

const CaretDownIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="#344767"
            viewBox="0 0 256 256">
            <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,48,88H208a8,8,0,0,1,5.66,13.66Z"></path>
        </svg>
    )
}


const MDExport = ({ feature, formats, options }) => {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig, loginConfig} = controller;
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();
    const [loading, setLoading] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);

    let isExportInActive = false;
    if (!!feature) {
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[feature] ?? false);
    }

    const handleExport = (selectedFormat) => {
        if (loading) {
            return;
        }

        let shouldAllow = loginConfig.allow_customer_export ?? false
        if (!shouldAllow) {
            tracker.event("Access Denied", {type: "export", feature});
            toast.error("Access denied. Please contact support.")
            return 
        }

        if (isExportInActive) {
            tracker.event("Paywall", { feature });
            NiceModal.show(PaywallDialog, {feature })
            return false
        }

        let reqData = {
            format: selectedFormat,
            type: options.type,
            parameters: options.parameters
        };

        tracker.event("Data Exported", {...reqData, feature, ...options});

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setLoading(true);
        axiosInstance
            .get("/api/data/export", { params: reqData, responseType: "blob" })
            .then(function (response) {
                setLoading(false);
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement("a");
                link.href = url;

                let fileName = "data";
                if (options.fileName) {
                    fileName = options.fileName;
                }

                link.setAttribute("download", fileName + "." + selectedFormat);
                document.body.appendChild(link);
                link.click();
            })
            .catch((err) => {
                setLoading(false);
                console.log("error", err);
                !axios.isCancel(err) && toast.error(t("something-went-wrong"));
            });
    };

    const handleMenuOpen = (event) => {
        if (loading) {
            return;
        }
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleMenuExport = (format) => {
        setAnchorEl(null);
        handleExport(format);
    }


    return (
        <MDBox mx={0.5}>
            {formats.length == 1 && <MDButton variant="outlined" color="dark" onClick={() => handleExport(formats[0] ?? "csv")} size="small">
                {loading ? <CircularProgress size={16} color="dark" /> : (
                    <Icon size="medium"> description</Icon>
                )}
                &nbsp;{t("export")}
                {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
            </MDButton>}
            {formats.length > 1 && <MDButton
                variant="outlined"
                color="dark"
                size="small"
                onClick={handleMenuOpen}
                display="flex"
                alignItems="center"
                justifyContent="center"
                sx={{
                    '&:focus': {
                        boxShadow: "none !important"
                    }
                }}
            >
                {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" mr={0.8} />}
                {loading && <CircularProgress size={16} color="dark" />}
                {!loading && <MDBox> {t("export")} &nbsp; </MDBox>}
                {!loading && <CaretDownIcon />}
            </MDButton>}
            {formats.length > 1 && <Menu
                anchorEl={anchorEl}
                anchorReference={null}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
                keepMounted
                open={Boolean(anchorEl)}
                onClose={handleClose}
                sx={{ mt: 1, width: "100%", backgroundColor: "grey-100" }}
            >
                {formats.includes("csv") && (
                    <MenuItem divider={true} key={"csv"} onClick={() => handleMenuExport("csv")} color="secondary" borderRadius="md" sx={{fontSize: "0.8rem"}}>
                        <FileCSVIcon /> &nbsp; {t("export-to-csv")}
                    </MenuItem>
                )}
                {formats.includes("xlsx") && (
                    <MenuItem key={"xlsx"} onClick={() => handleMenuExport("xlsx")} color="secondary" borderRadius="md" sx={{fontSize: "0.8rem"}}>
                        <FileXLSIcon /> &nbsp; {t("export-to-xlsx")}
                    </MenuItem>
                )}
            </Menu>}
        </MDBox>
    );
}

// options.fileName must be set without extension
// options.type must be set 
// options.parameters must be set
MDExport.propTypes = {
    options: PropTypes.shape({
      fileName: PropTypes.string,
      type: PropTypes.string.isRequired,
      parameters: PropTypes.object.isRequired,
    }).isRequired,
    formats: PropTypes.arrayOf(PropTypes.oneOf(['xlsx', 'csv'])),
    feature: PropTypes.string.isRequired,
    disabled: PropTypes.bool,
};
  
MDExport.defaultProps = {
    options: {},
    formats: ['csv'],
    disabled: false,
};
  

export default MDExport;