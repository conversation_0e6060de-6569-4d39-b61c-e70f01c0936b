import React, { useEffect } from 'react';
import PillBar from '@/components/PillBar';
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek);

import {tracker} from "@/context";

export const relativeGroups = {
    // "hour" : {
    //     short: "hr",
    //     granularity: "hour",
    //     full : "Hour",
    //     full_label : "hourly",
    //     row_name : (dt) => dt.format("DD MMM HH:mm"),
    //     format : (dt) => dt.format("YYYY-MM-DD HH"),
    //     add : (dt) => dt.add(1, 'hour'),
    // },
    "day" : {
        short: "day",
        granularity: "day",
        full : "Daily",
        full_label : "daily",
        row_name : (dt) => dt.format("DD MMM YYYY"),
        row_name_short : (dt) => dt.format("DD MMM"),
        format : (dt) => dt.format("YYYY-MM-DD"),
        add : (dt) => dt.add(1, 'day'),
    },
    "week" : {
        short: "wk",
        granularity: "week",
        full : "Week",
        full_label : "weekly",
        row_name : (dt) => dt.isoWeekday(1).format(`Wk ${dt.isoWeek()}: DD MMM`),
        row_name_short : (dt) => `${dt.isoWeekday(1).format(`DD MMM`)}`,
        format : (dt) => dt.isoWeekday(1).format("YYYY-MM-DD"),
        add : (dt) => dt.add(1, 'week'),
    },
    "month" : {
        short: "mo",
        granularity: "month",
        full : "Month",
        full_label : "monthly",
        row_name : (dt) => dt.format("MMM YYYY"),
        row_name_short : (dt) => `${dt.format(`MMM`)}`,
        format : (dt) => dt.format("YYYY-MM"),
        add : (dt) => dt.add(1, 'month'),
    },
    "quarter" : {
        short: "qtr",
        granularity: "quarter",
        full : "Quarter",
        full_label : "quarterly",
        row_name : (dt) => [
            `Jan-Mar ${dt.format("YYYY")}`,
            `Apr-Jun ${dt.format("YYYY")}`,
            `Jul-Sept ${dt.format("YYYY")}`,
            `Oct-Dec ${dt.format("YYYY")}`,
        ][dt.quarter()-1],
        row_name_short : (dt) => `Q${dt.quarter()}`,
        format : (dt) => `${dt.format("YYYY")}-${String("0" + dt.quarter()).slice(-2)}`,
        add : (dt) => dt.add(1, 'quarter'),
    },
    "year" : {
        short: "yr",
        granularity: "year",
        full : "Year",
        full_label : "yearly",
        row_name : (dt) => dt.format("YYYY"),
        row_name_short : (dt) => dt.format("YYYY"),
        format : (dt) => dt.format("YYYY"),
        add : (dt) => dt.add(1, 'year'),
    }
}


export function createTimeFrameBuckets(start_date, end_date, time_frame) {

    var handler = relativeGroups[time_frame]
    var bucketsMap = {}

    var currentDate = dayjs(start_date)
	var endDate = dayjs(end_date)
    let index = 0;
	while (handler.format(currentDate) <= handler.format(endDate)) {
        let key = handler.format(currentDate)
        bucketsMap[key] = {
            start : dayjs(currentDate).format("YYYY-MM-DD"),
            id : handler.format(currentDate),
            name : handler.row_name(currentDate),
            index : index
        }
		currentDate = handler.add(currentDate)
        index++
	}

	return bucketsMap
}

import {useMaterialUIController, setSelectedFilters} from "@/context";
import { useTranslation } from 'react-i18next';

export function RelativeGroupingFilter(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedFilters} = controller;
    const {time_frame} = selectedFilters;
    const {t} = useTranslation();
    let options = Object.keys(relativeGroups).map((tf) => {
        return {
            label : t(relativeGroups[tf].full_label),
            value : tf
        }
    });

    let exclude_list = props.exclude ?? []
    options = options.filter((opt) => !exclude_list.includes(opt.value))

    useEffect(() => {
        if (exclude_list.includes(time_frame)) {
            // reset to month if the current time frame is excluded
            setSelectedFilters(dispatch, {time_frame : "month"})
        }
    }, [time_frame, exclude_list])


    let handleChange = (tf) => {
        tracker.mixpanel.track("Switch Grouping", {
            report: (props.report ?? "unknown"),
            old_value: time_frame,
            new_value: tf
        })
        setSelectedFilters(dispatch, {time_frame : tf})
    }

    return (
        <div style={{display:"flex"}}>
            <PillBar
                name="type"
                options={options}
                value={time_frame}
                onChange={handleChange}
            />
        </div>
    );
}
