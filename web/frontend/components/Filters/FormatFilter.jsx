import React from 'react';
import PillBar from '@/components/PillBar';

function getValueSymbol(use_currency, currency) {
    if (!use_currency || !currency)  {
        return "#"
    }

    var currency_symbols = {
        'USD': '$', // US Dollar
        'EUR': '€', // Euro
        'CRC': '₡', // Costa Rican Colón
        'GBP': '£', // British Pound Sterling
        'ILS': '₪', // Israeli New Sheqel
        'INR': '₹', // Indian Rupee
        'JPY': '¥', // Japanese Yen
        'KRW': '₩', // South Korean Won
        'NGN': '₦', // Nigerian Naira
        'PHP': '₱', // Philippine Peso
        'PLN': 'zł', // Polish Zloty
        'PYG': '₲', // Paraguayan <PERSON>
        'THB': '฿', // Thai Baht
        'UAH': '₴', // Ukrainian Hryvnia
        'VND': '₫', // Vietnamese Dong
    };

    if(currency_symbols[currency] !== undefined) {
        return currency_symbols[currency]
    } else {
        return currency
    }
}

export default class FormatFilter extends React.Component {
    constructor(props) {
        super(props);
    }

    handleFormatChange = (e) => {
        return this.props.handleFormatChange(e.target.value)
    }

    render() {
        const  {format, currency, use_currency, handleFormatChange} = this.props

        const formats = {
            "value" : {
                short: getValueSymbol(use_currency, currency)
            },
            "percentage" : {
                short: "%"
            }
        }

        let options = Object.keys(formats).map((tf) => {
            return {
                label : formats[tf].short,
                value : tf
            }
        });

        return (
            <div style={{display:"flex"}}>
                <PillBar
                    name="type"
                    options={options}
                    value={format}
                    onChange={handleFormatChange}
                />
            </div>
        );
    }
}