import React from "react";
import {useTranslation} from "react-i18next";

export const metrics = {
    acc_total_sales_per_customer: {
        label: "m-cumrevpc-label",
        value: "acc_total_sales_per_customer",
        desc: "m-cumrevpc-desc",
        desc_subtitle: "m-cumrevpc-desc-subtitle",
        chart: "line",
        format: "currency",
        isCumulative: true,
        showHighlights: true,
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";

            if (index == "first_month" || index == 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumrevpc-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumrevpc-tooltip-2", {display_value})}} />
                );
            } else if (index == 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumrevpc-tooltip-3", {
                        display_value,
                        display_cumulative_total,
                        index,
                        tf_name_pl
                    })}} />
                );
            } else if (index >= 1) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumrevpc-tooltip-4", {
                        display_value,
                        display_cumulative_total,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
    acc_order_count_per_customer: {
        label: "m-cumorpc-label",
        value: "acc_order_count_per_customer",
        desc: "m-cumorpc-desc",
        desc_subtitle: "m-cumorpc-desc-subtitle",
        chart: "line",
        format: "number",
        isCumulative: true,
        showHighlights: true,
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";

            if (index == "first_month" || index == "0") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumorpc-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumorpc-tooltip-2", {display_value})}} />
                );
            } else if (index >= 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cumorpc-tooltip-3", {
                        display_value,
                        display_cumulative_total,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
    customer_count: {
        label: "m-cust-label",
        value: "customer_count",
        desc: "m-cust-desc",
        chart: "line",
        format: "number",
        isCumulative: false,
        showHighlights: true,
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";

            if (index == "first_month" || index == "0") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cust-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cust-tooltip-2", {display_value})}} />
                );
            } else if (index >= 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-cust-tooltip-3", {
                        display_value,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
    order_count: {
        label: "m-orders-label",
        value: "order_count",
        desc: "m-orders-desc",
        chart: "line",
        isCumulative: false,
        format: "number",
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";


            if (index == "first_month" || index == "0") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-orders-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-orders-tooltip-2", {display_value})}} />
                );
            } else if (index >= 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-orders-tooltip-3", {
                        display_value,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
    total_sales: {
        label: "m-rev-label",
        value: "total_sales",
        desc: "m-rev-desc",
        chart: "line",
        isCumulative: false,
        format: "currency",
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";

            if (index == "first_month" || index == "0") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-rev-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-rev-tooltip-2", {display_value})}} />
                );
            } else if (index >= 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-rev-tooltip-3", {
                        display_value,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
    aov: {
        label: "avg-order-value",
        value: "aov",
        desc: "m-aov-desc",
        chart: "line",
        isCumulative: true,
        format: "currency",
        tooltip: function (display_value, display_percentage, display_cumulative_total, tf_name, index, t) {
            let tf_name_pl = index == 1 ? tf_name : tf_name + "s";

            if (index == "first_month" || index == "0") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-aov-tooltip-1", {display_value, tf_name})}} />
                );
            } else if (index == "first_order") {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-aov-tooltip-2", {display_value})}} />
                );
            } else if (index >= 0) {
                return (
                    <span dangerouslySetInnerHTML={{__html: t("m-aov-tooltip-3", {
                        display_value,
                        index,
                        tf_name_pl
                    })}} />
                );
            }
        },
    },
};

export const new_vs_existing_metrics = {
    customer_count: {
        label: "cust",
        label_long : "m-newvse-label",
        value: "customer_count",
        desc: "",
        format: "number",
        is_stacked : true,
    },
    order_count: {
        label: "orders",
        label_long : "m-newvse-o-label",
        value: "order_count",
        desc: "",
        format: "number",
        is_stacked : true,
    },
    total_sales: {
        label: "revenue",
        label_long : "m-newvserev-label",
        value: "total_sales",
        desc: "",
        format: "currency",
        is_stacked : true,
    },
    aov: {
        label: "aov",
        label_long : "m-newvseaov-label",
        value: "aov",
        desc: "",
        format: "currency",
        is_stacked : false,
    },
    arpu: {
        label: "arpu",
        label_long : "m-newvsearpu-label",
        value: "arpu",
        desc: "",
        format: "currency",
        is_stacked : false,
    }
}