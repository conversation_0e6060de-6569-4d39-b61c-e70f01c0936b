import React from "react";
// antd imports
import { DateRangePicker } from 'react-date-range';
import 'react-date-range/dist/styles.css'; // main css file
import 'react-date-range/dist/theme/default.css'; // theme css file

import dayjs from 'dayjs';

export class DatePickerAnt extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        const { start_date, end_date, handleDateChange } = this.props;

        let state = {
            startDate: dayjs(start_date).toDate(),
            endDate: dayjs(end_date).toDate(),
            key: 'selection'
        }

        let ranges = { // TODO
            "Last 3 months": [
                dayjs().subtract(3, "month").startOf("month"),
                dayjs().subtract(1, "month").endOf("month"),
            ],
            "Last 6 months": [
                dayjs().subtract(6, "month").startOf("month"),
                dayjs().subtract(1, "month").endOf("month"),
            ],
            "Last 1 year": [
                dayjs().subtract(12, "month").startOf("month"),
                dayjs().subtract(1, "month").endOf("month"),
            ],
            "Last 2 years": [
                dayjs().subtract(24, "month").startOf("month"),
                dayjs().subtract(1, "month").endOf("month"),
            ],
        };

        return (
            <div style={{ display: "flex" }}>
                <>
                <DateRangePicker
                    onChange={item => {
                        console.log(item)
                    }}
                    editableDateInputs={true}
                    showSelectionPreview={true}
                    moveRangeOnFirstSelection={false}
                    months={2}
                    ranges={[state]}
                    maxDate={dayjs().add(1, "day").toDate()}
                    direction="horizontal"
                />;
                    {/* <RangePicker
                        inputReadOnly
                        
                        value={[dayjs(start_date), dayjs(end_date)]}
                        format={dateFormat}
                        allowClear={false}
                        disabledDate={(current) => {
                            // Can not select days before today and today
                            return (
                                current && current.toDate() > dayjs().add(1, "day").toDate()
                            );
                        }}
                        bordered
                        size={"middle"}
                        onChange={handleDateChange}
                        ranges={ranges}
                        suffixIcon={false}
                        onOk={(a) => console.log}
                    /> */}
                </>
            </div>
        );
    }
}
