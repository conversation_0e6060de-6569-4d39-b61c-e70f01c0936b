import React, { useState, useEffect } from 'react';
import {Link} from 'react-router-dom';
import Dialog from '@mui/material/Dialog';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useCancellableAxios, useMaterialUIController } from '@/context';
import MDBox from '@/components/MDBox';
import MDButton from '@/components/MDButton';
import MDTypography from '@/components/MDTypography';
import Icon from '@mui/material/Icon';

// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React examples
import DefaultPricingCard from "@/examples/Cards/PricingCards/DefaultPricingCard";
import {CardLoader} from "@/components/AppLoader";
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {PricingCardsDialog} from "@/layouts/pages/pricing-page";

const PaywallDialog = NiceModal.create(({feature, onExit}) => {
  const modal = useModal();
  const {t} = useTranslation();
  const axiosInstance = useCancellableAxios();

  const [controller] = useMaterialUIController();
  const { selectedShop, shopConfig } = controller;
  const [recommendPlan, setRecommendPlan] = useState({});
  const [currentPlan, setCurrentPlan] = useState({});
  const [loader, setLoader] = useState(true);
  
  let subscriptionHandler = (plan) => {
    let reqData = {
      plan,
    }
  
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
    }
  
    axiosInstance.post("/api/plan/upgrade", reqData).then((res) => {
      if (res.data && res.data.redirect) {
        window.location.href = res.data.redirect;
      } else if (res.data && res.data.error) {
        console.error(res.data.error)
        toast.error(res.data.error)
      } else {
        console.log(res);
      }
    }).catch((err) => {
      console.error(err);
    })
  }

  useEffect(() => {
    setLoader(true);
    const reqData = {}
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
    }

    if (!!feature) {
      reqData.feature_access = feature;
    }

    axiosInstance.post('/api/plan/paywall', reqData)
      .then((response) => {
        if (response.data.error) {
          setLoader(false);
          toast.error(response.data.error)
          return;
        }
        setRecommendPlan(response.data.recommendedPlan)
        setCurrentPlan(response.data.currentPlan)
        setLoader(false);
      })
      .catch((error) => {
        console.log(error)
        !axios.isCancel(error) && toast.error(t("something-went-wrong"))
        setLoader(false);
      })
  }, [selectedShop, feature])
  
  const getPaywallTitle = (feature) => {
    let title = ""
    let subtitle = ""
    const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";

    switch (feature) {
      case "data_time_limit":
        title = t("p-date-block-title-3-m")
        if (time_limit === "1_year") {
          title = t("p-date-block-title-1-yr")
        }
        subtitle = t("p-date-block-subtitle")
        break;
      default:
        if (!!feature && !!recommendPlan?.render?.display_name) {
          title = t("p-paywall-title-feature-plan", { plan: recommendPlan?.render?.display_name })
          subtitle = t("pricing.tagline")
        } else if (!!recommendPlan?.render?.display_name) {
          title = t("p-paywall-title-plan", { plan: recommendPlan?.render?.display_name })
          subtitle = t("pricing.tagline")
        } else {
          // No recommended plan
          title = t("p-paywall-title-default")
          subtitle = t("p-paywall-no-recommed")
        }
    }

    return { title, subtitle}
  }

  const title = getPaywallTitle(feature);
  return (
    <Dialog
      open={modal.visible}
      onClose={() => {
        modal.hide()
        if (onExit) {
          onExit();
        }
      }}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      maxWidth="lg"
    >
      {loader && <MDBox px={10}><CardLoader height={"30vh"} /></MDBox>}
      {!loader && recommendPlan?.planName && <Grid container>
          <Grid item xs={12} lg={12}>
            <DefaultPricingCard
              color="dark"
              badge={{ color: "dark", label: "" }}
              price={{ 
                currency: "$", 
                value: recommendPlan?.render?.display_price, 
                type: recommendPlan?.render?.interval_type ?? "", 
                strikeValue: recommendPlan.render?.strike_price ?? "",
                subtext: recommendPlan?.render?.interval_type == "year" && `${t("pricing.annual-offer")}`
              }}
              specifications={(currentPlan.render?.features ?? []).map(feature => ({
                label: t(feature), includes: true, understate: true
              }))}
              moreSpecifications={[
                ...((recommendPlan?.render?.prev_features ?? []).map(feature => ({
                  label: t(feature), includes: true, highlight: true
                })) || []),
                ...((recommendPlan?.render?.limited_features ?? []).map(feature => ({
                  label: t(feature), includes: false
                })) || []),
                ...((recommendPlan?.render?.features ?? []).map(feature => ({
                  label: t(feature), includes: true, highlight: true
                })) || [])
              ]}
              action={{
                disabled: false,
                hasIcon: true,
                type: "api",
                color: "info",
                label: t("upgrade"),
                handler: () => subscriptionHandler(recommendPlan?.planName)
              }}
              title={title}
              footer={
                <MDBox display="flex" width="100%" justifyContent="flex-end" p={1} pt={0} mt={-1.2}>
                  <MDButton
                      size="small"
                      variant="text"
                      to="/pricing"
                      component={Link}
                      onClick={() => modal.hide()}>
                    <MDTypography
                      variant="caption"
                      color="light"
                      textTransform="none"
                      fontWeight="regular" mr={0.5}>{t("see-other-plans")}</MDTypography> <Icon ml={0.4}>arrow_forward</Icon>
                  </MDButton>
              </MDBox>
              }
            />
          </Grid>
        </Grid>}
      {!loader && !recommendPlan?.planName && <MDBox px={2} py={2}>
        <MDTypography variant="h5" color="dark" fontWeight="bold" mb={2}>{t(title.title ?? "")}</MDTypography>
        <MDTypography variant="button" color="dark" fontWeight="regular">{t(title.subtitle ?? "")}</MDTypography>
        <MDBox display="flex" justifyContent="flex-end" mt={2}>
          <MDButton
            size="small"
            variant="contained"
            color="info"
            onClick={() => modal.hide()}
          >
            {t("close")}
          </MDButton>
        </MDBox>
      </MDBox>}
    </Dialog>
  );
});

// Switching to PricingCardsDialog instead of PaywallDialog
export default PricingCardsDialog;