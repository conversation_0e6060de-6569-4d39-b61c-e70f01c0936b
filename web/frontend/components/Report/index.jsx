import React, { useState, useEffect } from "react";
import MDBox from "@/components/MDBox";
import Table from "@/components/Report/views/Table";
import Plotly<PERSON>hart from "@/components/Report/views/PlotlyChart";
import PillBar from '@/components/PillBar';
import Divider from "@mui/material/Divider";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";
import MDBadge from "@/components/MDBadge";
import DatasetIcon from '@mui/icons-material/Dataset';
import BarChartIcon from '@mui/icons-material/BarChart';
import MDButton from "@/components/MDButton";
import MDTooltip from "@/components/MDTooltip";

import logoShopify from "@/assets/images/shopify-logo.svg";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import {SOURCE_FB, SOURCE_SHOPIFY, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS} from "@/layouts/dashboards/metrics/metadata"


export const sourceLogo = (source) => {
  switch (source) {
    case SOURCE_GOOGLE_ADS:
      return logoGoogleAds;
    case SOURCE_GOOGLE_ANALYTICS:
      return logoGoogleAnalytics;
    case SOURCE_FB:
      return logoFacebook;
    case SOURCE_SHOPIFY:
      return logoShopify;
    default:
      return logoShopify;
  }
}
// View component registry - maps viewType to component
const VIEW_COMPONENTS = {
  table: Table,
  chart: PlotlyChart
};

/**
 * Resolves variables starting with $ in the provided config object
 * @param {Object} config - The configuration object
 * @param {Array} variables - Array of variable strings to resolve
 * @returns {Object} - Object with resolved variable mappings
 */
function resolve(config, variables = []) {
  if (!variables || !Array.isArray(variables)) {
    return {};
  }

  // Start with existing variable_map if available
  const variable_map = { ...(config.variable_map || {}) };

  // Process each variable
  variables.forEach((variable) => {
    // Skip if not a string or doesn't start with $
    if (typeof variable !== 'string' || !variable.startsWith('$')) {
      return;
    }

    // Skip if already resolved in the variable_map
    if (variable_map[variable] !== undefined) {
      return;
    }

    // Parse the path parts
    const parts = variable.substring(1).split('.');
    const source = parts[0]; // config, global 

    // Handle different sources
    if (source === 'config') {
      // Navigate through the config object
      let value = config;
      for (let i = 1; i < parts.length; i++) {
        if (value === undefined || value === null) {
          break;
        }
        value = value[parts[i]];
      }
      variable_map[variable] = value;
    }
  });

  return variable_map;
}

// Render action buttons if provided in config
const renderActionButtons = (actions) => {
  if (!actions || !Array.isArray(actions) || actions.length === 0) {
    return null;
  }

  return (
    <MDBox display="flex" justifyContent="flex-end" alignItems="center" gap={1} my={1.5} px={2}>
      {actions.map((action, index) => {
        const button = (
          <MDButton
            key={index}
            variant="outlined"
            size="small"
            onClick={action.onClick}
            color="secondary"
            startIcon={action.icon && <Icon>{action.icon}</Icon>}
            sx={{ 
              fontSize: "0.75rem", 
              '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' } 
            }}
          >
            {action.label}
          </MDButton>
        );

        return action.tooltip ? (
          <MDTooltip key={index} title={action.tooltip} placement="top">
            {button}
          </MDTooltip>
        ) : button;
      })}
    </MDBox>
  );
};

export default function Report({ config }) {
  const [activeViewId, setActiveViewId] = useState("");
  const { t } = useTranslation();

  if (!config.views || config.views.length === 0) {
    return null;
  }

  useEffect(() => {
    if (config.views.length > 0) {
      // Find chart view if available
      const chartView = config.views.find(view => view.viewType === "chart");
      // Set chart view as active if available, otherwise use first view
      setActiveViewId(chartView ? chartView.id : config.views[0].id);
    }
  }, [config.views]);

  let viewOptions = config.views.map((view) => ({
    label: (
      <MDBox display="flex" direction="row" alignItems="center" verticalAlign="middle" justifyContent="center">
        {view.viewType === "table" ? <DatasetIcon fontSize="small" color="secondary" /> : <BarChartIcon fontSize="small" color="secondary" />}
        <MDTypography ml={0.5} variant="button" fontWeight="regular">{view.title || view.viewType}</MDTypography>
      </MDBox>
    ),
    value: view.id
  }));

  // Determine if we should show a footer with actions
  const shouldShowFooter = config.actions && Array.isArray(config.actions) && config.actions.length > 0;

  return (
    <MDBox
      mb={2}
      sx={{
          border: '1px solid #ccc', // Blue border
          borderRadius: '8px', // Rounded corners
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)', // Subtle shadow for depth
          maxWidth: '100%', // Ensure it doesn't exceed container width
        }}
    >
      <MDBox 
        p={1.6}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
          flexWrap: "wrap",
          gap: 1
        }}
      >
        <MDTypography 
          variant="h6" 
          color="secondary" 
          fontWeight="regular" 
          className="card-title-default" 
          sx={{ 
            flexShrink: 1, 
            overflow: "hidden", 
            textOverflow: "ellipsis",
            maxWidth: "100%"
          }}
        >
          {t(config.title ?? "report")}
        </MDTypography>
        
        {config.views.length > 1 && 
          <MDBox sx={{ flexShrink: 0, ml: { xs: 0, sm: 2 } }}>
            <PillBar
              name="type"
              options={viewOptions}
              value={activeViewId}
              onChange={(value) => setActiveViewId(value)}
            />
          </MDBox>
        }
      </MDBox>
      
      {(config.title || config.views.length > 1) && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
      
      {config.tags && 
        <MDBox 
          display="flex" 
          flexWrap="wrap" 
          alignItems="center" 
          justifyContent="left" 
          p={1.6} 
          sx={{ gap: 1 }}
        >
          {config.source && 
            <MDBox key="source" mr={0.5}>
              <MDBadge badgeContent={
                <MDBox display="flex" direction="row" alignItems="center">
                  <MDBox component="img" src={sourceLogo(config.source)} alt="shopify" width="1rem" mx={0.1} />
                  {config.source}
                </MDBox>
              } color="light" size="sm" variant="contained" container/>
            </MDBox>
          }
          {config.tags.map((tag, index) => (
            <MDBox key={index} mb={0.5}>
              <MDBadge badgeContent={tag.value} color={["info", "success", "warning", "primary", "secondary", "error", "light", "dark"][index % 8]} size="sm" variant="contained" container/>
            </MDBox>
          ))}
        </MDBox>
      }
      
      {config.tags && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
      
      <MDBox
        sx={{
          overflowX: 'auto', // Enable horizontal scrolling
          width: '100%',
          whiteSpace: 'nowrap', // Prevent wrapping
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
          scrollbarWidth: 'thin', // Firefox
          msOverflowStyle: 'none', // IE/Edge
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(0,0,0,0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: 'rgba(0,0,0,0.3)',
            },
          },
        }}
      >
        <Grid container spacing={1.2} p={1.6} pb={2.4} sx={{ minWidth: 'min-content' }}>
          {config.views.filter((view) => view.id === activeViewId).map((view) => {
              const ViewComponent = VIEW_COMPONENTS[view.viewType];
              if (!ViewComponent) {
                return null;
              }
              return (
                <ViewComponent
                  key={view.id}
                  view_config={{
                    ...view,
                    variable_map: resolve(config, view.variables)
                  }}
                />
              );
          })}
        </Grid>
      </MDBox>
      
      {shouldShowFooter && (
        <>
          <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
          {renderActionButtons(config.actions)}
        </>
      )}
    </MDBox>
  );
}