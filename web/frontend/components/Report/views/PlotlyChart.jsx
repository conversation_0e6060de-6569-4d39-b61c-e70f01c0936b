import React, { useState, useEffect, useRef } from "react";
import MDBox from "@/components/MDBox";
import Plot from "react-plotly.js"; 

function ViewPlotly({ view_config }) {
    const resolveVariable = (variable) => {
        if (typeof variable === 'string' && variable.startsWith('$') && view_config.variable_map) {
            return view_config.variable_map[variable] || variable;
        }
        return variable;
    }

    const plotlyJson = resolveVariable(view_config.dataReference);
    const containerRef = useRef(null);
    const [dimensions, setDimensions] = useState({ width: 600, height: 400 });
  
    // Measure container and update dimensions
    useEffect(() => {
      const updateDimensions = () => {
        if (containerRef.current) {
          const width = containerRef.current.offsetWidth;
          // Ensure a minimum width for wide charts
          const minWidth = 600;
          const actualWidth = Math.max(width, minWidth);
          const height = Math.max(400, actualWidth * 0.6);
          setDimensions({ width: actualWidth, height });
        }
      };
  
      updateDimensions();
      window.addEventListener('resize', updateDimensions);
      return () => window.removeEventListener('resize', updateDimensions);
    }, []);

    // Check if plotlyJson is valid
    if (!plotlyJson || !plotlyJson.data || !plotlyJson.layout) {
        return null;
    }

    return (
      <MDBox 
        ref={containerRef} 
        sx={{ 
          width: '100%', 
          minHeight: '400px',
          height: 'auto', 
          overflow: 'hidden',
          minWidth: 'min-content' // Ensure container expands to fit content
        }}
      >
        {plotlyJson && (
          <Plot
            data={plotlyJson.data}
            layout={{
              ...plotlyJson.layout,
              width: dimensions.width,
              height: dimensions.height,
              autosize: true,
              margin: { t: 30, b: 50, l: 50, r: 30 }
            }}
            style={{ width: '100%', height: '100%' }}
            config={{
              responsive: true,
              displayModeBar: true,
              scrollZoom: false
            }}
          />
        )}
      </MDBox>
    );
}

export default ViewPlotly;