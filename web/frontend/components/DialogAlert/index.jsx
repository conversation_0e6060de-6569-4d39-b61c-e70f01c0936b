import React from 'react';
import Button from "@mui/material/Button";
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Box from "@mui/material/Box";

const DialogAlert = NiceModal.create(({description, onDialogClose, image}) => {
  const modal = useModal();
  return (
    <Dialog
      open={modal.visible}
      onClose={onDialogClose}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
    >
      <DialogContent>
        {!!image && 
            <Box display="flex" justifyContent="center" alignItems="center">
                {image}
            </Box>
        }
        <DialogContentText id="alert-dialog-slide-description">
          {description}
        </DialogContentText>
        <Box display="flex" justifyContent="center" alignItems="center" mt={2}>
            <Button variant="contained" color="success" size="small" onClick={onDialogClose} width={"30%"}>
                Done
            </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
});

export default DialogAlert;