import React from "react";
// antd imports
import {Select, Form, Input, Row, Col, <PERSON><PERSON>, Card as AntCard} from "antd";
import { axiosInstance, tracker } from "@/context";
const { Option } = Select;
// Authentication layout components
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";

export default class OnboardingSurvey extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            loading: false,
        };

        this.saveOnboardingDetails = this.saveOnboardingDetails.bind(this);
    }

    saveOnboardingDetails = (values) => {
        var setState = (newState) => {
            return this.setState(newState);
        };

        tracker.event(`Onboarding Survey Completed`);

        const { user, onOnboardingFinish } = this.props;

        let url = !!user && !!user.email ? "/api/user/onboarding" : "/api/onboarding";

        setState({ loading: true });
        axiosInstance
            .post(url, values)
            .then(function (response) {
                setState({ loading: false });
                if (response && response.data.status) {
                    onOnboardingFinish();
                }
            })
            .catch((err) => {
                console.log("error", err);
                setState({ loading: false });
            });
    };

    render = () => {
        const { loading } = this.state;
        const {t, user} = this.props;

        return (
            <BasicLayout image={bgImage}>
                <AntCard
                    title={t("onboarding-title")}
                    className="custom-ant-card"
                    style={{ borderRadius: "10px" }}
                >
                    <Form
                        name="basic"
                        requiredMark={false}
                        layout="vertical"
                        initialValues={{
                            onboard_email: !!user && !!user.email ? user.email : '',
                            // Add other initial values as needed
                        }}
                        onFinish={this.saveOnboardingDetails}
                    >
                        <Form.Item
                            label={t("full-name")}
                            name="onboard_name"
                            rules={[{ required: true, message: t("full-name-validation") }]}
                        >
                            <Input placeholder="John Doe" />
                        </Form.Item>

                        <Form.Item
                            name={"onboard_email"}
                            label={t("work-email")}
                            rules={[
                                { type: "email", message: t("enter-valid-email") },
                                { required: true, message: t("enter-valid-email") },
                            ]}
                            validateTrigger="onBlur"
                        >
                            <Input placeholder="<EMAIL>" disabled={!!user && !!user.email} />
                        </Form.Item>

                        <Form.Item
                            name={"onboard_phone"}
                            label={t("phone-number")}
                            rules={[
                                { type: "phone", message: t("enter-valid-phone")},
                            ]}
                            validateTrigger="onBlur"
                        >
                            <Input placeholder="+40 735 631 620" />
                        </Form.Item>

                        <Form.Item
                            name="onboard_industry"
                            label={t("industry")}
                            rules={[
                                { required: true, message: t("select-industry") },
                            ]}
                        >
                            <Select showSearch placeholder={t("select-industry")}>
                                <Option value="Art & Photography">{t("art-photography")}</Option>
                                <Option value="Animals & Pet Supplies">{t("animals-pet-supplies")}</Option>
                                <Option value="Baby & Toddler">{t("baby-toddler")}</Option>
                                <Option value="Clothing & Fashion">{t("clothing-fashion")}</Option>
                                <Option value="Jewelry & Accessories">{t("jewelry-accessories")}</Option>
                                <Option value="Electronics">{t("electronics")}</Option>
                                <Option value="Food & Drink">{t("food-drink")}</Option>
                                <Option value="Home & Garden">{t("home-garden")}</Option>
                                <Option value="Furniture">{t("furniture")}</Option>
                                <Option value="Hardware">{t("hardware")}</Option>
                                <Option value="Health & Beauty">{t("health-beauty")}</Option>
                                <Option value="Sports & Recreation">{t("sports-recreation")}</Option>
                                <Option value="Toys & Games">{t("toys-games")}</Option>
                                <Option value="Stationary">{t("stationary")}</Option>
                                <Option value="Other">{t("other")}</Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            name="onboard_user_type"
                            label={t("user-type")}
                        >
                            <Select showSearch placeholder={t("user-type-label")}>
                                <Option value="owner">{t("shopify-merchant")}</Option>
                                <Option value="agency">
                                    {t("agency")}
                                </Option>
                                <Option value="other">{t("other")}</Option>
                            </Select>
                        </Form.Item>

                        <Form.Item>
                            <Row>
                                <Col span={10} offset={7}>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        style={{ width: "100%" }}
                                        loading={loading}
                                    >
                                        {t("submit")}
                                    </Button>
                                </Col>
                            </Row>
                        </Form.Item>
                    </Form>
                </AntCard>
            </BasicLayout>
        );
    };
}