import React, {useState} from 'react';
// import FacebookLogin from 'react-facebook-login';
// import FacebookLogin from 'react-facebook-login/dist/facebook-login-render-props'
import { tracker, useCancellableAxios } from "@/context";

import { toast } from 'react-toastify';
import NiceModal from '@ebay/nice-modal-react';
import DialogAlert from '@/components/DialogAlert';
import MDButton from '@/components/MDButton';

// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Typography from "@mui/material/Typography";

import { useMaterialUIController } from '@/context';

const ConnectionCard = () => {
    const [controller, dispatch] = useMaterialUIController();
    const { loginConfig, selectedShop } = controller;
    const [connected, setConnected] = useState(false)
    const [loading, setLoading] = useState(false)
    const [closed, setClosed] = useState(false)

    if (closed || connected) {
        return null
    }

    const axiosInstance = useCancellableAxios();


    var responseFacebook = (response)  => {
        console.log(response);

        if ('status' in response && response.status == 'unknown') {
            tracker.event(`Integration Failure`, {type: 'facebook',response});
            return
        }

        if ('error' in response) {
            tracker.event(`Integration Failure`, {type: 'facebook',response});
        } else {
            tracker.event(`Integration Success`, {type: 'facebook'});
        }

        let reqData = {authResult:response};
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        let notif_email = ""
        if (!!loginConfig.user && !!loginConfig.user.email) {
            notif_email = loginConfig.user.email
        } else if (!!loginConfig.shop && !!loginConfig.shop.onboard_email) {
            notif_email = loginConfig.shop.onboard_email ?? ''
        }

        setLoading(true)
        axiosInstance
            .post("/api/fbauthover", reqData)
            .then(function (response) {
                setLoading(false)
                let resp = response.data || {}
                if ('error' in resp && resp.error != '') {
                    tracker.event('Integration Failure', {type: 'facebook', err: resp.error_message});
                    toast.error(resp.error_message);
                } else if ('accounts' in resp) {
                    NiceModal.show(DialogAlert, {
                        image : <img src="https://illustrations.popsy.co/amber/creative-work.svg" style={{width:'60%'}}/>,
                        description: <>
                            <Typography variant="button" color="dark">
                                Successfully connected with Facebook Ads
                            </Typography>
                            <Typography variant="body2" mt={2}>
                                <p>You have successfully connected your Facebook account. It typically takes around 24 hours to generate Facebook Ads insights.</p>
                                <br/>
                                <p>{!!notif_email && <i>Once finished, You'll also receive a notification at {notif_email}</i>}</p>
                            </Typography>
                        </>,
                        onDialogClose : () => {
                            setConnected(true)
                            NiceModal.hide(DialogAlert);
                        }
                    })
                } else {
                    toast.error('Something went wrong, Please try again later. or Get <NAME_EMAIL>');
                }
            })
            .catch((err) => {
                setLoading(false)
                console.error("error", err);
                tracker.event('Integration Failure', {type: 'facebook', err: err});
            });
    }

    return (
        <MDBox mt={3} mb={3} >
            <Grid container spacing={3} justifyContent="center" alignItems={"center"}>
                <Grid item xs={4}>
                    <MDBox 
                        display="flex"
                        alignItems="center"
                        justifyContent="center">
                        <MDBox
                            component="img"
                            sx={{
                                width:"100%",
                            }}
                            src={"https://illustrations.popsy.co/yellow/finance-growth.svg"}
                            alt={"facebook-growth"}
                        />
                    </MDBox>
                </Grid>

                <Grid item xs={8}>
                    <MDBox py={1}>
                    <MDBox display="flex" alignItems="center">
                        <MDBox lineHeight={1}>
                            <MDTypography variant="h6" fontWeight="medium" mb={1}>
                                Introducing Facebook Ads Analytics
                            </MDTypography>
                            <MDBox mb={2} pr={2}>
                                <MDTypography variant="button" fontWeight={"regular"} color="dark" lineHeight={2}>
                                    <p>1. Campaign Strategy Analysis - <i>Analyse between acquisition, retargeting & retention strategy for spend share and performance share </i></p>
                                    <p>2. Creative Analysis - <i>Find the top CTR/ ROAS creatives quickly. Understand what constitue the best performing creatives</i> </p>
                                    <p>3. Audiences Recommendation - <i>Discover the potential audience to increase ROAS</i></p>
                                </MDTypography>
                            </MDBox>
                            {/* <FacebookLogin
                                appId="3144526972437033"
                                returnScopes
                                fields="name,email,picture"
                                callback={responseFacebook}
                                autoLoad={false}
                                scope = "ads_read,ads_management,business_management" //- removed it for now
                                render={renderProps => (
                                    <MDSocialButton
                                        onClick={() => {
                                            tracker.event(`Integration Started`, {type: 'facebook'});
                                            renderProps.onClick()
                                        }}
                                    >
                                        {(renderProps.isProcessing || loading) ? <CircularProgress size={20} thickness={2} color="white" /> : <>
                                            <FacebookIcon /> &nbsp;Connect with Facebook 
                                        </>}
                                    </MDSocialButton>
                                )}
                            /> */}
                            <MDButton variant="text" color="secondary" size="small" onClick={() => {
                                tracker.event(`Integration Card Closed`, {type: 'facebook'});
                                setClosed(true)
                            }}>
                                Later
                            </MDButton>
                        </MDBox>
                    </MDBox>
                    </MDBox>
                </Grid>
            </Grid>
        </MDBox>
    )
}

export default ConnectionCard;
