import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { MaterialUIControllerProvider } from "./context";
import { Suspense } from "react";

import "./i18";
import "./index.css";
import App from "./App";

const container = document.getElementById('app');
const root = createRoot(container);
root.render(
    <BrowserRouter>
        <MaterialUIControllerProvider>
            <Suspense fallback={null}>
                <App />
            </Suspense>
        </MaterialUIControllerProvider>
    </BrowserRouter>
);
