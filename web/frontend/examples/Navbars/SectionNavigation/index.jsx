import React, {useState, useEffect} from "react";
import MenuItem from '@mui/material/MenuItem';
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
// Material Dashboard 2 PRO React examples
import {useMaterialUIController} from "@/context";
import { useTranslation } from "react-i18next";

import { Link as RSLink, Element, Events, animateScroll as scroll, scrollSpy, scroller } from 'react-scroll';
import Menu from "@mui/material/Menu";
import ViewDayOutlinedIcon from '@mui/icons-material/ViewDayOutlined';

function SectionNavigation({ light, sections }) {

    const [controller, dispatch] = useMaterialUIController();
    const { transparentNavbar, darkMode } = controller;
    const { t } = useTranslation();

    const [currentSection, setCurrentSection] = useState("");
    const [anchorEl, setAnchorEl] = useState(null);

    // Styles for the navbar
    const navStyle = ({ palette: { dark, white, text }, functions: { rgba } }) => ({
        cursor : "pointer",
        color: () => {
        let colorValue = light || darkMode ? white.main : dark.main;

        if (transparentNavbar && !light) {
            colorValue = darkMode ? rgba(text.main, 0.6) : text.main;
        }

        return colorValue;
        },
    });

    const handleOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    useEffect(() => {
        scrollSpy.update();
    
        Events.scrollEvent.register('begin', function(to, element) {
        //   console.log("begin", arguments);
        });
      
        Events.scrollEvent.register('end', function(to, element) {
        //   console.log("end", arguments);
        });
      
        return () => {
          Events.scrollEvent.remove('begin');
          Events.scrollEvent.remove('end');
        };
    }, [sections]);

    return (
        <MDBox display="flex" justifyContent="center" alignItems="center" flexDirection="column">
            <MDBox display="flex" justifyContent="center" alignItems="center" flexDirection="row" p={1} sx={navStyle} onClick={handleOpen}>
                <ViewDayOutlinedIcon fontSize="medium" sx={navStyle} />
                {sections && sections.length > 1 && <MDTypography
                    ml={0.4}
                    fontWeight="regular"
                    aria-label="more"
                    aria-controls="section-menu"
                    aria-haspopup="true"
                    variant="button"
                    sx={navStyle}
                >
                    {t(currentSection)}
                </MDTypography>}
            </MDBox>
            <Menu
                id="section-menu"
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleClose}
                anchorReference={null}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                sx={{ mt: 1, ".MuiMenu-paper": { maxWidth: "300px" }}}
            >
                {sections.map((section) => (
                    <MenuItem value={section} key={section} onClick={() => {
                        scroller.scrollTo(section, {offset: -250, smooth: true, duration: 300});
                        handleClose();
                    }}>
                        {t(section)}
                    </MenuItem>
                ))}
            </Menu>
            {sections.map((section) => (
                <MenuItem value={section} key={section} sx={{display: "none"}}>
                    <RSLink
                        to={section}
                        spy={true}
                        smooth={true}
                        offset={-250}
                        duration={300}
                        onSetActive={() => setCurrentSection(section)}
                    >
                        {section}
                    </RSLink>
                </MenuItem>
            ))}
        </MDBox>
    );
}

// Setting default values for the props of SectionNavigation
SectionNavigation.defaultProps = {
    light: false,
    sections: [],
};
  
// Typechecking props for the SectionNavigation
SectionNavigation.propTypes = {
    light: PropTypes.bool,
    sections: PropTypes.arrayOf(PropTypes.string),
};
  
export default SectionNavigation;
  