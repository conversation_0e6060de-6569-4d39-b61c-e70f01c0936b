import { useState, useEffect } from "react";

// react-router components
import { useLocation, Link, useNavigate } from "react-router-dom";
import { tracker } from "@/context";
import { signOutUser } from "@/utils/auth";
// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";
import SectionNavigation from "@/examples/Navbars/SectionNavigation";

// @material-ui core components
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Icon from "@mui/material/Icon";
import MUILink from "@mui/material/Link";
import Avatar from "@mui/material/Avatar";
import ListItemIcon from "@mui/material/ListItemIcon";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Material Dashboard 2 PRO React examples
import Breadcrumbs from "@/examples/Breadcrumbs";

// Custom styles for DashboardNavbar
import {
  navbar,
  navbarContainer,
  navbarIconButton,
} from "@/examples/Navbars/DashboardNavbar/styles";

// Material Dashboard 2 PRO React context
import {
  useMaterialUIController,
  setTransparentNavbar,
  setMiniSidenav,
  setOpenConfigurator,
} from "@/context";

import { useTranslation } from 'react-i18next';

function DashboardNavbar({ absolute, light, isMini, sections }) {
  const [navbarType, setNavbarType] = useState();
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, transparentNavbar, fixedNavbar, openConfigurator, darkMode, loginConfig, shopConfig, selectedShop } = controller;
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [langMenuAnchor, setLangMenuAnchor] = useState(null);
  const route = useLocation().pathname.split("/").slice(1);
  const { i18n, t } = useTranslation();
  const { pathname } = useLocation();

  let showSubscriptionBanner = shopConfig.subscription_enabled
    && shopConfig.planDetails?.planType == "free";

  useEffect(() => {
    // Setting the navbar type
    if (fixedNavbar) {
      setNavbarType("sticky");
    } else {
      setNavbarType("static");
    }

    // A function that sets the transparent state of the navbar.
    function handleTransparentNavbar() {
      setTransparentNavbar(dispatch, (fixedNavbar && window.scrollY === 0) || !fixedNavbar);
    }

    /**
     The event listener that's calling the handleTransparentNavbar function when
     scrolling the window.
    */
    window.addEventListener("scroll", handleTransparentNavbar);

    // Call the handleTransparentNavbar function to set the state with the initial value.
    handleTransparentNavbar();

    // Remove event listener on cleanup
    return () => window.removeEventListener("scroll", handleTransparentNavbar);
  }, [dispatch, fixedNavbar]);

  const handleMiniSidenav = () => setMiniSidenav(dispatch, !miniSidenav);
  const handleConfiguratorOpen = () => setOpenConfigurator(dispatch, !openConfigurator);

  const handleUserMenuOpen = (event) => setUserMenuAnchor(event.currentTarget);
  const handleUserMenuClose = () => setUserMenuAnchor(null);

  const handleLangMenuOpen = (event) => setLangMenuAnchor(event.currentTarget);
  const handleLangMenuClose = () => setLangMenuAnchor(null);

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    handleLangMenuClose();
  };

  // Styles for the navbar icons
  const iconsStyle = ({ palette: { dark, white, text }, functions: { rgba } }) => ({
    color: () => {
      let colorValue = light || darkMode ? white.main : dark.main;

      if (transparentNavbar && !light) {
        colorValue = darkMode ? rgba(text.main, 0.6) : text.main;
      }

      return colorValue;
    },
  });

  // Render the language menu
  const renderLangMenu = () => (
    <Menu
      anchorEl={langMenuAnchor}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      open={Boolean(langMenuAnchor)}
      onClose={handleLangMenuClose}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: "140px",
          "& .MuiMenuItem-root": {
            py: 1,
            px: 2,
            fontSize: "0.875rem"
          }
        }
      }}
    >
      <MenuItem onClick={() => handleLanguageChange('en')}>English</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('es')}>Español</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('fr')}>Français</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('de')}>Deutsch</MenuItem>
    </Menu>
  );

  // Check if it's a shop flow (no user context)
  const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;
  const navigate = useNavigate();

  let activeShop = null;
  if (isShopFlow) {
    activeShop = (loginConfig.shopOptions ?? []).find((shop) => shop.myshopify_domain === selectedShop);
  } else {
    const activeWorkspace = loginConfig?.workspaces?.active_workspace;
    const shopOptions = activeWorkspace?.shops ?? [];
    activeShop = shopOptions.find((shop) => shop.myshopify_domain === selectedShop);
  }


  // Render the user menu with options based on flow type
  const renderUserMenu = () => (
    <Menu
      anchorEl={userMenuAnchor}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      open={Boolean(userMenuAnchor)}
      onClose={handleUserMenuClose}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: "240px",
          "& .MuiMenuItem-root": {
            py: 1,
            px: 2
          },
          "& .MuiListItemIcon-root": {
            minWidth: "36px"
          },
          "& .MuiTypography-root": {
            fontSize: "0.875rem"
          }
        }
      }}
    >
      {/* User info header - show in user flow only */}
      {!isShopFlow && loginConfig.userData && (
        <MDBox p={2} mb={1} display="flex" alignItems="center">
          <Avatar
            sx={{
              width: 40,
              height: 40,
              bgcolor: darkMode ? 'primary.main' : 'secondary.main',
              mr: 2
            }}
          >
            <Icon sx={{ fontSize: 20 }}>person</Icon>
          </Avatar>
          <MDBox>
            <Typography variant="subtitle2" fontWeight="medium">
              {loginConfig.userData.onboard_name || "User"}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {loginConfig.userData.email}
            </Typography>
          </MDBox>
        </MDBox>
      )}

      {/* Shop info header - show in shop flow only */}
      {isShopFlow && activeShop && (
        <MDBox p={2} mb={1} display="flex" alignItems="center">
          <Avatar
            sx={{
              width: 40,
              height: 40,
              bgcolor: darkMode ? 'primary.main' : 'secondary.main',
              mr: 2
            }}
          >
            <Icon sx={{ fontSize: 20 }}>storefront</Icon>
          </Avatar>
          <MDBox>
            <Typography variant="subtitle2" fontWeight="medium">
              {activeShop.name || activeShop.myshopify_domain || "Shop"}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {activeShop.myshopify_domain}
            </Typography>
          </MDBox>
        </MDBox>
      )}

      {/* Divider after user/shop info */}
      <Divider sx={{ my: 1 }} />
      {/* Theme Settings - Admin only */}
      {loginConfig.admin && (
        <MenuItem onClick={() => { handleConfiguratorOpen(); handleUserMenuClose(); }}>
          <ListItemIcon>
            <Icon sx={{ fontSize: "1.1rem" }}>settings</Icon>
          </ListItemIcon>
          <Typography variant="body2">{t("Theme Settings")}</Typography>
        </MenuItem>
      )}

      {/* User Settings - Show in user flow only */}
      {!isShopFlow && (
        <MenuItem
          component={Link}
          to="/user-settings"
          onClick={handleUserMenuClose}
        >
          <ListItemIcon>
            <Icon sx={{ fontSize: "1.1rem" }}>person</Icon>
          </ListItemIcon>
          <Typography variant="body2">{t("user-settings")}</Typography>
        </MenuItem>
      )}

      {/* Book a call */}
      <MenuItem
        component={Link}
        to="/book-call"
        onClick={() => {
          tracker.event("Clicked BookCall", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
          <Icon sx={{ fontSize: "1.1rem" }}>event_available</Icon>
        </ListItemIcon>
        <Typography variant="body2">{t("book-call")}</Typography>
      </MenuItem>

      {/* Feature Requests */}
      <MenuItem
        component={Link}
        to="/feature-requests"
        onClick={() => {
          tracker.event("Feature Request Clicked", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
          <Icon sx={{ fontSize: "1.1rem" }}>event_available</Icon>
        </ListItemIcon>
        <Typography variant="body2">{"Feature Requests"}</Typography>
      </MenuItem>

      {/* Help Center */}
      <MenuItem
        component={MUILink}
        href="https://help.datadrew.io/en"
        target="_blank"
        onClick={() => {
          tracker.event("Clicked HelpCenter", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
          <Icon sx={{ fontSize: "1.1rem" }}>help_center_outlined</Icon>
        </ListItemIcon>
        <Typography variant="body2">{t("visit-help-center")}</Typography>
      </MenuItem>

      {/* Language Selection */}
      <MenuItem onClick={handleLangMenuOpen}>
        <ListItemIcon>
          <Icon sx={{ fontSize: "1.1rem" }}>translate</Icon>
        </ListItemIcon>
        <Typography variant="body2">{t("Choose a different language")}</Typography>
      </MenuItem>

      {/* Add Sign Out option to the menu */}
      <MenuItem onClick={() => {
        signOutUser(navigate, isShopFlow);
        handleUserMenuClose();
      }}>
        <ListItemIcon>
          <Icon sx={{ fontSize: "1.1rem" }}>logout</Icon>
        </ListItemIcon>
        <Typography variant="body2">{t("sign-out")}</Typography>
      </MenuItem>
    </Menu>
  );

  return (
    <AppBar
      position={absolute ? "absolute" : navbarType}
      color="inherit"
      sx={(theme) => navbar(theme, { transparentNavbar, absolute, light, darkMode, showSubscriptionBanner })}
    >
      <Toolbar sx={(theme) => navbarContainer(theme)}>
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
        >
          <MDBox display="flex" alignItems="center">
            <IconButton
              sx={{
                ...navbarIconButton,
                mr: 1,
                display: "flex",
                alignItems: "center"
              }}
              onClick={handleMiniSidenav}
              size="small"
              disableRipple
            >
              <Icon fontSize="medium" sx={iconsStyle}>
                {miniSidenav ? "menu_open" : "menu"}
              </Icon>
            </IconButton>

            <Breadcrumbs
              icon="home"
              title={route[route.length - 1].replace("-", " ")}
              route={route}
              light={light}
            />
          </MDBox>

          {!isMini && (
            <MDBox display="flex" alignItems="center">
              {sections && sections.length > 0 && <SectionNavigation sections={sections} light={light} />}

              <MDBox color={light ? "white" : "inherit"}>
                <IconButton
                  size="small"
                  disableRipple
                  color="inherit"
                  sx={navbarIconButton}
                  onClick={handleUserMenuOpen}
                >
                  {/* Show settings icon in shop flow, user icon in user flow */}
                  <Avatar sx={{ width: 30, height: 30, bgcolor: darkMode ? 'primary.main' : 'secondary.main' }}>
                    <Icon sx={{ fontSize: 18 }}>{!loginConfig.user || !loginConfig.user.email ? "settings" : "person"}</Icon>
                  </Avatar>
                </IconButton>
                {renderUserMenu()}
                {renderLangMenu()}
              </MDBox>
            </MDBox>
          )}
        </MDBox>
      </Toolbar>
    </AppBar>
  );
}

// Setting default values for the props of DashboardNavbar
DashboardNavbar.defaultProps = {
  absolute: false,
  light: false,
  isMini: false,
  sections: [],
};

// Typechecking props for the DashboardNavbar
DashboardNavbar.propTypes = {
  absolute: PropTypes.bool,
  light: PropTypes.bool,
  isMini: PropTypes.bool,
  sections: PropTypes.arrayOf(PropTypes.string),
};

export default DashboardNavbar;