import MDBox from "@/components/MDBox";

export const InfoOutlinedIcon = () => {
    return (
        <MDBox display="flex" alignItems="center" justifyContent="center">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={"20"}
            height={"20"}
            fill="#7b809a"
            viewBox="0 0 256 256">
            <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm16-40a8,8,0,0,1-8,8,16,16,0,0,1-16-16V128a8,8,0,0,1,0-16,16,16,0,0,1,16,16v40A8,8,0,0,1,144,176ZM112,84a12,12,0,1,1,12,12A12,12,0,0,1,112,84Z">
            </path>
        </svg>
        </MDBox>
    )
}

export const ArrowUpRightIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            fill="#000000"
            viewBox="0 0 256 256">
            <path d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z">
            </path>
        </svg>
    )
}

export const ArrowDownRightIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            fill="#000000"
            viewBox="0 0 256 256">
            <path d="M200,88V192a8,8,0,0,1-8,8H88a8,8,0,0,1,0-16h84.69L58.34,69.66A8,8,0,0,1,69.66,58.34L184,172.69V88a8,8,0,0,1,16,0Z">
            </path>
        </svg>
    )
}