// prop-types is library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";
import Tooltip from "@mui/material/Tooltip";
import CardActionArea from "@mui/material/CardActionArea";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDBadge from "@/components/MDBadge";
import MDTypography from "@/components/MDTypography";

function DefaultInfoCard({ color, icon, title, description, value, mdi_icon, tooltip, onCardClick, active }) {
  return (
    <Card>
      <Tooltip title={(tooltip ?? '')} placement="right" arrow>
      <CardActionArea onClick={onCardClick}>
      {!!active &&
        <MDBox position="absolute" top={0} right={0} zIndex={1}>
          <MDBadge
            size="sm"
            badgeContent={
              <>
                <Icon fontSize="small">check</Icon>
              </>
            } 
            container
            color="primary"
            sx={{
                borderRadius: "0 0 0 1rem"
            }}
          />
        </MDBox>}

      {!!tooltip && false && 
        <MDBox mt={1} mr={-1} position="absolute" right="1.5rem">
          <Tooltip title={tooltip} placement="right" arrow>
            <Icon color="secondary">error_outline</Icon>
          </Tooltip>
        </MDBox>}
      <MDBox p={2} mx={3} display="flex" justifyContent="center">
        <MDBox
          display="grid"
          justifyContent="center"
          alignItems="center"
          bgColor={color}
          color="white"
          width="4rem"
          height="4rem"
          shadow="md"
          borderRadius="lg"
          variant="gradient"
        >
          {!!icon && <Icon fontSize="default">{icon}</Icon>}
          {!!mdi_icon && <Icon fontSize="default" className={mdi_icon} />}
        </MDBox>
      </MDBox>
      <MDBox pb={2} px={2} textAlign="center" lineHeight={1.25}>
        <MDTypography variant="h6" fontWeight="medium" textTransform="capitalize">
          {title}
        </MDTypography>
        {description && (
          <MDTypography variant="caption" color="text" fontWeight="regular">
            {description}
          </MDTypography>
        )}
        {description && !value ? null : <Divider />}
        {value && (
          <MDTypography variant="h6" fontWeight="medium">
            {value}
          </MDTypography>
        )}
      </MDBox>
      </CardActionArea>
      </Tooltip>
    </Card>
  );
}

// Setting default values for the props of DefaultInfoCard
DefaultInfoCard.defaultProps = {
  color: "info",
  value: "",
  description: "",
  tooltip: ""
};

// Typechecking props for the DefaultInfoCard
DefaultInfoCard.propTypes = {
  color: PropTypes.oneOf(["primary", "secondary", "info", "success", "warning", "error", "dark"]),
  icon: PropTypes.node,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  description: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.node]),
  tooltip : PropTypes.string,
  onCardClick : PropTypes.func,
};

export default DefaultInfoCard;
