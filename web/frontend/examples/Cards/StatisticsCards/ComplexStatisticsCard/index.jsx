// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

function ComplexStatisticsCard({ color, title, count, percentage, icon, chart, diffLabel, vsLabel, isEmbedded }) {
  return (
    <Card className={isEmbedded ? "embedded-card" : ""}>
      <MDBox display="flex" justifyContent="space-between" py={1} px={1.5} m={1} className={isEmbedded ? "embedded-card-header" : ""} alignItems="baseline">
        {!!icon && <MDBox
          variant="gradient"
          bgColor={color}
          color={color === "light" ? "dark" : "white"}
          coloredShadow={color}
          borderRadius="xl"
          display="flex"
          justifyContent="center"
          alignItems="center"
          width="4rem"
          height="4rem"
          mt={-3}
        >
          <Icon fontSize="medium" color="inherit">
            {icon}
          </Icon>
        </MDBox>}
        <MDBox textAlign={!icon ? "left" : "right"} lineHeight={1.25}>
          <MDBox mb={0.5}>
          {typeof title === "string" 
                ? (<MDTypography variant="button" fontWeight="regular" color="dark" mr={0.4} className={isEmbedded ? "embedded-card-header-title" : ""}>
                  {title}
                  </MDTypography>)
                : (title)}
          </MDBox>
          <MDBox><MDTypography variant="h6" fontWeight="bold">{count}</MDTypography></MDBox>
        </MDBox>
        <MDBox textAlign={"right"} lineHeight={1.25}>
          <MDBox mb={0.5}>{diffLabel}</MDBox>
          <>{vsLabel}</>
        </MDBox>
      </MDBox>
      {!icon && <Divider variant="fullWidth" sx={{m : 0.75}}/>}
      {!!chart && <MDBox px={0.75} mb={1}>{chart}</MDBox>}
    </Card>
  );
}

// Setting default values for the props of ComplexStatisticsCard
ComplexStatisticsCard.defaultProps = {
  color: "info",
  percentage: {
    color: "success",
    text: "",
    label: "",
  },
};

// Typechecking props for the ComplexStatisticsCard
ComplexStatisticsCard.propTypes = {
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
    "error",
    "light",
    "dark",
  ]),
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.node]).isRequired,
  percentage: PropTypes.shape({
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "dark",
      "white",
    ]),
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    label: PropTypes.string,
  }),
  icon: PropTypes.node,
  chart: PropTypes.node,
  footer: PropTypes.node,
  isEmbedded: PropTypes.bool,
};

export default ComplexStatisticsCard;
