// prop-types is a library for typechecking of props
import PropTypes from "prop-types";
import React from "react";

// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

// Material Dashboard 2 PRO React contexts
import { useMaterialUIController } from "@/context";

function DefaultStatisticsCard({ title, count, percentage, dropdown, isEmbedded, diffLabel, vsLabel }) {
  const [controller] = useMaterialUIController();
  const { darkMode } = controller;

  return (
    <Card className={isEmbedded ? "embedded-card" : ""}>
      <MDBox p={1}>
        <Grid container>
          <Grid item xs={dropdown ? 7 : 12} p={0.6} className={isEmbedded ? "embedded-card-header" : ""}>
            <MDBox mb={0.5} lineHeight={1} display="flex" alignItems="center" justifyContent="center">
              {typeof title === "string" 
                ? (<MDTypography variant="button" fontWeight="regular" color="dark" textTransform="capitalize">
                  {title}
                  </MDTypography>)
                : (title)}
            </MDBox>
            <MDBox lineHeight={1.5} display="flex" alignItems="center" justifyContent="center">
              {React.isValidElement(count)
                ? count
                : (<MDTypography variant="h4" fontWeight="medium" my={0.4}>
                  {count}
                </MDTypography>)}
              {!!percentage && percentage.value && <MDTypography variant="button" fontWeight="bold" color={percentage.color}>
                {percentage.value}&nbsp;
                <MDTypography
                  variant="button"
                  fontWeight="regular"
                  color={darkMode ? "text" : "secondary"}
                >
                  {percentage.label}
                </MDTypography>
              </MDTypography>}
            </MDBox>
          </Grid>
          {diffLabel && <MDBox mt={1} display="flex" alignItems="center" justifyContent="space-between" sx={{width: "100%"}}>
            <>{diffLabel}</> <>{vsLabel}</>
          </MDBox>}
          {dropdown && <Grid item xs={5}>
            {dropdown && (
              <MDBox width="100%" textAlign="right" lineHeight={1}>
                <MDTypography
                  variant="caption"
                  color="secondary"
                  fontWeight="regular"
                  sx={{ cursor: "pointer" }}
                  onClick={dropdown.action}
                >
                  {dropdown.value}
                </MDTypography>
                {dropdown.menu}
              </MDBox>
            )}
          </Grid>}
        </Grid>
      </MDBox>
    </Card>
  );
}

// Setting default values for the props of DefaultStatisticsCard
DefaultStatisticsCard.defaultProps = {
  percentage: {
    color: "success",
    value: "",
    label: "",
  },
  dropdown: false,
};

// Typechecking props for the DefaultStatisticsCard
DefaultStatisticsCard.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.node]).isRequired,
  percentage: PropTypes.shape({
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "dark",
      "white",
    ]),
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    label: PropTypes.string,
  }),
  dropdown: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      action: PropTypes.func,
      menu: PropTypes.node,
      value: PropTypes.string,
    }),
  ]),
  isEmbedded : PropTypes.bool
};

export default DefaultStatisticsCard;
