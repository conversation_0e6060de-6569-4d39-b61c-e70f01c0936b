// react-router-dom components
import { Link } from "react-router-dom";

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Divider from "@mui/material/Divider";

// Modified

function CategoriesList({ title, categories }) {
  const renderItems = categories.map(({ color, icon, mdi_icon, name, description, route }, key) => (
    <MDBox
      key={name}
      component="li"
      display="flex"
      justifyContent="space-between"
      alignItems="center"
      borderRadius="lg"
      py={1}
      pr={2}
      mb={categories.length - 1 === key ? 0 : 1}
      sx={{
            transition: "all 0.2s cubic-bezier(.34,1.61,.7,1.3)",
            "&:hover, &:focus": {
              transform: "translateX(5px)",
            },
          }}
    >
      <MDBox display="flex" alignItems="center" component={Link} to={route}>
        <MDBox
          display="grid"
          alignItems="center"
          justifyContent="center"
          bgColor={color}
          borderRadius="lg"
          shadow="md"
          color="white"
          width="2rem"
          height="2rem"
          mr={2}
          variant="gradient"
          fontSize="0.875rem"
        >
          {!!icon && <Icon
            sx={{
              display: "grid",
              placeItems: "center",
            }}
          >
            {icon}
          </Icon>}
          {!!mdi_icon && <Icon sx={{
              display: "grid",
              placeItems: "center",
            }} fontSize="small" className={mdi_icon} />}
        </MDBox>
        <MDBox display="flex" flexDirection="column">
          <MDTypography variant="button" color={"dark"} fontWeight="regular" gutterBottom>
            {name}
          </MDTypography>
          <MDTypography variant="caption" color="text">
            {description}
          </MDTypography>
        </MDBox>
      </MDBox>
      <MDBox display="flex">
        <MDTypography
          component={Link}
          variant="button"
          color={"dark"}
          to={route}
          sx={{
            lineHeight: 0,
            transition: "all 0.2s cubic-bezier(.34,1.61,.7,1.3)",
            p: 0.5,

            "&:hover, &:focus": {
              transform: "translateX(5px)",
            },
          }}
        >
          <Icon sx={{ fontWeight: "bold" }}>chevron_right</Icon>
        </MDTypography>
      </MDBox>
    </MDBox>
  ));

  return (
    <Card sx={{height:"100%"}}>
      <Grid container spacing={3} p={1.6} direction="row" justifyContent="flex-start" alignItems="flex-end">
          <Grid item display="flex" justifyContent="flex-start" alignItems="center">
            <MDTypography variant="h5" color="dark" fontWeight="regular" mr={1} className="card-title-default">
              {title}
            </MDTypography>
          </Grid>
      </Grid>
      <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
      <MDBox p={1.6} pb={2.4} variant="gradient" sx={{borderRadius:"inherit", height:"100%"}}>
        <MDBox component="ul" display="flex" flexDirection="column" p={0} m={0} className="explore-items" height="18rem">
          {renderItems}
        </MDBox>
      </MDBox>
    </Card>
  );
}

// Typechecking props for the CategoriesList
CategoriesList.propTypes = {
  title: PropTypes.string.isRequired,
  categories: PropTypes.arrayOf(PropTypes.object).isRequired,
};

export default CategoriesList;
