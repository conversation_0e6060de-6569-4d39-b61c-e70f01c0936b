// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";
import React from "react";

// @mui material components
import Collapse from "@mui/material/Collapse";
import ListItem from "@mui/material/ListItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Custom styles for the SidenavCollapse
import {
  collapse,
  collapseItem,
  collapseIconBox,
  collapseIcon,
  collapseText,
  collapseArrow,
} from "@/examples/Sidenav/styles/sidenavCollapse";

// Material Dashboard 2 PRO React context
import { useMaterialUIController } from "@/context";

function SidenavCollapse({ icon, hoverIcon, name, subtitle, badge, children, active, noCollapse, disabled, open, ...rest }) {
  const [controller] = useMaterialUIController();
  const { miniSidenav, transparentSidenav, whiteSidenav, darkMode } = controller;
  const [hover, setHover] = React.useState(false);

  // Handle string name truncation
  if (typeof name === 'string' && name.length > 21) {
    name = `${name.substring(0, 21)}...`
  }

  return (
    <>
      <ListItem component="li" disabled={disabled}>
      {/* definitions for hoverIcon fill */}
        <svg width={0} height={0}>
          <linearGradient id="iconGradient" x1={0} y1={0} x2={1} y2={1}>
            <stop offset={0} stopColor="#DE4DAA" />
            <stop offset={1} stopColor="#F6D327" />
          </linearGradient>
        </svg>
        <MDBox
          {...rest}
          onMouseEnter={() => !disabled && setHover(true)}
          onMouseLeave={() => !disabled && setHover(false)}
          sx={(theme) =>
            collapseItem(theme, { active, transparentSidenav, whiteSidenav, darkMode })
          }
          disabled={disabled}
        >
          <ListItemIcon
            sx={(theme) => collapseIconBox(theme, { miniSidenav, transparentSidenav, whiteSidenav, darkMode })}
          >
            {(hover || active) && hoverIcon ? (hoverIcon) :
              typeof icon === "string"
                ? (<Icon sx={(theme) => collapseIcon(theme, { active })}>{icon}</Icon>)
                : (icon)
            }
          </ListItemIcon>

          <ListItemText
            primary={<>{name} &nbsp; {!!badge && badge}</>}
            secondary={!miniSidenav && subtitle ? subtitle : null}
            sx={(theme) => ({
              ...collapseText(theme, {
                miniSidenav,
                transparentSidenav,
                whiteSidenav,
                active,
              }),
              "& .MuiListItemText-primary": {
                lineHeight: subtitle ? "1.1" : "1.5",
                marginBottom: subtitle ? "6px" : "0",
                display: "block",
              },
              "& .MuiListItemText-secondary": {
                fontSize: "0.7rem",
                fontWeight: "light",
                color: transparentSidenav || whiteSidenav ? theme.palette.text.secondary : theme.palette.white.main,
                lineHeight: 1,
                marginTop: "0px",
                paddingTop: "0px",
                opacity: 0.7,
                display: "block",
                clear: "both"
              }
            })}
          />

          {!noCollapse && (
            <Icon
              sx={(theme) =>
                collapseArrow(theme, {
                  noCollapse,
                  transparentSidenav,
                  whiteSidenav,
                  miniSidenav,
                  open,
                  active,
                  darkMode,
                })
              }
            >
              expand_more
            </Icon>
          )}
        </MDBox>
      </ListItem>
      {children && (
        <Collapse in={open} unmountOnExit
          sx={(theme) =>
            collapse(theme, { active, transparentSidenav, whiteSidenav, darkMode })
          }
          >
          {children}
        </Collapse>
      )}
    </>
  );
}

// Setting default values for the props of SidenavCollapse
SidenavCollapse.defaultProps = {
  active: false,
  noCollapse: false,
  children: false,
  open: false,
  disabled: false,
};

// Typechecking props for the SidenavCollapse
SidenavCollapse.propTypes = {
  icon: PropTypes.node.isRequired,
  name: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  subtitle: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  badge: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  children: PropTypes.node,
  active: PropTypes.bool,
  noCollapse: PropTypes.bool,
  open: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default SidenavCollapse;
