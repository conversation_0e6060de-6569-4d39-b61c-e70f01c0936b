import { useEffect, useState } from "react";

// react-router-dom components
import { useLocation, NavLink } from "react-router-dom";

// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";

// @mui material components
import List from "@mui/material/List";
import Divider from "@mui/material/Divider";
import Link from "@mui/material/Link";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

import BookCall from "@/layouts/pages/book-call";
// Material Dashboard 2 PRO React examples
import SidenavCollapse from "@/examples/Sidenav/SidenavCollapse";
import SidenavList from "@/examples/Sidenav/SidenavList";
import SidenavItem from "@/examples/Sidenav/SidenavItem";
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
// Custom styles for the Sidenav
import SidenavRoot from "@/examples/Sidenav/SidenavRoot";
import sidenavLogoLabel from "@/examples/Sidenav/styles/sidenav";
import premiumTag from "@/assets/images/premium-tag.png";
import pxToRem from "@/assets/theme/functions/pxToRem";


// Material Dashboard 2 PRO React context
import {
  useMaterialUIController,
  setMiniSidenav,
  setTransparentSidenav,
  setWhiteSidenav,
  tracker,
} from "@/context";

import ProfileCollapse from "@/layouts/pages/profile/current";
import { useTranslation } from "react-i18next";

function Sidenav({ color, brand, brandName, brandNameTagline, routes, ...rest }) {
  const [openCollapse, setOpenCollapse] = useState(false);
  const [openNestedCollapse, setOpenNestedCollapse] = useState(false);
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, transparentSidenav, whiteSidenav, darkMode, shopConfig, selectedShop } = controller;
  const location = useLocation();
  const { pathname } = location;
  const collapseName = pathname.split("/").slice(1)[0];
  const items = pathname.split("/").slice(1);
  const itemParentName = items[1];
  const itemName = items[items.length - 1];


  let textColor = "white";
  let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  let isFreePlan = shopConfig.planDetails?.planType == "free";
  let showSubscriptionBanner = shopConfig.subscription_enabled && isFreePlan;

  if (transparentSidenav || (whiteSidenav && !darkMode)) {
    textColor = "dark";
  } else if (whiteSidenav && darkMode) {
    textColor = "inherit";
  }

  const closeSidenav = () => setMiniSidenav(dispatch, true);
  const {t} = useTranslation();

  useEffect(() => {
    setOpenCollapse(collapseName);
    setOpenNestedCollapse(itemParentName);
  }, []);

  useEffect(() => {
    // A function that sets the mini state of the sidenav.
    function handleMiniSidenav() {
      setMiniSidenav(dispatch, window.innerWidth < 1200);
      setTransparentSidenav(dispatch, window.innerWidth < 1200 ? false : transparentSidenav);
      setWhiteSidenav(dispatch, window.innerWidth < 1200 ? false : whiteSidenav);
    }

    /**
     The event listener that's calling the handleMiniSidenav function when resizing the window.
    */
    window.addEventListener("resize", handleMiniSidenav);

    // Call the handleMiniSidenav function to set the state with the initial value.
    handleMiniSidenav();

    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", handleMiniSidenav);
  }, [dispatch, location]);

  // Render all the nested collapse items from the routes.js
  const renderNestedCollapse = (collapse) => {
    const template = collapse.map(({ name, route, key, href, badge }) =>
      href ? (
        <Link
          key={key}
          href={href}
          target="_blank"
          rel="noreferrer"
          sx={{ textDecoration: "none" }}
        >
          <SidenavItem name={t(key)} badge={badge} nested />
        </Link>
      ) : (
        <NavLink to={route} key={key} sx={{ textDecoration: "none" }}>
          <SidenavItem name={t(key)} badge={badge} active={route === pathname} nested />
        </NavLink>
      )
    );

    return template;
  };
  // Render the all the collpases from the routes.js
  const renderCollapse = (collapses) =>
    collapses.map(({ name, badge, collapse, route, href, key }) => {
      let returnValue;

      if (collapse) {
        returnValue = (
          <SidenavItem
            key={key}
            color={color}
            name={t(key)}
            badge={badge}
            active={key === itemParentName ? "isParent" : false}
            open={openNestedCollapse === key}
            onClick={({ currentTarget }) =>
              openNestedCollapse === key && currentTarget.classList.contains("MuiListItem-root")
                ? setOpenNestedCollapse(false)
                : setOpenNestedCollapse(key)
            }
          >
            {renderNestedCollapse(collapse)}
          </SidenavItem>
        );
      } else {
        returnValue = href ? (
          <Link
            href={href}
            key={key}
            target="_blank"
            rel="noreferrer"
            sx={{ textDecoration: "none" }}
          >
            <SidenavItem color={color} name={t(key)} badge={badge} active={key === itemName} />
          </Link>
        ) : (
          <NavLink to={route} key={key} sx={{ textDecoration: "none" }}>
            <SidenavItem color={color} name={t(key)} badge={badge} active={key === itemName} />
          </NavLink>
        );
      }
      return <SidenavList key={key}>{returnValue}</SidenavList>;
    });

  const topRoutes = routes.filter(({ feature, stick }) => {
    if (!!feature && !shopConfig.planDetails?.features?.[feature]) {
      return false;
    }
    return stick === "top"
  });
  const bottomRoutes = routes.filter(({ feature, stick }) => {
    if (!!feature && !shopConfig.planDetails?.features?.[feature]) {
      return false;
    }
    return stick === "bottom"
  });


  // Render all the routes from the routes.js (All the visible items on the Sidenav)
  const renderRoutes = (rs) => rs.map(
    ({ type, name, badge, icon, hoverIcon, title, collapse, noCollapse, key, href, route }) => {
      let returnValue;

      if (type == "profile-collapse") {
        returnValue = (
          <ProfileCollapse
            color={color}
            openCollapse={openCollapse}
            key={key}
            keyName={key}
            setOpenCollapse={setOpenCollapse}
            collapseOnClick={() => (openCollapse === key ? setOpenCollapse(false) : setOpenCollapse(key))}
          />
        )
      } else if (type === "collapse") {
        if (href) {
          returnValue = (
            <Link
              href={href}
              key={key}
              target="_blank"
              rel="noreferrer"
              sx={{ textDecoration: "none" }}
            >
              <SidenavCollapse
                name={t(key)}
                badge={badge}
                disabled={false}
                icon={icon}
                hoverIcon={hoverIcon}
                active={key === collapseName}
                noCollapse={noCollapse}
              />
            </Link>
          );
        } else if (noCollapse && route) {
          returnValue = (
            <NavLink to={route} key={key}>
              <SidenavCollapse
                name={t(key)}
                badge={badge}
                icon={icon}
                disabled={false}
                hoverIcon={hoverIcon}
                noCollapse={noCollapse}
                active={key === collapseName}
              >
                {collapse ? renderCollapse(collapse) : null}
              </SidenavCollapse>
            </NavLink>
          );
        } else {
          returnValue = (
            <SidenavCollapse
              key={key}
              name={t(key)}
              badge={badge}
              disabled={false}
              icon={icon}
              hoverIcon={hoverIcon}
              active={key === collapseName}
              open={openCollapse === key}
              onClick={() => (openCollapse === key ? setOpenCollapse(false) : setOpenCollapse(key))}
            >
              {collapse ? renderCollapse(collapse) : null}
            </SidenavCollapse>
          );
        }
      } else if (type === "title") {
        returnValue = (
          <MDTypography
            key={key}
            color={textColor}
            display="block"
            variant="caption"
            fontWeight="bold"
            textTransform="uppercase"
            pl={3}
            mt={1}
            mb={1}
            ml={1}
          >
            {title}
          </MDTypography>
        );
      } else if (type === "divider") {
        returnValue = (
          <Divider
            sx={{margin: "8px !important"}}
            key={key}
            light={
              (!darkMode && !whiteSidenav && !transparentSidenav) ||
              (darkMode && !transparentSidenav && whiteSidenav)
            }
          />
        );
      }

      return returnValue;
    }
  );



  return (
    <SidenavRoot
      {...rest}
      variant="permanent"
      ownerState={{ transparentSidenav, whiteSidenav, miniSidenav, darkMode }}
      sx={{
        ".MuiPaper-root": {
          height: `calc(100vh - ${pxToRem((showSubscriptionBanner ? 64 : 32))}) !important`,
          marginTop: pxToRem((showSubscriptionBanner ? 48 : 16)),
        },
      }}
    >
      <MDBox pt={3} pb={1} px={4} textAlign="center">
        <MDBox
          display={{ xs: "block", xl: "none" }}
          position="absolute"
          top={0}
          right={0}
          p={1.625}
          onClick={closeSidenav}
          sx={{ cursor: "pointer" }}
        >
          <MDTypography variant="h6" color="secondary">
            <Icon sx={{ fontWeight: "bold" }}>close</Icon>
          </MDTypography>
        </MDBox>
        <MDBox display="flex" alignItems="center">
          {brand && <MDBox component="img" src={brand} alt="DataDrew" width="2.3rem" mr={0.8} />}
          {brandName && <MDBox
            width={!brandName && "100%"}
            sx={(theme) => sidenavLogoLabel(theme, { miniSidenav })}
          >
            <MDTypography component="h6" variant="h5" fontWeight="regular" color={textColor}>
              {brandName}
            </MDTypography>
            <MDTypography variant="button" fontWeight="light" color={textColor}  mr={1}>
                {brandNameTagline}
            </MDTypography>
          </MDBox>}
        </MDBox>
      </MDBox>
      <Divider
        sx={{margin: "8px !important"}}
        light={
          (!darkMode && !whiteSidenav && !transparentSidenav) ||
          (darkMode && !transparentSidenav && whiteSidenav)
        }
      />
      <List sx={{maxHeight: `calc(100vh - ${pxToRem((showSubscriptionBanner ? 64 : 32))} - 260px) !important`, overflow: "scroll"}}>
        {renderRoutes(topRoutes)}
      </List>


      <MDBox
          className="rotate-tag-parent"
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          position="absolute"
          zIndex={100}
          bgColor="transparent"
          width="100%"
          bottom={0}
          pb={1}
          // style={{
          //   background: "linear-gradient(195deg, #252527, #191919)"
          // }}
        >
      <List style={{display: "absolute", bottom: 0, width: "100%"}}>
        <Divider
            sx={{margin: "8px !important"}}
            key={"divider-bottom"}
            light={
              (!darkMode && !whiteSidenav && !transparentSidenav) ||
              (darkMode && !transparentSidenav && whiteSidenav)
            }
          />
        {/* Sign out button removed from sidebar */}
          {/* {isSubscriptionEnabled && !isFreePlan && renderRoutes([{
            type: "collapse",
            name: "Book a call",
            key: "book-call",
            route: "/book-call",
            stick: "bottom",
            component: <BookCall />,
            icon: <EventAvailableIcon fontSize="small" />,
            hoverIcon : <EventAvailableIcon fontSize="medium" sx={{fill: `url(#iconGradient)` }} />,
            noCollapse: true
          }])} */}
          {isSubscriptionEnabled && isFreePlan &&
          <NavLink to={"/pricing"} key={"/pricing"}>
          <SidenavCollapse
                onClick={() => {tracker.mixpanel.track('Clicked Pricing Page', {page : pathname, source: "sidenav"})}}
                key="pricing"
                disabled={false}
                name={!isFreePlan ? t("subscription") : t("start-trial")}
                icon={<MDBox className="rotate-tag" component="img" src={premiumTag} alt="Brand" width="1.2rem" mb={0.4} />}
                noCollapse={true}
                active={false}
              >
            </SidenavCollapse>
          </NavLink>
          }
          {renderRoutes(bottomRoutes)}
      </List>
      </MDBox>

      {false && isSubscriptionEnabled &&
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          bgColor={!isFreePlan ? "inherit" : "dark"}
          position="absolute"
          zIndex={100}
          bottom={1.5}
          pt={2}
          pb={1}
          px={4}
          textAlign="center"
          sx={{ cursor: "pointer",  width:"100%"}}
          onClick={() => {tracker.mixpanel.track('Clicked Pricing Page')}}
        >
          <MDBox component={NavLink} to="/pricing" display="flex" alignItems="center" sx={{cursor:"pointer"}}>
            <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" mr={0.8} />
            {!miniSidenav && <MDBox width={"100%"} sx={{cursor:"pointer"}}>
              <MDTypography component="h6" variant="button" fontWeight="regular" color={textColor} sx={{cursor:"pointer"}}>
                {!isFreePlan ? t("subscription") : t("upgrade")}
              </MDTypography>
            </MDBox>}
          </MDBox>
        </MDBox>
      }
    </SidenavRoot>
  );
}

// Setting default values for the props of Sidenav
Sidenav.defaultProps = {
  color: "info",
  brand: "",
};

// Typechecking props for the Sidenav
Sidenav.propTypes = {
  color: PropTypes.oneOf(["primary", "secondary", "info", "success", "warning", "error", "dark"]),
  brand: PropTypes.string,
  brandName: PropTypes.string.isRequired,
  brandNameTagline: PropTypes.string,
  routes: PropTypes.arrayOf(PropTypes.object).isRequired,
};

export default Sidenav;
