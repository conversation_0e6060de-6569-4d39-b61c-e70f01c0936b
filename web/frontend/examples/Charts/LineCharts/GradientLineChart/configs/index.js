// Material Dashboard 2 PRO React base styles
import typography from "@/assets/theme/base/typography";
// import zoomPlugin from 'chartjs-plugin-zoom';

function configs(labels, datasets, customOptions = {}) {
  return {
    data: {
      labels,
      datasets: [...datasets],
    },
    // plugins: [zoomPlugin],
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        zoom: {
          limits: {
            y: {min: 'original', max: 'original'},
          },
          zoom: {
            wheel: {
              enabled: false,
            },
            drag : {
              enabled: true,
              borderColor: 'rgba(0,0,0,0.5)',
              borderWidth: 1,
              backgroundColor: 'rgb(0,0,0, 0.3)',
              animationDuration: 0
            },
            pinch: {
              enabled: false
            },
            mode: 'y',
          }
        },
        legend: {
          display: false,
          onClick: false
        },
        tooltip: {
          titleFontColor: "#333",
          bodyFontColor: "#666",
          bodySpacing: 4,
          xPadding: 12,
          yPadding: 12,
          intersect: 0,
          position: "nearest",
          callbacks: {
            label: function (tooltipItems, data) {
              return `${tooltipItems.dataset.label}: ${tooltipItems.raw}${customOptions.valSuffix ?? ""}`;
            },
            labelColor: function(chartData) {
              let labelColors = ["#344767","","#4CAF50","#ffd59f", "#F44335", ""]
              if (chartData.datasetIndex < labelColors.length) {
                return {backgroundColor: labelColors[chartData.datasetIndex]}
              }

              return {}
          },
          },
          filter : function (a,b,c,d) {
            return b != 1 && b != 5 // hijacking for benchmarks - TODO cleanup later
          }
        }
      },
      interaction: {
        intersect: false,
        mode: "index",
      },
      scales: {
        y: {
          grid: {
            drawBorder: false,
            display: true,
            drawOnChartArea: true,
            drawTicks: false,
            borderDash: [5, 5],
          },
          ticks: {
            display: true,
            padding: 10,
            color: "#344767",
            font: {
              size: 11,
              family: typography.fontFamily,
              style: "normal",
              lineHeight: 2,
            },
          },
        },
        x: {
          grid: {
            drawBorder: false,
            display: false,
            drawOnChartArea: false,
            drawTicks: false,
            borderDash: [5, 5],
          },
          ticks: {
            display: true,
            color: "#344767",
            padding: 20,
            font: {
              size: 11,
              family: typography.fontFamily,
              style: "normal",
              lineHeight: 2,
            },
          },
        },
      },
    },
  };
}

export default configs;
