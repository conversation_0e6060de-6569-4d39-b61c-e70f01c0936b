import { useRef, useEffect, useState, useMemo } from "react";

// porp-types is a library for typechecking of props
import PropTypes from "prop-types";

// react-chartjs-2 components
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React helper functions
import gradientChartLine from "@/assets/theme/functions/gradientChartLine";

// GradientLineChart configurations
import configs from "@/examples/Charts/LineCharts/GradientLineChart/configs";

// Material Dashboard 2 PRO React base styles
import colors from "@/assets/theme/base/colors";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

function GradientLineChart({ icon, title, description, height, chart }) {
  const chartRef = useRef(null);
  const [chartData, setChartData] = useState({});
  const { data, options, plugins } = useMemo(() => chartData, [chartData]);

  // const myChartRef = useRef(null);
  
  // let chartPlugins = []
  // if (plugins) {
  //   chartPlugins = plugins;
  // }

  // ChartJS.register(...chartPlugins);

  // let resetZoomHandler = () => {
  //   myChartRef.current.resetZoom();
  // };

  useEffect(() => {
    const chartElement = chartRef.current;
    if (!chartElement) return;

    const chartDatasets = chart.datasets
      ? chart.datasets.map((dataset) => ({
          tension: 0,
          pointRadius: 0,
          borderWidth: 4,
          borderColor: colors[dataset.color]
            ? colors[dataset.color || "dark"].main
            : colors.dark.main,
          fill: true,
          maxBarThickness: 6,
          backgroundColor: gradientChartLine(
            chartElement.ctx,
            dataset.bgColor ? (colors[dataset.bgColor] ? colors[dataset.bgColor || "dark"].main : colors.dark.main) 
              : (colors[dataset.color] ? colors[dataset.color || "dark"].main : colors.dark.main)
          ),
          ...dataset
        }))
      : [];

    setChartData(configs(chart.labels || [], chartDatasets, chart.customOptions ?? {}));
  }, [chart]);

  const renderChart = (
    <MDBox py={1} pr={1} pl={icon.component ? 1 : 1}>
      {title || description ? (
        <MDBox display="flex" px={description ? 1 : 0} pt={description ? 1 : 0}>
          {icon.component && (
            <MDBox
              width="4rem"
              height="4rem"
              bgColor={icon.color || "info"}
              variant="gradient"
              coloredShadow={icon.color || "info"}
              borderRadius="xl"
              display="flex"
              justifyContent="center"
              alignItems="center"
              color="white"
              mt={-5}
              mr={2}
            >
              <Icon fontSize="medium">{icon.component}</Icon>
            </MDBox>
          )}
          <MDBox mt={icon.component ? -2 : 0}>
            {title && <MDTypography variant="h6">{title}</MDTypography>}
            <MDBox mb={2}>
              <MDTypography component="div" variant="button" color="text">
                {description}
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      ) : null}
      <MDBox height={height}>
        <Line
          ref={chartRef}
          data={{
            labels: data?.labels || [],
            datasets: data?.datasets || [],
          }}
          options={options}
          redraw
        />
      </MDBox>
      {/* <MDButton onClick={resetZoomHandler}>Reset Zoom</MDButton> */}
    </MDBox>
  );

  return title || description ? <Card>{renderChart}</Card> : renderChart;
}

// Setting default values for the props of GradientLineChart
GradientLineChart.defaultProps = {
  icon: { color: "info", component: "" },
  title: "",
  description: "",
  height: "19.125rem",
};

// Typechecking props for the GradientLineChart
GradientLineChart.propTypes = {
  icon: PropTypes.shape({
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "light",
      "dark",
    ]),
    component: PropTypes.node,
  }),
  title: PropTypes.string,
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  chart: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.array, PropTypes.object])).isRequired,
};

export default GradientLineChart;
