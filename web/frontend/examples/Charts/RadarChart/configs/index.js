import typography from "@/assets/theme/base/typography";

function configs(labels, datasets, customOptions = {}) {
  return {
    data: {
      labels,
      datasets: [...datasets],
    },
    options: {
      scales: {
        r: {
          pointLabels: {
            color: "#344767",
            font: {
              size: 12,
              family: typography.fontFamily,
              style: "normal",
              lineHeight: 2,
            }
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position : "bottom"
        },
        tooltip: {
          titleFontColor: "#333",
          bodyFontColor: "#666",
          bodySpacing: 4,
          xPadding: 12,
          yPadding: 12,
          intersect: 0,
          position: "nearest",
          callbacks: {
              label: function (tooltipItems, data) {
                return `${tooltipItems.dataset.label}: ${tooltipItems.raw}${customOptions.valSuffix ?? ""}`;
              }
          }
        }
      },
    },
  };
}

export default configs;
