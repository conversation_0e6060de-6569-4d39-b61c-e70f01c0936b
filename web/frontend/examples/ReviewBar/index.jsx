// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import {useLocation } from "react-router-dom";


import * as React from 'react';
import { styled } from '@mui/material/styles';
import Rating from '@mui/material/Rating';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentSatisfiedIcon from '@mui/icons-material/SentimentSatisfied';
import SentimentVerySatisfiedIcon from '@mui/icons-material/SentimentVerySatisfied';
import {useMaterialUIController, useCancellableAxios} from "@/context";
import { useTranslation } from "react-i18next";
import {tracker} from "@/context";

const StyledRating = styled(Rating)(({ theme }) => ({
  '& .MuiRating-iconEmpty .MuiSvgIcon-root': {
    color: theme.palette.action.disabled,
  },
}));

const customIcons = {
  1: {
    icon: <SentimentVeryDissatisfiedIcon sx={{mr:1}} color="error" />,
    label: 'Very Dissatisfied',
  },
  2: {
    icon: <SentimentSatisfiedIcon sx={{mr:1}} color="warning" />,
    label: 'Neutral',
  },
  3: {
    icon: <SentimentVerySatisfiedIcon sx={{mr:1}} color="success" />,
    label: 'Very Satisfied',
  },
};

function IconContainer(props) {
  const { value, ...other } = props;
  return <span {...other}>{customIcons[value].icon}</span>;
}

IconContainer.propTypes = {
  value: PropTypes.number.isRequired,
};

function ReviewBar({subject}) {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, selectedFilters, loginConfig} = controller;
  const {t} = useTranslation();
  const { pathname } = useLocation();
  let sub = !!subject ? subject : pathname;
  let dv = null
  if (loginConfig.feedbacks && sub in loginConfig.feedbacks) {
    dv = loginConfig.feedbacks[sub]
  }

  const axiosInstance = useCancellableAxios();

  return (
      <MDBox
        width="100%"
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        flexWrap="wrap"
        color="text"
        mt={5}
        mb={1}
      >
        <MDTypography fontWeight="regular" variant="button" style={{color: "#737373"}} my={1}>
          {t("report-feedback-text")}
        </MDTypography>
        <StyledRating
            max={3}
            defaultValue={dv}
            size={"large"}
            onChange={(e, val) => {
                tracker.event("Report Feedback", {
                  subject: sub,
                  rating: val
                })

                let reqData = {
                  subject: sub,
                  rating: val
                }

                if (!!selectedShop) {
                  reqData.selectedShop = selectedShop;
                }

                axiosInstance.post('/api/feedback', reqData).then((response) => {
                  console.log(response)
                }).catch((error) => {
                  console.log(error)
                })
            }}
            name="review-bar"
            IconContainerComponent={IconContainer}
            getLabelText={(value) => customIcons[value].label}
            highlightSelectedOnly
        />
      </MDBox>
  );
}

// Typechecking props for the ReviewBar
ReviewBar.propTypes = {
  subject: PropTypes.string,
};

export default ReviewBar;
