// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import MDTooltip from "@/components/MDTooltip";
import AvatarGroup from '@mui/material/AvatarGroup';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Divider from "@mui/material/Divider";
import MDButton from "@/components/MDButton";
import Grid from "@mui/material/Grid";

import Dialog from '@mui/material/Dialog';
import {useTranslation} from "react-i18next";

const DialogBox = NiceModal.create(({content}) => {
  const modal = useModal();
  const {t} = useTranslation();
  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        sx={{minWidth:"200px"}}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
      {content}
    </Dialog>
  );
});


function ProductCell({ products, breakdown }) {

  const {t} = useTranslation();
  const handleImgError = (e) => {e.target.onerror = null; e.target.src="https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081";}
  let names = products.map(p => p.breakdown);
  let images = products.map(p => p.image_src);
  if (breakdown == 'product_type' || breakdown == 'product_vendor') {
      images = []
  }

  const showDialog = () => {
    console.log(products);
    NiceModal.show(DialogBox, {
        content: (
            <MDBox sx={{overflow: "scroll", borderRadius:0, pb: 1}} p={1}>
                <MDTypography variant="h6" color="dark" fontWeight="regular" mb={1} p={1}>
                    {t("product-comb")}
                </MDTypography>
                <Divider />
                <MDBox display="flex" justifyContent="center" alignItems="center" width="100%" mt={1}>
                <Grid container spacing={1} p={1}>
                    {images.map((i, ind) => (
                        <Grid item xs={12} sm={12 / (images.length)} key={`k-${ind}`}>
                            <MDBox display="flex" flexDirection="column" alignItems="center" p={1}>
                                <MDBox mb={1}>
                                  <MDBox component="img" src={i} sx={{width: "100%", minHeight: "100px", maxHeight: "400px", objectFit: "contain"}}
                                      onError={handleImgError}
                                   />
                                </MDBox>
                                <MDTypography variant="button" color="dark" fontWeight="regular">
                                    {names[ind]}
                                </MDTypography>
                            </MDBox>
                        </Grid>
                    ))}
                </Grid>
                </MDBox>
                <MDBox display="flex" justifyContent="flex-end" alignItems="center" width="100%" mt={1}>
                    <MDButton variant="outlined" color="dark" size="small" my={1} onClick={() => NiceModal.hide(DialogBox)}>
                        {t("close")}
                    </MDButton>
                </MDBox>
            </MDBox>)
    });
  }


  let clickToEnlarge = images && images.length > 0;


  return (
    <MDBox display="flex" alignItems="center" pr={2} sx={{cursor: clickToEnlarge ? "pointer" : "default"}} onClick={clickToEnlarge ? showDialog : null}>
      {images && images.length > 0 && <MDBox mr={2} display="flex">
        <AvatarGroup total={images.length} variant="circular">
        <MDAvatar key={`k-${0}`} src={images[0]} onError={handleImgError} />
        {images.map((i, ind) => {
          if (ind == 0) {
            return null;
          }
          return (<MDBox key={`k-${ind}`} ml={-3} ><MDAvatar src={i} onError={handleImgError} /></MDBox>)
        })}
        </AvatarGroup>
      </MDBox>}
      {names && names.length > 0 && <MDBox mr={2} display="flex" flexDirection="column">
        {names.map((n, ind) => (
          <MDBox key={`k-${ind}`}>
          {(n && n.length > 40)  ? 
          (<MDTooltip title={n} placement="right" key={`k-${ind}`}>
            <MDTypography color={clickToEnlarge ? "info" : "dark"} key={`k-${ind}`} variant="button" fontWeight="regular" display="block">
              {n.substring(0, 40) + "..."}
            </MDTypography>
            </MDTooltip>) : 
          (<MDTypography color={clickToEnlarge ? "info" : "dark"} key={`k-${ind}`} variant="button" fontWeight="regular" display="block">
            {n}
          </MDTypography>)
          }
          </MDBox>
          )
        )}
      </MDBox>}
    </MDBox>
  );
}

// Typechecking props for the ProductCell
ProductCell.propTypes = {
  products: PropTypes.arrayOf(PropTypes.object).isRequired
};

export default ProductCell;
