// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDTypography from "@/components/MDTypography";
import PaidOutlinedIcon from '@mui/icons-material/PaidOutlined';

function ReviewCell({ rating }) {
  const ratings = {
    0.5: [
      <PaidOutlinedIcon key={1} />,
      <PaidOutlinedIcon key={2} />,
      <PaidOutlinedIcon key={3} />,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    1: [
      <Icon key={1}>paid</Icon>,
      <PaidOutlinedIcon key={2} />,
      <PaidOutlinedIcon key={3} />,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    1.5: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>star_half</Icon>,
      <PaidOutlinedIcon key={3} />,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    2: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <PaidOutlinedIcon key={3} />,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    2.5: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>star_half</Icon>,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    3: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>paid</Icon>,
      <PaidOutlinedIcon key={4} />,
      <PaidOutlinedIcon key={5} />,
    ],
    3.5: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>paid</Icon>,
      <Icon key={4}>star_half</Icon>,
      <PaidOutlinedIcon key={5} />,
    ],
    4: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>paid</Icon>,
      <Icon key={4}>paid</Icon>,
      <PaidOutlinedIcon key={5} />,
    ],
    4.5: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>paid</Icon>,
      <Icon key={4}>paid</Icon>,
      <Icon key={5}>star_half</Icon>,
    ],
    5: [
      <Icon key={1}>paid</Icon>,
      <Icon key={2}>paid</Icon>,
      <Icon key={3}>paid</Icon>,
      <Icon key={4}>paid</Icon>,
      <Icon key={5}>paid</Icon>,
    ],
  };

  return (
    <MDTypography variant="h4" color="text">
      {ratings[rating]}
    </MDTypography>
  );
}

// Typechecking props for the ReviewCell
ReviewCell.propTypes = {
  rating: PropTypes.number.isRequired,
};

export default ReviewCell;
