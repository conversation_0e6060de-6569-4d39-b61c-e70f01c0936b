import React from "react";
import axios from "axios";
import DatePickerAnt  from "@/components/Filters/DatePickerFilter";
import PillBar from '@/components/PillBar';
// antd imports
import {Row, Col} from "antd";

import { useCancellableAxios, tracker } from "@/context";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import ProductScatterPlot from "./ProductScatterPlot.jsx";
import ProductLTVTable from "./ProductLTVTable.jsx";
import { CSVLink } from "react-csv";
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';

// Material Dashboard 2 PRO React components
import Divider from "@mui/material/Divider";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import Grid from "@mui/material/Grid";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import FeatureBlurBox from "@/components/FeatureBlurBox";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { Element } from 'react-scroll';

import dayjs from 'dayjs';
import { useTranslation } from "react-i18next";

function MultiProductType({response, setProductTypes}) {

    let product_types = !!response && !!response.product_types ? response.product_types : [];

    let optionList = product_types.map((p) => {
        return {label : p, value : p}
    })

    return (
        <Autocomplete
            multiple
            blurOnSelect
            id="product-type-select"
            options={optionList}
            onChange={(event, value) => {
                setProductTypes(value.map((v) => v.value))
            }}
            getOptionLabel={(option) => {
                return option.label
            }}
            isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
            }}
            filterSelectedOptions
            renderInput={(params) => (
                <TextField {...params} size="small"/>
            )}
        />
    );
}

let cancelToken;
import {useMaterialUIController} from "@/context";
import MDTooltip from "@/components/MDTooltip/index.jsx";

export default function Product(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, shopConfig} = controller;
    const [repurchase_window, setRepurchaseWindow] = React.useState("100000");
    const [selectedProductTypes, setProductTypes] = React.useState([])
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    return (
        <>
        <DashboardFeatureBlock feature={"repurchase_rate"} />
        <ProductRoot
            {...props}
            selectedProductTypes={selectedProductTypes}
            setProductTypes={setProductTypes}
            repurchase_window={repurchase_window}
            setRepurchaseWindow={setRepurchaseWindow}
            axiosInstance={axiosInstance}
            shopConfig={shopConfig}
            selectedShop={selectedShop}
            {...selectedFilters}
            t={t}
        />
        </>
    )
}

class ProductRoot extends React.Component {
    constructor(props) {
        super(props);

        // defaults
        this.state = {
            loading: true,
            response: {},
            group_value : "product"
        };

        this.exportTrack = this.exportTrack.bind(this);
        this.fetchData = this.fetchData.bind(this);
        this.handleGroupValueChange = this.handleGroupValueChange.bind(this);
    }

    componentDidUpdate = (prevProps) => {
        // Typical usage (don't forget to compare props):
        let shopChange = prevProps.selectedShop != this.props.selectedShop
        let filterChange = prevProps.start_date.getTime() != this.props.start_date.getTime()
            || prevProps.end_date.getTime() != this.props.end_date.getTime()

        filterChange = filterChange || prevProps.repurchase_window != this.props.repurchase_window

        if (shopChange || filterChange) {
            this.fetchData(this.state);
        }
    }

    handleGroupValueChange = (val) => {
        this.setState({group_value : val}, function () {
            this.fetchData(this.state);
        })
    }

    mixpanelTrack = (event_str, props = {}) => {
        tracker.mixpanel.track(event_str, props);
    };

    componentDidMount = () => {
        this.fetchData(this.state, false);
    };

    fetchData = ({group_value}, should_track = true) => {

        const { start_date, end_date, repurchase_window, axiosInstance } = this.props;

        var setState = (newState) => {
            return this.setState(newState);
        };

        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to new request.");
        }

        //Save the cancel token for the current request
        cancelToken = axios.CancelToken.source();

        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            time_frame: "month",
            repurchase_window: repurchase_window,
            group_value : group_value
        };

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, report: "product repurchase rate"});
        }

        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }

        setState({ loading: true });
        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/repurchase", reqData, { cancelToken: cancelToken.token })
            .then(function (response) {

                let missing_product = !response.data || !response.data.product_ltv || response.data.product_ltv.length == 0
                let missing_product_type = !response.data || !response.data.product_type_ltv || response.data.product_type_ltv.length == 0
                if (group_value == 'product' && missing_product) {
                    tracker.event("No Data", {report: "product repurchase rate", missing_value: "product"})
                }

                if (group_value == 'product_type' && missing_product_type) {
                    tracker.event("No Data", {report: "product repurchase rate", missing_value: "product_type"})
                }

                setState({ loading: false, response: response.data });
            })
            .catch((err) => {
                console.error("error", err);
                if (!axios.isCancel(err)) {
                    setState({ loading: false });
                }
            });
    };

    componentWillUnmount = () => {
        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to unmount.");
        }
    }

    exportTrack = () => {
        const { start_date, end_date, shopConfig } = this.props;
        
        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
        };

        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        if (isSubscriptionEnabled) {
            if (!shopConfig.planDetails?.features?.repurchase_rate_export) {
                tracker.event("Paywall", {...reqData, feature : "repurchase_rate_export"});
                NiceModal.show(PaywallDialog, {feature : "repurchase_rate_export"})
                return false
            }
        }

        tracker.event("Data Exported", {...reqData, report: "product repurchase rate"});
    };

    render() {
        const { loading, response , group_value} = this.state;
        const {repurchase_window, setRepurchaseWindow, t, setProductTypes, selectedProductTypes, shopConfig} = this.props;

        let productTableProps = { response, loading, group_value, t, selectedProductTypes };

        let { product_ltv, product_type_ltv } = response;

        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        let isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.repurchase_rate_export ?? false)
        let headers_product = [
            {
                label: t("first-order-product"),
                key: "product_name",
            },
            {
                label: t("product-id"),
                key: "product_id",
            },
            {
                label: t("new-cust"),
                key: "customer_count",
            },
            {
                label: t("repurchasers"),
                key: "repeat_customer_count",
            },
            {
                label: t("repurchase-perc"),
                key: "repeat_percentage",
            },
            {
                label: t("repurchase-same-perc"),
                key: "same_product_repeat_percentage",
            },
            {
                label: t("fo-aov"),
                key: "aov",
            },
            {
                label: t("ltv-90d"),
                key: "ltv_90d",
            },
            {
                label: t("ltv-180d"),
                key: "ltv_180d",
            },
            {
                label: t("ltv"),
                key: "ltv",
            }
        ];


        let headers_product_type = [
            {
                label: t("product-type"),
                key: "product_type",
            },
            {
                label: t("new-cust"),
                key: "customer_count",
            },
            {
                label: t("repurchasers"),
                key: "repeat_customer_count",
            },
            {
                label: t("repurchase-perc"),
                key: "repeat_percentage",
            },
            {
                label: t("repurchase-same-perc"),
                key: "same_product_repeat_percentage",
            },
            {
                label: t("fo-aov"),
                key: "aov",
            },
            {
                label: t("ltv-90d"),
                key: "ltv_90d",
            },
            {
                label: t("ltv-180d"),
                key: "ltv_180d",
            },
            {
                label: t("ltv"),
                key: "ltv",
            }
        ];

        let pillbarOptions = [
            {
                label : t("product"),
                value : "product"
            },
            {
                label : t("product-type"),
                value : "product_type"
            }
        ];

        let repurchaseWindowOptions = [
            {
                label : t("1m"),
                value : "30"
            },
            {
                label : t("2m"),
                value : "60"
            },
            {
                label : t("3m"),
                value : "90"
            },
            {
                label : t("6m"),
                value : "180"
            },
            {
                label : t("1y"),
                value : "365"
            },
            {
                label : t("all-time"),
                value : "100000"
            },
        ];

        let exportable = {
            data : [],
            headers : []
        };

        if (
            group_value == 'product' &&
            response &&
            Object.keys(response).length > 0 &&
            product_ltv &&
            product_ltv.length > 0
        ) {
            exportable.data = product_ltv;
            exportable.headers = headers_product;
        }

        if (
            group_value == 'product_type' &&
            response &&
            Object.keys(response).length > 0 &&
            product_type_ltv &&
            product_type_ltv.length > 0
        ) {
            exportable.data = product_type_ltv;
            exportable.headers = headers_product_type;
        }

        let extraButtons = (
            <div style={{ display: "flex" }}>
                <CSVLink
                    data={exportable.data}
                    headers={exportable.headers}
                    filename={"Products.csv"}
                    onClick={this.exportTrack}
                >
                    <MDBox>
                        <MDButton variant="outlined" color="dark" size="small">
                            <Icon>description</Icon>
                            &nbsp;{t("export-csv")}
                            {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                        </MDButton>
                    </MDBox>
                </CSVLink>
            </div>
        );

        const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        let tooltip_title = t("p-date-block-title-3-m");
        if (time_limit === "1_year") {
            tooltip_title = t("p-date-block-title-1-yr")
        }

        return (
            <>
                <MDBox mb={2} mt={2} >
                <Card elevation={0} mb={4} my={4}>
                    <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center"  px={2} pb={2}>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}} >
                                {t("first-purchase-period")} &nbsp; {isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <DatePickerAnt report="product repurchase rate"/>
                        </Grid>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("breakdown-by")}</MDTypography>
                            <PillBar
                                name="type"
                                isDropdown={false}
                                options={pillbarOptions}
                                value={group_value}
                                onChange={this.handleGroupValueChange}
                            />
                        </Grid>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("repurchased-within")}</MDTypography>
                            <PillBar
                                isDropdown={true}
                                options={repurchaseWindowOptions}
                                value={repurchase_window}
                                onChange={(value) => setRepurchaseWindow(value)}
                            />
                        </Grid>
                        <Grid item xs={6} lg={3} >
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("product-type")}</MDTypography>
                            <MultiProductType setProductTypes={setProductTypes} response={response}/>
                        </Grid>
                    </Grid>
                </Card>
                </MDBox>
                <Row style={{ marginBottom: "10px", marginTop: "10px" }} gutter={10}>
                    <Col span={24}>
                        <Element name="section-product-scatter">
                        <Card sx={{ width: "100%" }}>
                            <MDBox display="flex">
                                <MDTypography variant="h6" sx={{ mt: 2, mb: 1, ml: 2 }} className="card-title-default">
                                    {t("repurchase-rate")}
                                    <MDTypography component="div" variant="button" color="text" fontWeight="regular">
                                        {t("repurchase-rate-desc")}
                                    </MDTypography>
                                </MDTypography>
                            </MDBox>
                            <MDBox py={2}>
                                <FeatureBlurBox feature="repurchase_rate">
                                <ProductScatterPlot {...productTableProps} />
                                </FeatureBlurBox>
                            </MDBox>
                        </Card>
                        </Element>
                    </Col>
                </Row>
                <Row style={{ marginBottom: "10px", marginTop: "25px" }} gutter={10}>
                    <Col span={24}>
                        <Element name="section-product-performance">
                        <Card mt={3}>
                            <MDBox>
                                <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={2} px={2}>
                                    <MDTypography variant="h6" className="card-title-default">
                                        {t("product-perf-repurchase")}
                                        &nbsp;
                                        <MDTooltip
                                            title={
                                                <MDBox display="flex" flexDirection="column" alignItems="center">
                                                    <MDBox  width="2rem" mb={1}>
                                                    <Icon 
                                                        fontSize="large"
                                                        sx={{
                                                            color: "#e91e63",
                                                        }}
                                                        >tips_and_updates</Icon>
                                                    </MDBox>
                                                    <MDTypography variant="caption" fontWeight="medium" color="text" textTransform="capitalize">
                                                        {t("product-repurchase-tip-1")}
                                                    </MDTypography>
                                                    <MDTypography variant="caption" fontWeight="regular" color="text" mt={1}>
                                                        {t("product-repurchase-tip-2")}
                                                    </MDTypography>
                                                    <MDBox mt={2}>
                                                        <MDButton
                                                            component="a"
                                                            href={"http://help.datadrew.io/en/articles/5096099-product-repurchase-analysis"}
                                                            target="_blank"
                                                            rel="noreferrer"
                                                            variant="outlined"
                                                            color={"secondary"}
                                                            size="small"
                                                            sx={{ mt: -0.3, p: "0", m: "0"}}
                                                            onClick={this.mixpanelTrack.bind(this, "View Article", {
                                                                report: "product repurchase rate"
                                                            })}
                                                            >
                                                            {t("more")}
                                                        </MDButton>
                                                    </MDBox>
                                                </MDBox>
                                            } placement="right">
                                            <Icon 
                                                fontSize="medium"
                                                color="light"
                                                sx={{
                                                    fontSize:"1.2rem",
                                                    cursor:"pointer",
                                                    verticalAlign:"middle",
                                                    position:"relative"
                                                }}
                                            >tips_and_updates</Icon>
                                        </MDTooltip>
                                    </MDTypography>
                                    {extraButtons}
                                </MDBox>
                            </MDBox>
                            <Divider />
                            <MDBox>
                                <MDBox px={2}>
                                    <FeatureBlurBox feature="repurchase_rate">
                                        <ProductLTVTable {...productTableProps} />
                                    </FeatureBlurBox>
                                </MDBox>
                            </MDBox>
                        </Card>
                        </Element>
                    </Col>
                </Row>
            </>
        );
    }
}
