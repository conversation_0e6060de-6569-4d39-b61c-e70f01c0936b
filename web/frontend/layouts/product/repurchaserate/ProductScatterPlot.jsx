import React, {useMemo} from "react";
// antd imports
import {Spin, Image} from "antd";

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>Axis,
    YAxis,
    Cell,
    ZAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
} from "recharts";

import CircularProgress from "@mui/material/CircularProgress";
import Empty from "@/components/EmptyChart";
import MDBox from "@/components/MDBox";
import { useTranslation } from "react-i18next";
import {getColors} from "@/util";

const CustomTooltip = ({ active, payload, label }) => {
    const {t} = useTranslation();
    if (active && payload && payload.length) {
        return (
            <div className="custom-tooltip-full" style={{userSelect:"none"}}>
                {!!payload[0].payload.image && <div style={{ height: 250, display: "flex", marginBottom: "5px" }}>
                    <div style={{ alignSelf: "flex-end" }}>
                        <Image
                            width={200}
                            src={payload[0].payload.image}
                            fallback="data:image/png;base64,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"
                        />
                    </div>
                </div>}
                <span style={{ color: "green" }}>
                    <b>{payload[0].payload.product_name}</b>
                    <br />
                </span>
                {payload[0].payload.product_type != '' && <span>{t("product-type")} : <b>{payload[0].payload.product_type}</b><br /></span>}
                {t("new-cust")} : <b>{payload[0].payload.customer_count}</b>
                <br />
                {t("repurchasers")} : <b>{payload[0].payload.repeat_percentage}%</b><br />
                {t("aov-long")} : <b>{payload[0].payload.aov_display}</b><br />
            </div>
        );
    }

    return null;
};

export default function ScatteredProduct({selectedProductTypes, response, loading, group_value, t }) {

    const [hovered_product_type, setHoveredProductType] = React.useState(null);
    let orig_product_ltv = !!response && !!response.product_ltv ? response.product_ltv : []
    let orig_product_type_ltv = !!response && !!response.product_type_ltv ? response.product_type_ltv : [];

    let product_ltv = useMemo(() => {
        if (selectedProductTypes.length == 0) {
            return orig_product_ltv
        }
        return orig_product_ltv.filter(p => selectedProductTypes.indexOf(p.product_type ?? "") != -1)
    }, [selectedProductTypes, orig_product_ltv]);


    let product_type_ltv = useMemo(() => {
        if (selectedProductTypes.length == 0) {
            return orig_product_type_ltv
        }
        return orig_product_type_ltv.filter(p => selectedProductTypes.indexOf(p.product_type ?? "") != -1)
    }, [selectedProductTypes, orig_product_type_ltv]);

    let product_types = {}
    let product_ltv_arr = []
    if (!!product_ltv && product_ltv.length > 0) {
        product_ltv_arr = product_ltv
    }
    for (var k in product_ltv_arr) {
        if (!(product_ltv_arr[k].product_type in product_types)) {
            product_types[product_ltv_arr[k].product_type] = Object.keys(product_types).length
        }
    }

    let top_colors = useMemo(() => {
        return getColors(Object.keys(product_types).length)
    }, [product_types])

    if (
        !response ||
        Object.keys(response).length == 0 ||
        (group_value == "product" && (!product_ltv || product_ltv.length == 0)) || 
        (group_value == "product_type" && (!product_type_ltv || product_type_ltv.length == 0))
    ) {
        if (loading) {
            return (
                <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="500px" height="100%">
                    <CircularProgress color="secondary" />
                </MDBox>
            );
        }

        return <Empty />;
    }

    return (
        <Spin
            indicator={
                <CircularProgress color="secondary" size={30} />
            }
            spinning={loading}
        >
            <ResponsiveContainer width="100%" height={500}>
                <ScatterChart
                    width={400}
                    height={400}
                    onMouseLeave={() => {
                        setHoveredProductType(null)
                    }}
                    margin={{
                        top: 20,
                        right: 20,
                        bottom: 20,
                        left: 20,
                    }}
                >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                        allowDataOverflow
                        type="number"
                        dataKey="customer_count"
                        name={t("cust")}
                        label={{ value: t("new-customers"), position: "bottom", offset: 10 }}
                    />
                    <YAxis
                        allowDataOverflow
                        type="number"
                        yAxisId="1"
                        dataKey="repeat_percentage"
                        name={t("repeat-cust")}
                        unit="%"
                        label={{
                            value: t("perc-cust-repurchased"),
                            angle: -90,
                            position: "insideBottomLeft",
                            dy: -140,
                            dx: 5,
                        }}
                    />
                    <ZAxis
                        allowDataOverflow
                        type="number"
                        scale={"linear"}
                        dataKey="aov"
                        name={t("aov-long")}
                    />
                    <Tooltip
                        cursor={{ strokeDasharray: "3 3" }}
                        content={<CustomTooltip />}
                    />
                    {group_value == "product" && <Scatter 
                        name={t("product")}
                        data={product_ltv}
                        onMouseOver={(e) => {
                            if (e && 'product_type' in e) {
                                setHoveredProductType(e.product_type)
                            }
                        }}
                        onMouseLeave={() => {
                            setHoveredProductType(null)
                        }}
                        fill="#344767"
                        yAxisId="1"
                    >
                        {product_ltv.map((p, index) => (
                            <Cell
                                key={`cell-${index}`}
                                fill={top_colors[product_types[p.product_type] % top_colors.length]}
                                style={{opacity: hovered_product_type && hovered_product_type != p.product_type ? 0.05 : 1, cursor: "pointer"}}
                                />
                        ))}
                    </Scatter>
                    }
                    {/* <Legend /> */}
                    {group_value == "product_type" && <Scatter
                        name={t("product-type")}
                        data={product_type_ltv}
                        fill="#344767"
                        yAxisId="1"
                        style={{cursor : "pointer"}}
                    />}
                </ScatterChart>
            </ResponsiveContainer>
        </Spin>
    );
}