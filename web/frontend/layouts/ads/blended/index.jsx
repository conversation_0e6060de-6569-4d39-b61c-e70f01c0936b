// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import React, {useMemo} from "react";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import {useMaterialUIController} from "@/context";
import {DashboardLoader} from "@/components/AppLoader";
import ReviewBar from "@/examples/ReviewBar";
import AccountAnalytics from "@/layouts/integrations/components/AccountAnalytics";
import {Integrations} from "@/layouts/integrations";
import { SOURCE_FB, SOURCE_GOOGLE_ADS, BLENDED } from "@/layouts/dashboards/metrics/metadata";


export default function BlendedAds() {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, integrations} = controller;

  const loader = integrations.loading ?? false;

  const request_ids = useMemo(() => {
    let fbIntgs = SOURCE_FB in integrations ? integrations[SOURCE_FB] : [];
    let ggIntgs = SOURCE_GOOGLE_ADS in integrations ? integrations[SOURCE_GOOGLE_ADS] : [];
    let request_ids = [];
    for (let intg of fbIntgs) {
      if (intg.synced) {
        request_ids.push(intg.request_id)
      }
    }

    for (let intg of ggIntgs) {
      if (intg.synced) {
        request_ids.push(intg.request_id)
      }
    }
    return request_ids;
  }
  , [integrations]);
  let seeDashboard = request_ids.length > 0;

  return (
      <DashboardLayout>
          <DashboardNavbar />
          <MDBox mt={1.5}>
            {loader && <DashboardLoader />}
            {!loader && !seeDashboard && <Integrations st={`${SOURCE_FB},${SOURCE_GOOGLE_ADS}`} />}
            {!loader && seeDashboard && <AccountAnalytics preset={BLENDED} request_ids={request_ids} />}
          </MDBox>
          {!loader && seeDashboard && <ReviewBar />}
          <Footer />
      </DashboardLayout>
  )
}