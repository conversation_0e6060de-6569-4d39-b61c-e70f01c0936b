
import React, { useMemo, useState, useEffect } from "react";
import numeral from 'numeral';

// @mui material components
import Empty from "@/components/EmptyChart";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Grid from "@mui/material/Grid";
import PillBar from '@/components/PillBar';

// Sales dashboard components
import {  useCancellableAxios, useMaterialUIController } from "@/context";
import { getColors } from "@/util";

import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import axios from 'axios';
import {toast} from "react-toastify";
import dayjs from 'dayjs';
import DataTable from "@/examples/Tables/DataTable";

import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
} from "recharts";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";

// @mui material components
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import { useTranslation } from "react-i18next";
import { getMetricFormatterFn } from '@/util.jsx';
import {BLENDED, getMetricBySlug, SOURCE_FB, SOURCE_GOOGLE_ADS} from "@/layouts/dashboards/metrics/metadata";
import Skeleton from "@mui/material/Skeleton";
import CheckboxMUI from '@mui/material/Checkbox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import FeatureBlurBox from "@/components/FeatureBlurBox";
import {sourceLogo} from "@/layouts/dashboards/metrics/MetricTitle";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" m={0} />;
const checkedIcon = <CheckBoxIcon fontSize="small" m={0} />;

import MetricTitle from "@/layouts/dashboards/metrics/MetricTitle";
import MDTooltip from "@/components/MDTooltip";

const breakdown_config = {
   [SOURCE_FB] :  {
        feature : "facebook_ads_overview",
        metrics: [
          "facebook-marketing:spend",
          "facebook-marketing:clicks",
          "facebook-marketing:impressions",
          "facebook-marketing:link_click",
          "facebook-marketing:add_to_cart",
          "facebook-marketing:purchase",
          "facebook-marketing:landing_page_view",
          "facebook-marketing:website_lead",
          "facebook-marketing:website_view_content",
          "facebook-marketing:website_add_to_cart",
          "facebook-marketing:website_initiate_checkout",
          "facebook-marketing:website_purchase",
          "facebook-marketing:app_view_content",
          "facebook-marketing:app_add_to_cart",
          "facebook-marketing:app_initiate_checkout",
          "facebook-marketing:app_purchase",
          "facebook-marketing:add_to_cart_value",
          "facebook-marketing:website_purchase_value",
          "facebook-marketing:app_purchase_value",
          "facebook-marketing:roas",
          "facebook-marketing:cpc_link",
          "facebook-marketing:cpc_all",
          "facebook-marketing:ctr_link",
          "facebook-marketing:ctr_all",
          "facebook-marketing:cost_per_purchase",
          "facebook-marketing:cost_per_website_purchase",
          "facebook-marketing:cost_per_add_to_cart"
        ],
        default_chart_metrics: [
          "facebook-marketing:spend",
          "facebook-marketing:link_click"
        ],
        breakdowns: {
          "account" : {
              label : "account",
              breakdownId: "facebook-marketing:account_id",
              breakdownName: "facebook-marketing:account_name"
          },
          "campaign" : {
              label : "campaign",
              breakdownId: "facebook-marketing:campaign_id",
              breakdownName: "facebook-marketing:campaign_name"
          },
          "objective" : {
              label : "objective",
              breakdownId: "facebook-marketing:objective",
              breakdownName: "facebook-marketing:objective"
          }
        },
        default_breakdown: "campaign",
        table_metrics: [
          "facebook-marketing:spend",
          "facebook-marketing:impressions",
          "facebook-marketing:link_click",
          "facebook-marketing:ctr_link",
          "facebook-marketing:cpc_link",
          "facebook-marketing:website_purchase",
          "facebook-marketing:website_purchase_value",
          "facebook-marketing:roas"
        ],
        table_sort_default: [{id: "facebook-marketing:spend", desc: true}],
        aggregateRow : (metricList, row1, row2) => {
          let response = {...row1}
          for (var k in row2) {
              if (metricList.indexOf(k) == -1) {
                  continue
              }
      
              if (k in response) {
                  response[k] = parseFloat(response[k]) + parseFloat(row2[k])
              } else {
                  response[k] = row2[k]
              }
          }
          
          response["facebook-marketing:cpc_link"] = "facebook-marketing:link_click" in response && response["facebook-marketing:link_click"] > 0 ? response["facebook-marketing:spend"] / response["facebook-marketing:link_click"] : 0;
          response["facebook-marketing:cpc_all"] = "facebook-marketing:clicks" in response && response["facebook-marketing:clicks"] > 0 ? response["facebook-marketing:spend"] / response["facebook-marketing:clicks"] : 0;
          response["facebook-marketing:ctr_link"] = "facebook-marketing:impressions" in response && response["facebook-marketing:impressions"] > 0 ? (100 * response["facebook-marketing:link_click"] / response["facebook-marketing:impressions"]) : 0;
          response["facebook-marketing:ctr_all"] = "facebook-marketing:impressions" in response && response["facebook-marketing:impressions"] > 0 ? (100 * response["facebook-marketing:clicks"] / response["facebook-marketing:impressions"]) : 0;
          response["facebook-marketing:roas"] = "facebook-marketing:spend" in response && response["facebook-marketing:spend"] > 0 ? response["facebook-marketing:website_purchase_value"] / response["facebook-marketing:spend"] : 0;
          response["facebook-marketing:cost_per_purchase"] = "facebook-marketing:purchase" in response && response["facebook-marketing:purchase"] > 0 ? response["facebook-marketing:spend"] / response["facebook-marketing:purchase"] : 0;
          response["facebook-marketing:cost_per_website_purchase"] = "facebook-marketing:website_purchase" in response && response["facebook-marketing:website_purchase"] > 0 ? response["facebook-marketing:spend"] / response["facebook-marketing:website_purchase"] : 0;
          response["facebook-marketing:cost_per_add_to_cart"] = "facebook-marketing:add_to_cart" in response && response["facebook-marketing:add_to_cart"] > 0 ? response["facebook-marketing:spend"] / response["facebook-marketing:add_to_cart"] : 0;
          return response
      }
   },
   [SOURCE_GOOGLE_ADS] : {
      feature : "google_ads_overview",
      metrics: [
        "google-ads:cost",
        "google-ads:clicks",
        "google-ads:impressions",
        "google-ads:video_views",
        "google-ads:interactions",
        "google-ads:active_view_impressions",
        "google-ads:active_view_measurable_impressions",
        "google-ads:conversions",
        "google-ads:conversions_value",
        "google-ads:active_view_measurable_cost",
        "google-ads:ctr",
        "google-ads:cpc",
        "google-ads:cpm",
        "google-ads:cost_per_conversion",
        "google-ads:avg_conversion_value",
        "google-ads:revenue_per_click",
        "google-ads:rpm",
        "google-ads:roas",
        "google-ads:order_rate",
        "google-ads:active_view_cpm",
        "google-ads:cost_per_interaction",
        "google-ads:value_per_conversion",
        "google-ads:active_view_viewability",
        "google-ads:active_view_measurability"
      ],
      default_chart_metrics: [
        "google-ads:cost",
        "google-ads:clicks"
      ],
      breakdowns: {
        "account" : {
            label : "account",
            breakdownId: "google-ads:customer_id",
            breakdownName: "google-ads:customer_descriptive_name"
        },
        "campaign" : {
            label : "campaign",
            breakdownId: "google-ads:campaign_id",
            breakdownName: "google-ads:campaign_name"
        },
        "ad_network_type" : {
            label : "ad-network-type",
            breakdownId: "google-ads:segments_ad_network_type",
            breakdownName: "google-ads:segments_ad_network_type"
        },
      },
      default_breakdown: "campaign",
      table_metrics: [
        "google-ads:cost",
        "google-ads:impressions",
        "google-ads:clicks",
        "google-ads:ctr",
        "google-ads:cpc",
        "google-ads:conversions",
        "google-ads:conversions_value",
        "google-ads:roas"
      ],
      table_sort_default: [{id: "google-ads:cost", desc: true}],
      aggregateRow : (metricList, row1, row2) => {
        let response = {...row1}
        for (var k in row2) {
            if (metricList.indexOf(k) == -1) {
                continue
            }
    
            if (k in response) {
                response[k] = parseFloat(response[k]) + parseFloat(row2[k])
            } else {
                response[k] = row2[k]
            }
        }
    
        response["google-ads:ctr"] = "google-ads:impressions" in response && response["google-ads:impressions"] > 0 ? (100 * response["google-ads:clicks"] / response["google-ads:impressions"]) : 0;
        response["google-ads:cpc"] = "google-ads:clicks" in response && response["google-ads:clicks"] > 0 ? response["google-ads:cost"] / response["google-ads:clicks"] : 0;
        response["google-ads:cpm"] = "google-ads:impressions" in response && response["google-ads:impressions"] > 0 ? (1000 * response["google-ads:cost"] / response["google-ads:impressions"]) : 0;
        response["google-ads:cost_per_conversion"] = "google-ads:conversions" in response && response["google-ads:conversions"] > 0 ? response["google-ads:cost"] / response["google-ads:conversions"] : 0;
        response["google-ads:avg_conversion_value"] = "google-ads:conversions" in response && response["google-ads:conversions"] > 0 ? response["google-ads:conversions_value"] / response["google-ads:conversions"] : 0;
        response["google-ads:revenue_per_click"] = "google-ads:clicks" in response && response["google-ads:clicks"] > 0 ? response["google-ads:conversions_value"] / response["google-ads:clicks"] : 0;
        response["google-ads:rpm"] = "google-ads:impressions" in response && response["google-ads:impressions"] > 0 ? (1000 * response["google-ads:conversions_value"] / response["google-ads:impressions"]) : 0;
        response["google-ads:roas"] = "google-ads:cost" in response && response["google-ads:cost"] > 0 ? response["google-ads:conversions_value"] / response["google-ads:cost"] : 0;
        response["google-ads:order_rate"] = "google-ads:clicks" in response && response["google-ads:clicks"] > 0 ? (100 * response["google-ads:conversions"] / response["google-ads:clicks"]) : 0;
        response["google-ads:active_view_cpm"] = "google-ads:active_view_measurable_impressions" in response && response["google-ads:active_view_measurable_impressions"] > 0 ? (1000 * response["google-ads:active_view_measurable_cost"] / response["google-ads:active_view_measurable_impressions"]) : 0;
        response["google-ads:cost_per_interaction"] = "google-ads:interactions" in response && response["google-ads:interactions"] > 0 ? response["google-ads:cost"] / response["google-ads:interactions"] : 0;
        response["google-ads:value_per_conversion"] = "google-ads:conversions" in response && response["google-ads:conversions"] > 0 ? response["google-ads:conversions_value"] / response["google-ads:conversions"] : 0;
        response["google-ads:active_view_viewability"] = "google-ads:active_view_measurable_impressions" in response && response["google-ads:active_view_measurable_impressions"] > 0 ? (100 * response["google-ads:active_view_impressions"] / response["google-ads:active_view_measurable_impressions"]) : 0;
        response["google-ads:active_view_measurability"] = "google-ads:impressions" in response && response["google-ads:impressions"] > 0 ? (100 * response["google-ads:active_view_measurable_impressions"] / response["google-ads:impressions"]) : 0;
    
        return response
      }
   },
   [BLENDED] : {
      feature : "blended_ads_overview",
      metrics: [
        "ad:spend",
        "ad:impressions",
        "ad:clicks",
        "ad:ctr",
        "ad:cpc",
        "ad:cpm",
        "ad:conversions_value",
        "ad:conversions",
        "ad:aov",
        "ad:roas",
        "ad:order_rate",
        "ad:cac"
      ],
      default_chart_metrics: [
        "ad:spend",
        "ad:clicks"
      ],
      breakdowns: {
        "channel" : {
            label : "channel",
            breakdownId: "ad:channel",
            breakdownName: "ad:channel"
        },
        "campaign" : {
            label : "campaign",
            breakdownId: "ad:campaign_id",
            breakdownName: "ad:campaign_name"
        },
      },
      default_breakdown: "campaign",
      table_metrics: [
        "ad:spend",
        "ad:impressions",
        "ad:clicks",
        "ad:ctr",
        "ad:cpc",
        "ad:cpm",
        "ad:conversions_value",
        "ad:conversions",
        "ad:aov",
        "ad:roas"
      ],
      table_sort_default: [{id: "ad:spend", desc: true}],
      aggregateRow : (metricList, row1, row2) => {
        let response = {...row1}
        for (var k in row2) {
            if (metricList.indexOf(k) == -1) {
                continue
            }
    
            if (k in response) {
                response[k] = parseFloat(response[k]) + parseFloat(row2[k])
            } else {
                response[k] = row2[k]
            }
        }
    
        response["ad:cac"] = "ad:conversions" in response && response["ad:conversions"] > 0 ? response["ad:spend"] / response["ad:conversions"] : 0;
        response["ad:order_rate"] = "ad:clicks" in response && response["ad:clicks"] > 0 ? (100 * response["ad:conversions"] / response["ad:clicks"]) : 0;
        response["ad:aov"] = "ad:conversions" in response && response["ad:conversions"] > 0 ? response["ad:conversions_value"] / response["ad:conversions"] : 0;
        response["ad:cpm"] = "ad:impressions" in response && response["ad:impressions"] > 0 ? (1000 * response["ad:spend"] / response["ad:impressions"]) : 0;
        response["ad:ctr"] = "ad:impressions" in response && response["ad:impressions"] > 0 ? (100 * response["ad:clicks"] / response["ad:impressions"]) : 0;
        response["ad:cpc"] = "ad:clicks" in response && response["ad:clicks"] > 0 ? response["ad:spend"] / response["ad:clicks"] : 0;
        response["ad:roas"] = "ad:spend" in response && response["ad:spend"] > 0 ? response["ad:conversions_value"] / response["ad:spend"] : 0;
        return response
      }
    }
}

const CustomLineTooltip = ({
    active,
    payload,
    label,
    currency
}) => {

    if (!active || !payload || !payload.length) {
        return null;
    }

    return (
        <Card raised style={{backgroundColor: "#f0f2f5"}}>
        <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="left" style={{userSelect: "none"}} >
            <TableContainer sx={{ height: "100%", boxShadow: "none", borderTopLeftRadius:"0", borderTopRightRadius:"0"}}>
            {label && <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" style={{userSelect: "none"}} p={1} >
                <MDTypography variant="button" fontWeight="medium">
                    {label}
                </MDTypography>
            </MDBox>}
            <Table size="small" >
                <TableBody>
                    {payload && payload.length > 0 && payload.map((p, pi) => {

                        let metric = getMetricBySlug(p.dataKey)
                        let formatter = getMetricFormatterFn(metric.format_type)
                        return (
                            <TableRow key={pi}>
                                <TableCell padding="none" align="center" sx={{ color : p.color }}>
                                    <MDBox display="flex" flexDirection="column">
                                        <MDTypography
                                            variant="caption"
                                            textAlign="left"
                                            color="text"
                                            fontWeight="medium"
                                            textTransform="capitalize"
                                            style={{color : p.color, fontSize: "12px", width: "120px", whiteSpace: "nowrap"}}
                                        >
                                            {p.name}
                                        </MDTypography>
                                    </MDBox>
                                </TableCell>
                                <TableCell padding="none" align="center" sx={{ color : p.color }}>
                                    <MDBox display="flex" flexDirection="column">
                                        <MDTypography variant="button" fontWeight="regular" textTransform="capitalize" textAlign="right">
                                            {formatter(p.value, currency)}
                                        </MDTypography>
                                    </MDBox>
                                </TableCell>
                            </TableRow>
                        )
                    })}
                </TableBody>
            </Table>
            </TableContainer>
        </MDBox>
        </Card>
    );
}

function DataTableRoot({data, breakdowns, breakdown, setSelectedBreakdown, selectedBreakdown, table_metrics, table_sort_default, currency}) {
    const {t} = useTranslation();

    const breakdownNameCell = ({value, row}) => {

        if (!value) {
            return <MDBox> </MDBox>
        }

        let name = value
        if (name.length > 35) {
            name = name.substring(0, 35) + "..."
        }

        let logo = null;
        if (row.original?.source) {
            logo = (
                <MDBox
                    component="img"
                    src={sourceLogo(row.original.source)}
                    alt="logo"
                    mr={0.8}
                    width="1rem" />
            )
        }

        return (
            <MDTooltip title={value}>
                <MDBox display="flex" alignItems="center">
                    {logo}
                    <MDBox>
                        {name}
                    </MDBox>
                </MDBox>
            </MDTooltip>
        )
    }

    const cellMetricFormatter = (m) => {
        return ({value}) => {
            let metric = getMetricBySlug(m)
            const formatter = getMetricFormatterFn(metric.format_type)
            return <MDTooltip title={numeral(value).format('0,0.[00]')}>
                <MDBox>{formatter(value, currency)}</MDBox>
            </MDTooltip>
        }
    }

    const cellHeader = (slug) => {
        return <MetricTitle metric={slug} removeLogoInTitle={true}/>;
    }

    const cellHeader2 = (val) => {
        return (
            <MDTypography variant="caption" textTransform="capitalize" color="dark" fontWeight="bold">{val}</MDTypography>
        )
    }

    const checkboxNodeCell = ({value}) => {

        const handleBreakdownSelect = (e) => {
            console.log(e.target.checked, e.target.value)
            if (e.target.checked) {
                setSelectedBreakdown([...selectedBreakdown, e.target.value])
            } else {
                setSelectedBreakdown(selectedBreakdown.filter((v) => v != e.target.value))
            }
        }


        const stringValue = String(value); // Convert the value to a string

        return (
            <MDBox display="flex" alignItems="center" justifyContent="center" sx={{textAlign: "center"}}>
            <CheckboxMUI
                size="small"
                icon={icon}
                checkedIcon={checkedIcon}
                sx={{ m: 0, p:0 }}
                checked={selectedBreakdown.indexOf(stringValue) != -1}
                value={stringValue}
                onChange={handleBreakdownSelect}
            />
            </MDBox>
        )
    }

    const breakdownIdField = breakdowns[breakdown].breakdownId
    const breakdownNameField = breakdowns[breakdown].breakdownName

    let initialState = {}
    if (table_sort_default) {
        initialState = {
            sortBy: table_sort_default
        }
    }

    const cellHeaderCheckbox = (
      <MDBox sx={{textAlign: "center"}}>
        <CheckboxMUI
            size="small"
            icon={icon}
            checkedIcon={checkedIcon}
            sx={{ m: 0, p:0, textAlign: "center"}}
            checked={selectedBreakdown.length == data.length}
            value="all"
            onChange={(e) => {
                if (e.target.checked) {
                    setSelectedBreakdown(data.map((d) => String(d[breakdownIdField])))
                } else {
                    setSelectedBreakdown([])
                }
            }}
        />
      </MDBox>
    );

    const columns = [
        { Header: cellHeaderCheckbox, accessor: breakdownIdField, Cell: checkboxNodeCell, id:"breakdownId", disableSortBy: true},
        { Header: cellHeader2(t(breakdown)), accessor: breakdownNameField, Cell: breakdownNameCell, className:"priority-cell", id:"breakdownName", disableSortBy: true}
    ];

    const dataTableData = {
        columns: columns,
        rows: data
    };

    // pushing the metrics
    table_metrics.forEach((m) => {
        let clm = { Header: cellHeader(m), accessor: m, Cell: cellMetricFormatter(m), id: m}
        dataTableData.columns.push(clm)
    })

    if (!data || data.length == 0) {
        return null
    }

    let entriesPerPage = {
        defaultValue: 25,
        entries: [10, 25, 50, 100, 200, 500],
    };

    if (data.length < entriesPerPage.defaultValue) {
        entriesPerPage = false;
    }

    return (
        <DataTable
            canSearch
            isBasic={true}
            table={dataTableData}
            entriesPerPage={entriesPerPage}
            showTotalEntries={false}
            isSorted={true}
            noEndBorder
            pagination={{
                variant: "contained",
                color: "light",
            }}
            initialState={initialState}
        />
    )
}

export default function PivotDashboard({integrationRequestIds, preset}) {

  if (!(preset in breakdown_config)) {
    return null;
  }

  if (!integrationRequestIds || integrationRequestIds.length == 0) {
    return <Empty />;
  }

  return (
    <PivotDashboardRoot
      integrationRequestIds={integrationRequestIds}
      preset={preset}
    />
  );
}

function PivotDashboardRoot({integrationRequestIds, preset}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedFilters, selectedShop} = controller;
    const {start_date, end_date, time_frame} = selectedFilters;

    const {
      feature,
      default_chart_metrics,
      default_breakdown,
      breakdowns,
      aggregateRow,
      table_metrics,
      table_sort_default,
      metrics
    } = breakdown_config[preset];

    const [loading, setLoading]  = useState(true)
    const [breakdownData, setBreakdownData] = useState({})
    const [chartMetric, setChartMetric] = useState(default_chart_metrics[0])
    const [chartMetric1, setChartMetric1] = useState(default_chart_metrics[1])
    const [breakdown, setBreakdown] = useState(default_breakdown)
    const {t} = useTranslation();
    const [selectedBreakdown, setSelectedBreakdown] = React.useState([]);
    const [shouldTrack, setShouldTrack] = useState(false)
    const axiosInstance = useCancellableAxios();

    const {currency = "", current = []} = breakdownData;
    const data = current ?? [];

    const fetchPivotData = () => {
        let reqData = {
            type: "breakdown_data",
            filters : {
                start_date: dayjs(start_date).format("YYYY-MM-DD"),
                end_date: dayjs(end_date).format("YYYY-MM-DD"),
                time_frame,
                breakdown,
                preset,
                integration_request_ids : integrationRequestIds
            },
        };
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }
    
        setLoading(true);
        axiosInstance.post('/api/report/data', reqData)
            .then((response) => {
                if (!response.data || response.data.error) {
                    toast.error(t("something-went-wrong"));
                    return;
                }

                setBreakdownData(response.data?.data ?? {});
                setShouldTrack(true)
                setLoading(false);
            })
            .catch((error) => {
                !axios.isCancel(error) && toast.error(t("something-went-wrong"));
                console.log(error)
                setLoading(false);
          })
    }

    useEffect(() => {
        fetchPivotData();
        return () => {
            setLoading(false);
            setShouldTrack(false);
            setBreakdownData([]);
        };

    }, [start_date, end_date, time_frame, breakdown, selectedShop, integrationRequestIds]);

    useEffect(() => {
        setSelectedBreakdown([])
    }, [selectedShop, integrationRequestIds])

    const breakdownIdField = breakdowns[breakdown].breakdownId

    let bucketAggData = useMemo(() => {
        let aggData = {}
        for (var k in data) {
            let row = data[k]

            if (selectedBreakdown.length > 0 && !selectedBreakdown.includes(String(row[breakdownIdField]))) {
                continue
            }

            let bucketId = row["bucketId"]
            if (!(bucketId in aggData)) {
                aggData[bucketId] = row
            } else {
                aggData[bucketId] = aggregateRow(metrics, aggData[bucketId], row)
            }
        }

        aggData = Object.values(aggData)

        // sort by bucketId
        return aggData.sort((a, b) => {
            return a.bucketId.localeCompare(b.bucketId)
        })

    }, [data, selectedBreakdown])

    let dataChart = useMemo(() => {
        let linesData = {}
        for (var k in bucketAggData) {
            let row = bucketAggData[k]

            if (!(row.bucketName in linesData)) {
                linesData[row.bucketName] = {
                    bucketName : row.bucketName,
                    bucketNameShort: row.bucketNameShort
                }
            }

            let bucket = linesData[row.bucketName]

            if (chartMetric in row) {
                bucket[chartMetric] = row[chartMetric]
            }

            if (chartMetric1 in row) {
                bucket[chartMetric1] = row[chartMetric1]
            }
            linesData[row.bucketName] = bucket
        }

        let chartData = {linesData : Object.values(linesData), currency}

        return (
            <DataChartRoot
                t={t}
                data={chartData}
                m1={chartMetric}
                m2={chartMetric1}
                metric={chartMetric} />
        )
    }, [bucketAggData, chartMetric, chartMetric1, t])


    let dataTableData = useMemo(() => {

        // Aggregate data on breakdownId - so different buckets are aggregated
        let aggData = {}
        for (var k in data) {
            let row = data[k]
            let breakdownIdValue = row[breakdownIdField]
            if (!(breakdownIdValue in aggData)) {
                aggData[breakdownIdValue] = row
            } else {
                aggData[breakdownIdValue] = aggregateRow(metrics, aggData[breakdownIdValue], row)
            }
        }

        aggData = Object.values(aggData)
        return aggData;
    }, [data])

    let breakdownOptions = Object.keys(breakdowns).map((p) => {
        return {
            label : t(breakdowns[p].label),
            value : p
        }
    });

    let handleBreakdownChange = (value) => {
        setSelectedBreakdown([])
        setBreakdown(value)
    }

    return (
      <MDBox>
            <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
            <Grid item>
                <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                    {t("campaign-performance")}
                </MDTypography>
            </Grid>
            </Grid>
            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
            {loading && <MDBox display="flex" borderRadius="lg" justifyContent="center" alignItems="center" p={2}><Skeleton variant="rectangular" height={300} width={"95%"} sx={{borderRadius:"10px"}} /></MDBox>}
            {!loading && <Grid container spacing={1.2} p={2} pb={2.4}>
                <Grid item xs={12} lg={12}>
                    {(!!data && data.length > 0)
                        ? <FeatureBlurBox feature={feature} >
                            <MDBox pt={2} className="pivot-chart-box">
                                <MDBox display="flex" alignItems="center" justifyContent="space-between" p={2}>
                                    <MetricSelector setChartMetric={setChartMetric} chartMetric={chartMetric} key={"m1"} metrics={metrics} />
                                    <MetricSelector setChartMetric={setChartMetric1} chartMetric={chartMetric1} key={"m2"} metrics={metrics} />
                                </MDBox>
                                {dataChart}
                            </MDBox>
                        </FeatureBlurBox>
                        : <MDBox pt={2} className="pivot-chart-box"><Empty /></MDBox>
                    }
                </Grid>
            </Grid>}
            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
            <MDBox py={1.5} display="flex" justifyContent="center" alignItems="center">
                <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("breakdown-by")}&nbsp;&nbsp;</MDTypography>
                <PillBar
                    name="breakdown"
                    options={breakdownOptions}
                    value={breakdown}
                    onChange={handleBreakdownChange}
                />
            </MDBox>
            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
            {/* {loading && <CardLoader height={300} />} */}
            {!loading && <MDBox p={2} pt={1} display="flex" direction="row" justifyContent="space-between" alignItems="center" sx={{width :"100%"}}>
                <DataTableRoot
                    currency={currency}
                    data={dataTableData}
                    breakdowns={breakdowns}
                    breakdown={breakdown}
                    table_metrics={table_metrics}
                    table_sort_default={table_sort_default}
                    selectedBreakdown={selectedBreakdown}
                    setSelectedBreakdown={setSelectedBreakdown}/>
            </MDBox>}
      </MDBox>
    );
}

function MetricSelector({metrics, setChartMetric, chartMetric}) {
    const {t} = useTranslation();

    let optionList = metrics.map((m) => {
        const metric = getMetricBySlug(m)
        return {label : t(metric.title), value : m}
    })

    let chartMetricOption = optionList.find((o) => o.value == chartMetric)
    return (
        <Autocomplete
            options={optionList}
            blurOnSelect
            disableClearable={true}
            onChange={(event, newValue) => {
                if (newValue && newValue.length == 0) {
                    setChartMetric(null);
                }
                if (newValue && newValue.value) {
                    setChartMetric(newValue.value);
                } else {
                    setChartMetric(null);
                }
            }}
            getOptionLabel={(option) => {
                return option.label ?? ""
            }}
            isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
            }}
            sx={{
                width: 200,
                height: 45,
                fontSize: 14,
            }}
            value={chartMetricOption}
            renderInput={(params) => (
                <TextField {...params} size="small"/>
            )}
        />
    );
}

class DataChartRoot extends React.Component {
    constructor(props) {
        super(props);
    }

    renderLineChart = () => {
        const {
            t,
            data,
            m1,
            m2
        } = this.props;

        let {linesData, currency} = data
        const metrics = [m1, m2];
        const metricConfigs = metrics.map((m) => getMetricBySlug(m));
        const colorArr = getColors(2);

        const Lines = metrics.map((m, mi) => {
            return (
                <Line
                    yAxisId={`a${mi}`}
                    type="monotoneX"
                    strokeWidth={2}
                    dot={false}
                    hide={false}
                    name={t(metricConfigs[mi].title)}
                    key={m}
                    metric={m}
                    dataKey={m}
                    stroke={colorArr[mi]}
                />
            );
        });

        return (
            <ResponsiveContainer width="95%" height={300} style={{fontSize:"14px"}}>
                <LineChart
                    data={linesData}
                    syncId="anyId"
                    margin={{ top: 10, right: 0, left: 0, bottom: 50 }}
                >
                    <CartesianGrid strokeDasharray="4 4" />
                    <Tooltip
                        filterNull
                        wrapperStyle={{zIndex: 1000}} 
                        content={
                            <CustomLineTooltip currency={currency}/>
                        }
                    />
                    <XAxis
                        dataKey="bucketName"
                        padding={{ right: 20 }}
                        label={{
                            value: ``,
                            position: "bottom",
                            offset: 0,
                        }}
                    />
                    <YAxis
                        yAxisId={`a0`}
                        orientation={"left"}
                        tickFormatter={(value) => {
                            let formatter = getMetricFormatterFn(metricConfigs[0].format_type)
                            return formatter(value, currency)
                        }}
                        width={80}
                    />
                    <YAxis
                        yAxisId={`a1`}
                        orientation={"right"}
                        tickFormatter={(value) => {
                            let formatter = getMetricFormatterFn(metricConfigs[1].format_type)
                            return formatter(value, currency)
                        }}
                        width={80}
                    />
                    {Lines}
                    <Legend iconSize={10}
                            iconType="square"
                            align="center"
                            verticalAlign="bottom"
                            layout="horizontal"
                            wrapperStyle={{paddingLeft:0, paddingTop: 0}} />
                </LineChart>
            </ResponsiveContainer>
        );
    };

    render() {
        return (
            <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mt={1}>
                {this.renderLineChart()}
            </MDBox>
        );
    }
}