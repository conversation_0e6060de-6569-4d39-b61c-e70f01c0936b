import { useState, useEffect } from "react";

// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";

import {useTranslation} from "react-i18next";

import {useMaterialUIController} from "@/context";

const Feedback = () => {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, loginConfig} = controller;
    let myshopify_domain = selectedShop
    let onboard_email = ""
    let onboard_name = ""
    if (!!loginConfig.userData && !!loginConfig.userData.email) {
        myshopify_domain = loginConfig.userData.email
        onboard_email = loginConfig.userData.onboard_email
        onboard_name = loginConfig.userData.onboard_name
    } else if (!!loginConfig.shop && !!loginConfig.shop.myshopify_domain) {
        myshopify_domain = loginConfig.shop.myshopify_domain
        onboard_email = loginConfig.shop.onboard_email
        onboard_name = loginConfig.shop.onboard_name
    }

    // only use first name if name is present
    if (!!onboard_name) {
        onboard_name = onboard_name.split(' ')[0]
    }


  useEffect(() => {
    (function(w,d,i,s){function l(){if(!d.getElementById(i)){var f=d.getElementsByTagName(s)[0],e=d.createElement(s);e.type="text/javascript",e.async=!0,e.src="https://canny.io/sdk.js",f.parentNode.insertBefore(e,f)}}if("function"!=typeof w.Canny){var c=function(){c.q.push(arguments)};c.q=[],w.Canny=c,"complete"===d.readyState?l():w.attachEvent?w.attachEvent("onload",l):w.addEventListener("load",l,!1)}})(window,document,"canny-jssdk","script");

    Canny('identify', {
        appID: '642574368d8e6dac6a178296',
        user: {
        //   avatarURL: viewer.avatarURL, // optional
        //   companies: viewer.companies.map((company) => ({
        //     // created: new Date(company.created).toISOString(), // optional
        //     customFields: {
        //       companyType: 'b2b',
        //       employees: 100,
        //       industry: 'technology',
        //     },
        //     id: company.id,
        //     monthlySpend: company.monthlySpend, // optional
        //     name: company.name,
        //   })),
        //   created: new Date(viewer.created).toISOString(), // optional
          customFields: {
            myshopify_domain: myshopify_domain,
          },
          email: onboard_email,
          id: myshopify_domain,
          name: onboard_name,
        },
    });

    Canny('render', {
      boardToken: '************************************',
      basePath: null, // See step 2
      ssoToken: null, // See step 3,
      theme: 'light', // options: light [default], dark, auto
    });
  }, []);

  return (
    <div data-canny />
  );
}

function FeatureRequests() {

    const {t} = useTranslation();

    return (
        <DashboardLayout>
        <DashboardNavbar />
      <MDBox mt={6} mb={10} height="80vh">
            <Grid container spacing={3} justifyContent="center" alignItems={"center"}>
                <Grid item xs={12}>
                    <MDBox m={2} px={8}>
                        <Feedback />
                    </MDBox>
                </Grid>
            </Grid>
        </MDBox>
        <Footer />
        </DashboardLayout>
    );
}

export default FeatureRequests;
