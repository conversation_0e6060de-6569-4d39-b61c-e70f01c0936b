import { MessagePrimitive } from "@assistant-ui/react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Avatar from 'boring-avatars';
import MDAvatar from "@/components/MDAvatar";
import { useMaterialUIController } from "@/context";
import {Avatar as AntAvatar} from "antd";

function UserAvatar() {
    const [controller] = useMaterialUIController();
    const {loginConfig, shopConfig} = controller;

    const displayName = shopConfig.shop.name;

    return (
        <MDAvatar 
            size="sm" 
            shadow="sm" 
            sx={{
                backgroundColor: "#e91e63",
                verticalAlign: 'middle',
                justifyContent: "middle",
                textTransform: "uppercase"
            }}
        >
            {displayName.length > 0 ? displayName[0] : "x"}
        </MDAvatar>
    );
}

export default function UserMessage() {
  return (
    <MessagePrimitive.Root className="relative mb-8 flex w-full max-w-4xl gap-4 font-size-12 bg-gray-100 p-4 rounded-lg" display="flex">
      <UserAvatar />
      <MDBox className="flex-grow space-y-4" display="flex" verticalAlign="middle">
        <MDBox className="space-y-2">
          <MDTypography variant="button" fontWeight="regular" sx={{fontSize: "15px !important", lineHeight: "30px !important"}}>
            <MessagePrimitive.Content />
          </MDTypography>
        </MDBox>
      </MDBox>
    </MessagePrimitive.Root>
  );
}
