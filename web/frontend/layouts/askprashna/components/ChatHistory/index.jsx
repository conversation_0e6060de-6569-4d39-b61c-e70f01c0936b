import { useTranslation } from "react-i18next";
import { useState } from "react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDInput from "@/components/MDInput";
import Grid from "@mui/material/Grid";
import { ThreadList } from "@assistant-ui/react";
import { CompactThreadListItem } from "@/layouts/askprashna/components/MyAssistant";
import Card from "@mui/material/Card";
import Skeleton from "@mui/material/Skeleton";
import SearchIcon from "@mui/icons-material/Search";
import InputAdornment from "@mui/material/InputAdornment";

const ChatHistorySkeleton = () => {
  return (
    <Grid container spacing={2} sx={{ mt: 2 }}>
      {Array.from({ length: 6 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card 
            sx={{ 
              height: 130, 
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: "10px",
              boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
              border: "1px solid rgba(0,0,0,0.08)",
              position: "relative"
            }}
          >
            <MDBox sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
              <Skeleton variant="text" width="70%" height={24} />
            </MDBox>
            <Skeleton variant="text" width="90%" />
            <Skeleton variant="text" width="60%" />
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

const ChatHistory = ({ 
  threads = [], 
  threadsLoading = false, 
  onClose,
  searchTerm = "", 
  onSearchChange = () => {} 
}) => {
  const { t } = useTranslation();

  const handleSearchChange = (event) => {
    onSearchChange(event.target.value);
  };

  return (
    <MDBox id="history" sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <MDBox p={3} mb={1} sx={{ borderBottom: "1px solid rgba(0, 0, 0, 0.06)" }}>
        <MDTypography variant="h4" fontWeight="medium" mb={3}>
          {t("prashna.threads.all-conversations")}
        </MDTypography>
        
        <MDInput
          placeholder="Search conversations..."
          fullWidth
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" color="action" />
              </InputAdornment>
            ),
          }}
        />
      </MDBox>

      <MDBox 
        sx={{ 
          flex: 1, 
          overflowY: "auto", 
          px: 3, 
          pb: 3, 
          pt: 2,
          maxHeight: "calc(100% - 120px)"  // Ensure proper scrolling height
        }}
      >
        {threadsLoading ? (
          <ChatHistorySkeleton />
        ) : (
          <ThreadList.Root>
            <Grid container spacing={2}>
              {threads.length > 0 ? (
                <ThreadList.Items
                  threads={threads}
                  components={{
                    ThreadListItem: (props) => (
                      <Grid item xs={12} sm={6} md={4}>
                        <CompactThreadListItem {...props} onSelect={onClose} />
                      </Grid>
                    )
                  }}
                />
              ) : (
                <Grid item xs={12}>
                  <MDBox 
                    sx={{ 
                      textAlign: "center", 
                      py: 5, 
                      display: "flex", 
                      flexDirection: "column", 
                      alignItems: "center",
                      justifyContent: "center",
                      height: "200px"
                    }}
                  >
                    <SearchIcon sx={{ fontSize: '3rem', opacity: 0.2, mb: 2 }} />
                    <MDTypography variant="h6" color="secondary" fontWeight="regular">
                      No conversations found matching your search
                    </MDTypography>
                    <MDButton 
                      variant="text" 
                      color="info" 
                      onClick={() => {
                        onSearchChange("");
                      }}
                      sx={{ mt: 1 }}
                    >
                      Clear filters
                    </MDButton>
                  </MDBox>
                </Grid>
              )}
            </Grid>
          </ThreadList.Root>
        )}
      </MDBox>
    </MDBox>
  );
};

export default ChatHistory; 