import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import {
  useMessage,
  MessagePrimitive,
} from "@assistant-ui/react";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";
import { StreamText, AnimatedMarkdown } from "flowtoken";
import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController } from "@/context/index.jsx";
import Report from "@/components/Report";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Dialog from '@mui/material/Dialog';
import MDButton from "@/components/MDButton";
import Divider from "@mui/material/Divider";
import { highlightSQL, sqlHighlightStyles } from "@/utils/sqlSyntaxHighlighter.jsx";
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { CSVLink } from "react-csv";
import MDProgress from "@/components/MDProgress";


const FinalAnswerText = (props) => {
  return (
    <MDBox my={2}>
        <AnimatedMarkdown
          key="final-answer"
          content={props.text}
          sep="word"
          animation="fadeIn"
          animationDuration="0.5s"
          animationTimingFunction="ease-in-out"
        />
    </MDBox>
  );
};

const Steps = ({ steps, final_answer, progress_value }) => {
    const [isCollapsed, setIsCollapsed] = useState(true);

    useEffect(() => {
      if (progress_value === 100) {
        const timer = setTimeout(() => {
          setIsCollapsed(true);
        }, 4000); // Same timing as progress bar fade
        return () => clearTimeout(timer);
      } else if (progress_value > 0 && progress_value < 100) {
        setIsCollapsed(false);
      }
    }, [progress_value]);

    if (final_answer && steps.length <= 1) {
      return null;
    }

    return (
      <MDBox mt={1}>
        <MDBox
          display="flex"
          alignItems="center"
          mb={1}
          sx={{ cursor: 'pointer' }}
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {isCollapsed ? (
            <ExpandMoreIcon sx={{ mr: 1, color: 'text.secondary' }} />
          ) : (
            <ExpandLessIcon sx={{ mr: 1, color: 'text.secondary' }} />
          )}
          <MDTypography variant="button" fontWeight="medium" color="text">
            Prashna AI Thoughts
          </MDTypography>
        </MDBox>
        <div className={`space-y-2 relative transition-all duration-300 ease-in-out ${isCollapsed ? 'max-h-0 overflow-hidden' : 'max-h-[1000px]'}`}>
        {
          (steps ?? []).map((step, index) => (
            <div 
              key={index} 
              className="flex items-start gap-2 text-sm text-gray-600 font-medium relative"
              style={index === steps.length - 1 ? { paddingBottom: '1rem' } : {}}
            >
              {index < steps.length - 1 && (
                <div className="absolute left-[7px] top-[16px] h-full w-[2px] bg-[#aecef7]"></div>
              )}
              <div className="w-[16px] h-[16px] rounded-full flex items-center justify-center relative bg-[#8aaed7]">
                <div className="w-[6px] h-[6px] rounded-full bg-[#1A73E8]" />
              </div>
              <div className="flex-1 min-w-0">
                <span className="block leading-relaxed" style={{ wordBreak: 'break-word' }}>
                  <StreamText
                    key={step}
                    content={step}
                    windowSize={5}
                    delayMultiplier={1.1}
                    sep="word"
                    animation="fadeIn"
                    animationDuration="0.5s"
                    animationTimingFunction="ease-in-out"
                  />
                </span>
              </div>
            </div>
          ))
        }
        </div>
      </MDBox>
    );
};

const PrashnaReport = (props) => {
  const { query_result, plotly_image, plotly_json_path, report_title, query_tags, query_explanation, source, displayDebugInfo } = props;
  const { t } = useTranslation();
  const [plotlyJson, setPlotlyJson] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [csvFilename, setCsvFilename] = useState("data.csv");
  const csvLinkRef = useRef(null);
  const axiosInstance = useCancellableAxios();
  const [controller] = useMaterialUIController();
  const { selectedShop } = controller;

  // Define all hooks before any conditional returns
  const columns = query_result?.columns ?? [];
  const data = query_result?.data ?? [];

  // Prepare tableData object once when query_result changes
  const tableData = useMemo(() => {
    if (columns && columns.length > 0 && data && data.length > 0) {
      return { columns, data };
    }
    return null;
  }, [columns, data]);

  const displayQueryExplanation = useCallback(() => {
    NiceModal.show(QueryExplanation, {
      query_explanation
    });
  }, [query_explanation]);

  // Prepare export data whenever tableData changes
  useEffect(() => {
    if (!tableData || !tableData.columns || !tableData.data || tableData.columns.length === 0) {
      setCsvData(null);
      return;
    }

    const formatColumnName = (columnName) => {
      if (typeof columnName !== 'string') return columnName;
      return columnName
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    // Prepare data for CSV export
    const preparedCsvData = [
      // Header row with formatted column names
      tableData.columns.map(column => formatColumnName(column)),
      // Data rows
      ...tableData.data.map(row => tableData.columns.map(column => row[column]))
    ];

    setCsvData(preparedCsvData);
    setCsvFilename(`${report_title || "data"}.csv`);
  }, [tableData, report_title]);

  useEffect(() => {
    // Only fetch if there's a path to fetch
    if (!plotly_json_path) {
      setPlotlyJson(null);
      return;
    };
    
    const fetchPlotlyJson = async () => {
      let data = { plotly_json_path: plotly_json_path };
      if (selectedShop) {
        data.selectedShop = selectedShop;
      }

      setPlotlyJson(null);
      try {
        const response = await axiosInstance.post(`/api/agent/plotly`, data);

        if (response.data && response.data.data && response.data.layout) {
          setPlotlyJson(response.data);
        } else {
          console.error("Invalid plotly json:", response.data);
        }
      } catch (error) {
        console.error("Error parsing plotly_json_path:", error);
      }
    };

    fetchPlotlyJson();
  }, [plotly_json_path, selectedShop, axiosInstance]);

  // Create views array for the report
  const views = useMemo(() => {
    const result = [];
    
    // Add table view if table data exists
    if (tableData) {
      result.push({
        id: "table",
        viewType: "table",
        title: t("data"),
        dataReference: "$config.data.tableData",
        variables: ["$config.data.tableData"]
      });
    }
    
    // Add chart view if plotly data exists
    if (plotlyJson) {
      result.push({
        id: "chart",
        viewType: "chart",
        title: t("chart"),
        dataReference: "$config.data.plotlyJSON",
        variables: ["$config.data.plotlyJSON"],
      });
    }
    
    return result;
  }, [tableData, plotlyJson, t]);

  // Handle export button click
  const handleExport = useCallback(() => {
    // Trigger the CSV download by clicking the hidden CSVLink element
    if (csvLinkRef.current) {
      csvLinkRef.current.link.click();
    }
  }, [csvLinkRef]);


  // If no views available, don't render anything
  if (views.length === 0) {
    return null;
  }

  // Prepare action buttons for the report footer
  const actions = [];

  // Add "How did we arrive" button if explanation exists
  if (query_explanation) {
    actions.push({
      label: "How did we arrive at this answer?",
      tooltip: "View the reasoning and steps that led to this result",
      icon: "info_outlined",
      onClick: displayQueryExplanation
    });
  }
  
  // Add debug info button if we have SQL or other debug data
  if (displayDebugInfo) {
    actions.push({
      label: "Show debug info",
      tooltip: "View technical details about this query",
      icon: "code",
      onClick: displayDebugInfo
    });
  }
  
  // Add export button if we have data
  if (tableData && csvData) {
    actions.push({
      label: "Export",
      tooltip: "Download the data as CSV",
      icon: "download",
      onClick: handleExport
    });
  }

  const report_json_config = {
    title: report_title,
    tags: query_tags,
    source: source,
    data: {
      plotlyJSON: plotlyJson,
      tableData: tableData,
    },
    views: views,
    actions: actions
  };

  return (
    <MDBox sx={{ 
      width: '100%', 
      maxWidth: '100%',
      position: 'relative'
    }}>
      {/* Hidden CSVLink component for export functionality */}
      {csvData && (
        <div style={{ display: "none" }}>
          <CSVLink
            ref={csvLinkRef}
            data={csvData}
            filename={csvFilename}
            style={{ display: "none" }}
          />
        </div>
      )}
      <Report config={report_json_config} />
    </MDBox>
  );
};

const QueryExplanation = NiceModal.create(({query_explanation}) => {
  const modal = useModal();
  return (
    <Dialog open={modal.visible} onClose={modal.hide} sx={{minWidth:"300px", padding: "16px"}}>
      <MDBox p={2}>
        <MDTypography variant="h6"  fontWeight="bold">
          How did we arrive at this answer?
        </MDTypography>
        <Divider />
        <MDBox mt={2}>
          <MDTypography variant="button"  fontWeight="regular">
            {query_explanation}
          </MDTypography>
        </MDBox>
      </MDBox>
    </Dialog>
  );
});

const DebugInfo = NiceModal.create(({expanded_user_query, final_sql, query_data_processed, reasoning, source}) => {
  const modal = useModal();
  const [copied, setCopied] = useState(false);
  const [expandedStep, setExpandedStep] = useState(null);
  
  const handleCopy = () => {
    if (final_sql) {
      navigator.clipboard.writeText(final_sql)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy SQL: ', err);
        });
    }
  };

  const toggleStep = (index) => {
    if (expandedStep === index) {
      setExpandedStep(null);
    } else {
      setExpandedStep(index);
    }
  };

  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: "10px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            minWidth: "600px",
            maxWidth: "90vw",
            maxHeight: "90vh",
            overflow: "hidden"
          }
        }}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
      <MDBox 
        p={0} 
        sx={{
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          height: "100%"
        }}
      >
        {/* Header */}
        <MDBox 
          p={2} 
          sx={{ 
            borderBottom: "1px solid rgba(0,0,0,0.08)",
            background: "linear-gradient(to right, #f9f9f9, #ffffff)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between"
          }}
        >
          <MDTypography variant="h6" fontWeight="bold">
            Debug Information
          </MDTypography>
          <MDBox 
            onClick={modal.hide}
            sx={{ 
              cursor: "pointer", 
              borderRadius: "50%", 
              width: "30px",
              height: "30px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": { backgroundColor: "rgba(0,0,0,0.04)" }
            }}
          >
            ✕
          </MDBox>
        </MDBox>
        
        {/* Content - scrollable */}
        <MDBox 
          sx={{ 
            p: 3, 
            overflowY: "auto",
            flex: 1
          }}
        >
          {/* MiB processed info with pill styling */}
          <MDBox 
            mb={3}
            sx={{ 
              display: "inline-flex",
              background: "rgba(0,0,0,0.03)",
              borderRadius: "20px",
              padding: "6px 12px"
            }}
          >
            <MDTypography variant="button" fontWeight="medium" color="text">
              <strong>MiB processed:</strong> {query_data_processed}
            </MDTypography>
          </MDBox>

          {/* Reasoning Steps Section */}
          {reasoning && reasoning.length > 0 && (
            <MDBox mb={3}>
              <MDTypography variant="button" fontWeight="bold" color="dark" mb={1} display="block">
                Reasoning Steps:
              </MDTypography>
              <MDBox 
                sx={{
                  backgroundColor: "#f8f9fa",
                  borderRadius: "8px",
                  border: "1px solid rgba(0,0,0,0.08)",
                  overflow: "hidden"
                }}
              >
                {reasoning.map((step, index) => (
                  <MDBox 
                    key={index}
                    sx={{
                      borderBottom: index < reasoning.length - 1 ? "1px solid rgba(0,0,0,0.08)" : "none",
                      position: "relative"
                    }}
                  >
                    {/* Step header with role and toggle button */}
                    <MDBox
                      sx={{
                        padding: "12px 16px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        cursor: "pointer",
                        transition: "background-color 0.2s ease",
                        "&:hover": {
                          backgroundColor: "rgba(0,0,0,0.02)"
                        },
                        backgroundColor: expandedStep === index ? "rgba(26, 115, 232, 0.08)" : "transparent"
                      }}
                      onClick={() => toggleStep(index)}
                    >
                      <MDBox display="flex" alignItems="center" gap={2}>
                        <MDBox
                          sx={{
                            width: "28px",
                            height: "28px",
                            borderRadius: "50%",
                            backgroundColor: "rgba(26, 115, 232, 0.2)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "white",
                            fontWeight: "bold",
                            fontSize: "14px",
                            transition: "all 0.2s ease"
                          }}
                        >
                          {index + 1}
                        </MDBox>
                        <MDTypography 
                          variant="button" 
                          fontWeight="medium"
                          color={expandedStep === index ? "primary" : "text"}
                        >
                          {step.role}
                        </MDTypography>
                      </MDBox>
                      <MDBox
                        sx={{
                          width: "24px",
                          height: "24px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          transform: expandedStep === index ? "rotate(180deg)" : "rotate(0deg)",
                          transition: "transform 0.2s ease"
                        }}
                      >
                        <KeyboardArrowDownIcon fontSize="small" />
                      </MDBox>
                    </MDBox>
                    
                    {/* Expanded content */}
                    {expandedStep === index && (
                      <MDBox
                        sx={{
                          padding: "0 16px 16px 60px",
                          fontSize: "0.875rem",
                          lineHeight: 1.5,
                          animation: "fadeIn 0.3s ease",
                          "@keyframes fadeIn": {
                            "0%": {
                              opacity: 0,
                              transform: "translateY(-10px)"
                            },
                            "100%": {
                              opacity: 1,
                              transform: "translateY(0)"
                            }
                          }
                        }}
                      >
                        {step.content}
                      </MDBox>
                    )}
                  </MDBox>
                ))}
              </MDBox>
            </MDBox>
          )}
          
          {/* Expanded Query Section */}
          <MDBox mb={3}>
            <MDTypography variant="button" fontWeight="bold" color="dark" mb={1} display="block">
              Expanded User Query:
            </MDTypography>
            <MDBox 
              sx={{
                backgroundColor: "#f8f9fa",
                padding: "16px",
                borderRadius: "8px",
                border: "1px solid rgba(0,0,0,0.08)",
                fontSize: "0.875rem",
                fontFamily: "'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace",
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                lineHeight: 1.5
              }}
            >
              {expanded_user_query}
            </MDBox>
          </MDBox>
          
          {/* SQL Section */}
          <MDBox>
            <MDBox 
              display="flex" 
              justifyContent="space-between" 
              alignItems="center" 
              mb={1}
            >
              <MDTypography variant="button" fontWeight="bold" color="dark" display="block">
                Final SQL:
              </MDTypography>
              <Tooltip title={copied ? "Copied!" : "Copy SQL"}>
                <IconButton 
                  onClick={handleCopy} 
                  size="small"
                  sx={{
                    backgroundColor: copied ? "rgba(76, 175, 80, 0.08)" : "rgba(0, 0, 0, 0.04)",
                    borderRadius: "4px",
                    padding: "4px 8px",
                    '&:hover': {
                      backgroundColor: copied ? "rgba(76, 175, 80, 0.12)" : "rgba(0, 0, 0, 0.08)"
                    }
                  }}
                >
                  {copied ? (
                    <CheckIcon fontSize="small" sx={{ color: "#4caf50" }} />
                  ) : (
                    <ContentCopyIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>
            </MDBox>
            <MDBox 
              sx={{
                ...sqlHighlightStyles.container,
                ...sqlHighlightStyles.sxStyles,
                position: "relative",
                '&:hover': {
                  border: "1px solid rgba(25, 118, 210, 0.3)"
                }
              }}
              dangerouslySetInnerHTML={{ __html: highlightSQL(final_sql) }}
            />
          </MDBox>
        </MDBox>
      </MDBox>
    </Dialog>
  );
});


export default function AssistantMessage() {
    const { steps, current_step, plotly_image, plotly_json_path, final_answer, query_result, error, report_title, query_tags, query_explanation, expanded_user_query, final_sql, query_data_processed, reasoning, source, progress_update } = useMessage(m => {
      return m.metadata?.custom ?? {};
    });

    const [progressValue, setProgressValue] = useState(0);
    const [showProgressBar, setShowProgressBar] = useState(false);
    const [progressBarOpacity, setProgressBarOpacity] = useState(1);
    const [nodeProgressMap, setNodeProgressMap] = useState({});
    const [uiSteps, setUiSteps] = useState([]);

    // Append current_step to steps array if it has a value
    useEffect(() => {
      if (current_step && !uiSteps.includes(current_step)) { // deduping to handle retries in nodes
        setUiSteps(prevSteps => [...prevSteps, current_step]);
      }
    }, [current_step]);

    useEffect(() => {
      if (!!steps && steps.length > 0) {
        const deduped = Array.from(new Set(steps)); // deduping to handle retries in nodes
        setUiSteps(deduped);
      }
    }, [steps]);

    useEffect(() => {
      if (!progress_update || !progress_update?.identifier || nodeProgressMap[progress_update.identifier]) {
        return;
      }

      setNodeProgressMap(prevMap => ({
        ...prevMap,
        [progress_update.identifier]: progress_update
      }));

      const totalProgress = progress_update?.type === "overwrite" ? progress_update?.percentage : Math.min(progressValue + progress_update?.percentage, 100);

      setProgressValue(totalProgress);

      if (totalProgress === 0) {
        setShowProgressBar(false);
      }
      else if (totalProgress === 100) {
        const timer = setTimeout(() => {
          setProgressBarOpacity(0);
          setTimeout(() => {
            setShowProgressBar(false);
          }, 500); // Wait for fade out animation to complete
        }, 4000); // Wait 4 seconds before starting fade out
        return () => clearTimeout(timer);
      } else if (!!totalProgress && 0 < totalProgress && totalProgress < 100) {
        setShowProgressBar(true);
        setProgressBarOpacity(1);
      }
    }, [progress_update]);

    if (error) {
      return (
        <MessagePrimitive.Root className="relative mb-8 flex w-full max-w-4xl gap-4 font-size-12">
          <AssistantAvatar />
          <div className="flex-grow space-y-4">
            <MDTypography variant="button" color="error" fontWeight="regular" sx={{fontSize: "15px !important", lineHeight: "30px !important"}}>
              Something went wrong. Please try again.
            </MDTypography>
          </div>
        </MessagePrimitive.Root>
      );
    }

    const displayDebugInfo = () => {
      NiceModal.show(DebugInfo, {
        expanded_user_query,
        final_sql,
        query_data_processed,
        reasoning,
        source
      });
    }

    return (
      <MessagePrimitive.Root className="relative mb-8 flex w-full max-w-4xl gap-4 font-size-12 p-4 rounded-lg">
        <AssistantAvatar />
        <MDBox sx={{ width: '100%', maxWidth: 'calc(100% - 40px)' }}>
          <MDBox className="space-y-2">
            {(uiSteps ?? []).length > 0 && <Steps steps={uiSteps} final_answer={final_answer} progress_value={progressValue} />}
          </MDBox>

          {showProgressBar && (
            <MDBox
              sx={{
                pb: 2,
                pt: 2,
                position: 'relative',
                opacity: progressBarOpacity,
                transition: 'opacity 0.5s ease-out'
              }}
            >
              <MDTypography variant="button" fontWeight="medium" color="text" mb={1} >
                Progress: {progressValue}%
              </MDTypography>
              <MDProgress
                variant="gradient"
                value={progressValue}
                color="info"
              />
            </MDBox>
          )}

          {(!!query_result || !!plotly_image || !!plotly_json_path) &&
            <PrashnaReport
              source={source}
              query_result={query_result}
              plotly_image={plotly_image}
              report_title={report_title}
              plotly_json_path={plotly_json_path}
              query_tags={query_tags}
              query_explanation={query_explanation}
              displayDebugInfo={displayDebugInfo}
            />
          }

          {final_sql && <MDButton variant="outlined" color="secondary" size="small"  onClick={displayDebugInfo}>Show Debug Info</MDButton>}

          {final_answer && <MDBox mt={2}>
            <MDTypography variant="body2" fontWeight="regular">
              <MessagePrimitive.Content components={{Text: FinalAnswerText}}/>
            </MDTypography>
          </MDBox>}
        </MDBox>
      </MessagePrimitive.Root>
    );
  };