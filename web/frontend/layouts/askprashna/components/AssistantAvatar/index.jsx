
import AssistantIcon from '@mui/icons-material/Assistant';
import MDAvatar from "@/components/MDAvatar";

const AssistantAvatar = () => {
    const avatarStyles = {
      border: ({ borders: { borderWidth }, palette: { dark } }) =>
        `${borderWidth[2]} solid ${dark.main}`,
      position: "relative",
      "&:hover, &:focus": {
        zIndex: "10",
      },
    };
  
    return (
      <div className="flex-shrink-0">
        <MDAvatar sx={avatarStyles} size="sm">
            <svg width={0} height={0}>
            <linearGradient id="avatarGradient" x1={0} y1={0} x2={1} y2={1}>
                <stop offset={0} stopColor="#DE4DAA" />
                <stop offset={1} stopColor="#F6D327" />
            </linearGradient>
            </svg>
          <AssistantIcon fontSize="small" color="dark" sx={{
            // fill: `url(#avatarGradient)`
            }} />
        </MDAvatar>
      </div>
    );
}

export default AssistantAvatar;