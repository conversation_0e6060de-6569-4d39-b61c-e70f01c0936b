import { useRef, useEffect, useState } from "react";
import {
  Thread,
  useThread,
  ThreadWel<PERSON>,
  ThreadList,
  Composer,
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ThreadListPrimitive,
  ThreadListItemPrimitive,
  WebSpeechSynthesisAdapter,
  ThreadListItem,
  useComposerRuntime,
} from "@assistant-ui/react";
import "@assistant-ui/react/styles/index.css";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import { useLangGraphRuntime } from "@/layouts/askprashna/langgraph-runtime/index.jsx";
import UserMessage from "@/layouts/askprashna/components/UserMessage/index.jsx";
import AssistantMessage from "@/layouts/askprashna/components/AssistantMessage/index.jsx";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";
import {
  createThread,
  getThreadState,
  sendMessage,
  getThreads,
  cancelRun,
} from "@/layouts/askprashna/langgraph-api/index.jsx";
import { useTranslation } from "react-i18next";
import Skeleton from "@mui/material/Skeleton";
import Menu from "@mui/material/Menu";
import Grid from "@mui/material/Grid";
import AddIcon from "@mui/icons-material/Add";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController } from "@/context/index.jsx";
import Sidenav from "@/layouts/askprashna/components/Sidenav/index.jsx";
import Discover from "@/layouts/askprashna/components/Discover/index.jsx";
import ChatHistory from "@/layouts/askprashna/components/ChatHistory/index.jsx";
import MDTooltip from "@/components/MDTooltip";
import { Global, css } from '@emotion/react';
import { getPrompts } from "@/layouts/askprashna/components/constants/prompts.jsx";
import PaginationControls from "@/layouts/askprashna/components/common/PaginationControls.jsx";

// Global styles component
const GlobalStyles = () => (
  <Global
    styles={css`
      @keyframes pulse {
        0% {
          opacity: 0.3;
          transform: scale(0.8);
        }
        50% {
          opacity: 1;
          transform: scale(1.2);
        }
        100% {
          opacity: 0.3;
          transform: scale(0.8);
        }
      }
    `}
  />
);

const MySuggestions = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const suggestionsPerPage = 4;

  // Use the shared welcome suggestions
  const allSuggestions = getPrompts();

  const totalPages = Math.ceil(allSuggestions.length / suggestionsPerPage);
  const currentSuggestions = allSuggestions.slice(
    currentPage * suggestionsPerPage,
    (currentPage + 1) * suggestionsPerPage
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <Box sx={{ width: "100%", px: 2 }}>
      <Grid container spacing={2} sx={{ mb: 2 }}>
        {currentSuggestions.map((suggestion, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <ThreadPrimitive.Suggestion
              prompt={suggestion.text}
              method="replace"
              autoSend
              asChild
            >
              <MDTooltip title={suggestion.text} placement="top">
                <MDButton
                  variant="outlined"
                  color="secondary"
                  fullWidth
                  sx={{
                    height: "100px",
                    minHeight: "100px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    textAlign: "left",
                    p: 2,
                    pb: 3.5,
                    position: "relative"
                  }}
                >
                  <MDTypography 
                    variant="button" 
                    fontWeight="regular" 
                    sx={{ 
                      width: "100%", 
                      textAlign: "left",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {suggestion.text}
                  </MDTypography>
                  <Chip
                    label={suggestion.category.label}
                    size="small"
                    sx={{
                      position: "absolute",
                      bottom: "8px",
                      left: "8px",
                      backgroundColor: suggestion.category.backgroundColor,
                      color: suggestion.category.textColor,
                      fontSize: "0.65rem",
                      height: "20px",
                      fontWeight: "bold"
                    }}
                  />
                </MDButton>
              </MDTooltip>
            </ThreadPrimitive.Suggestion>
          </Grid>
        ))}
      </Grid>
      
      {totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          onPrevPage={handlePrevPage}
          onNextPage={handleNextPage}
          containerSx={{ mt: 1, mb: 1 }}
          size="small"
        />
      )}
    </Box>
  );
};

const MyThreadWelcome = () => {
  const { t } = useTranslation();
  return (
    <ThreadWelcome.Root>
      <ThreadWelcome.Center>
        <AssistantAvatar />
        <ThreadWelcome.Message message={t("prashna.welcome-message")} />
      </ThreadWelcome.Center>
      <MySuggestions />
    </ThreadWelcome.Root>
  );
};

const MyComposerAction = () => {
  const composerRuntime = useComposerRuntime();

  const handleCancel = () => {
    composerRuntime.cancel(); // Core cancellation method
  };
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <Composer.Send />
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <Composer.Cancel onClick={handleCancel} />
      </ThreadPrimitive.If>
    </>
  );
};

const MyComposer = () => {
  const { t } = useTranslation();
  return (
    <Composer.Root>
      <Composer.Input
        placeholder={t("prashna.composer-placeholder")}
        autoFocus
      />
      <MyComposerAction />
    </Composer.Root>
  );
};

const ThreadFollowupSuggestions = () => {
  const suggestions = useThread((t) => t.suggestions);
  const [currentPage, setCurrentPage] = useState(0);
  const followupSuggestionsPerPage = 4;

  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  const totalPages = Math.ceil(suggestions.length / followupSuggestionsPerPage);
  const currentSuggestions = suggestions.slice(
    currentPage * followupSuggestionsPerPage,
    (currentPage + 1) * followupSuggestionsPerPage
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <ThreadPrimitive.If empty={false} running={false}>
      <MDBox className="relative mb-8 flex w-full max-w-4xl gap-4 font-size-12 p-4 mx-auto">
        <MDBox className="flex-shrink-0" sx={{ width: 32, height: 32 }} />
        
        <MDBox width="100%">
          <MDTypography variant="subtitle2" fontWeight="regular" color="secondary" mb={1.5}>
            Related questions
          </MDTypography>
          
          <MDBox 
            sx={{ 
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              borderRadius: '8px',
              overflow: 'hidden',
              border: '1px solid rgba(0,0,0,0.1)',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
            }}
          >
            {currentSuggestions.map((suggestion, idx) => (
              <ThreadPrimitive.Suggestion
                key={idx}
                prompt={suggestion.prompt}
                method="replace"
                autoSend
                asChild
              >
                <MDBox
                  sx={{
                    width: '100%',
                    p: 1.8,
                    cursor: 'pointer',
                    backgroundColor: 'white',
                    transition: 'background-color 0.2s',
                    borderBottom: idx < currentSuggestions.length - 1 ? '1px solid rgba(0,0,0,0.06)' : 'none',
                    '&:hover': {
                      backgroundColor: 'rgba(0,0,0,0.02)'
                    },
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <MDTypography 
                    variant="body2" 
                    fontWeight="regular" 
                    color="dark"
                    sx={{ 
                      width: "100%",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      fontSize: "0.85rem",
                      lineHeight: "1.4",
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {suggestion.prompt}
                  </MDTypography>
                  <MDBox 
                    sx={{ 
                      ml: 1.5, 
                      color: 'rgba(0, 0, 0, 0.35)',
                      fontSize: '0.9rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <span>→</span>
                  </MDBox>
                </MDBox>
              </ThreadPrimitive.Suggestion>
            ))}
          </MDBox>

          {totalPages > 1 && (
            <PaginationControls
              currentPage={currentPage}
              totalPages={totalPages}
              onPrevPage={handlePrevPage}
              onNextPage={handleNextPage}
              containerSx={{ mt: 2 }}
              size="small"
              typographySx={{ fontSize: '0.7rem' }}
              buttonSx={{ width: "24px", height: "24px" }}
            />
          )}
        </MDBox>
      </MDBox>
    </ThreadPrimitive.If>
  );
};

const MyThread = (config) => {
  const { currentThreadLoading } = config;

  if (currentThreadLoading) {
    return (
      <Thread.Root config={config}>
        <Thread.Viewport height="80vh" autoScroll={false}>
          <Grid container spacing={3} sx={{ px: 2, py: 3 }}>
            {/* Welcome message skeleton */}
            <Grid item xs={12} sx={{ mb: 2 }}>
              <MDBox 
                sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  mb: 3,
                  mt: 1
                }}
              >
                <Skeleton variant="circular" width={50} height={50} sx={{ mb: 2 }} />
                <Skeleton variant="text" width="40%" height={35} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="60%" height={24} />
              </MDBox>
            </Grid>
            
            {/* User message skeleton */}
            <Grid item xs={12} container justifyContent="flex-end" sx={{ mb: 1 }}>
              <Grid item xs={8} sm={7} md={6}>
                <MDBox
                  sx={{
                    p: 1.5,
                    backgroundColor: '#f0f0f0',
                    borderRadius: '12px 2px 12px 12px',
                    position: 'relative',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}
                >
                  <Skeleton variant="text" width="100%" height={24} />
                  <Skeleton variant="text" width="80%" height={24} />
                </MDBox>
              </Grid>
            </Grid>
            
            {/* Assistant message skeleton with thinking animation */}
            <Grid item xs={12} container sx={{ mb: 2 }}>
              <Grid item xs={1} sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'center' }}>
                <Skeleton variant="circular" width={36} height={36} />
              </Grid>
              <Grid item xs={8} sm={7} md={6}>
                <MDBox
                  sx={{
                    p: 1.5,
                    backgroundColor: 'white',
                    borderRadius: '2px 12px 12px 12px',
                    border: '1px solid rgba(0, 0, 0, 0.08)',
                    position: 'relative',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }}
                >
                  <MDBox sx={{ display: 'flex', gap: 0.7, mb: 1.5, pl: 1 }}>
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0s'
                      }} 
                    />
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0.2s'
                      }} 
                    />
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0.4s'
                      }} 
                    />
                  </MDBox>
                  <Skeleton variant="text" width="90%" height={24} />
                  <Skeleton variant="text" width="100%" height={24} />
                  <Skeleton variant="text" width="75%" height={24} />
                </MDBox>
              </Grid>
            </Grid>
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Skeleton variant="rounded" width="100%" height={50} sx={{ borderRadius: '8px' }} />
            </Grid>
          </Grid>
        </Thread.Viewport>
      </Thread.Root>
    );
  }

  return (
    <Thread.Root config={config} height="80vh" sx={{ maxWidth: "100%", overflow: "hidden" }}>
      <Thread.Viewport height="80vh" autoScroll={false}>
        <MyThreadWelcome />
        <Thread.Messages components={{ 
          AssistantMessage: AssistantMessage,
          UserMessage: UserMessage
        }} />
        <ThreadFollowupSuggestions />
        <Thread.ViewportFooter>
          <Thread.ScrollToBottom />
          <MyComposer />
        </Thread.ViewportFooter>
      </Thread.Viewport>
    </Thread.Root>
  );
};

export const CompactThreadListItem = ({ onSelect }) => {
  return (
    <ThreadListItemPrimitive.Root>
      <ThreadListItemPrimitive.Trigger
        className="w-full"
        asChild
        onClick={onSelect}
      >
        <MDButton
          variant="text"
          color="secondary"
          fullWidth
          sx={{
            justifyContent: "flex-start",
            textAlign: "left",
            minHeight: "40px",
            padding: "4px 8px",
            margin: "0",
            borderRadius: "0",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.05)",
            }
          }}
        >
          <MDTypography
            variant="button"
            fontWeight="regular"
            sx={{
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 2,
              overflow: "hidden",
              fontSize: "0.875rem",
              lineHeight: "1.25"
            }}
          >
            <ThreadListItemPrimitive.Title />
          </MDTypography>
        </MDButton>
      </ThreadListItemPrimitive.Trigger>
    </ThreadListItemPrimitive.Root>
  );
};

// Mobile Thread List
const MobileThreadList = ({ onCreateNewThread }) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);
  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleNewChatClick = () => {
    onCreateNewThread();
  };
  
  return (
    <div className="md:hidden">
      <ThreadList.Root className="mb-4">
        <MDButton 
          variant="contained" 
          color="secondary" 
          fullWidth
          onClick={handleNewChatClick}
        >
          <AddIcon fontSize="small" /> &nbsp;
          <MDTypography variant="button" fontWeight="regular" color="dark">
            {t("prashna.new-question")}
          </MDTypography>
        </MDButton>
      </ThreadList.Root>
      <MDButton
        variant="outlined"
        color="secondary"
        fullWidth
        onClick={handleMenuOpen}
      >
        {t("prashna.threads.all-conversations")}
      </MDButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          style: {
            maxHeight: 300,
            width: "100%",
            marginTop: "8px",
            display: "flex",
            flexDirection: "column",
          },
        }}
      >
        <ThreadList.Items
          components={{ ThreadListItem: CompactThreadListItem }}
        />
      </Menu>
    </div>
  );
};

export function MyAssistant() {
  const {t} = useTranslation();
  const [rawThreads, setRawThreads] = useState([]);
  const [uiThreads, setUiThreads] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [threadsLoading, setThreadsLoading] = useState(false);
  const [currentThreadLoading, setCurrentThreadLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState("chat");
  const [collapsed, setCollapsed] = useState(() => {
    // Get the user's previous preference from localStorage, if none exists, default to collapsed (true)
    const savedPreference = localStorage.getItem("sidebarCollapsed");
    return savedPreference === null ? true : savedPreference === "true";
  });

  const setSidebarCollapsed = (collapsed) => {
    setCollapsed(collapsed);
    localStorage.setItem("sidebarCollapsed", String(collapsed));
  }

  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop} = controller;
  const axiosInstance = useCancellableAxios();

  // Compute filtered threads based on search term
  const filteredThreads = searchTerm.trim() === "" 
    ? uiThreads 
    : uiThreads.filter(thread => {
        const title = thread.title.toLowerCase();
        return title.includes(searchTerm.toLowerCase());
      });

  const processThreads = (rawThreads) => {
    return rawThreads
      .filter((thread) => {
        return (
          thread.values?.thread_name &&
          thread.values?.conversation_history?.run_summaries &&
          thread.values?.conversation_history?.run_summaries.length > 0
        );
      })
      .map((thread) => {
        const title = thread.values?.thread_name ?? thread.values?.user_query ?? "New Chat";
        const threadId = thread.thread_id;

        return {
          status: "regular",
          threadId: threadId,
          title: title,
        };
      });
  }

  // Handle search term change
  const handleSearchTermChange = (newSearchTerm) => {
    setSearchTerm(newSearchTerm);
  };

  // Process raw threads into UI threads
  useEffect(() => {
    const processed = processThreads(rawThreads);
    setUiThreads(processed);
  }, [rawThreads]);

  const threadIdRef = useRef();

  const createNewThread = async () => {
    // Check if we're already in the process of creating a thread
    if (currentThreadLoading) {
      return;
    }
    
    // If runtime is available with switchToNewThread capability, use it
    if (runtime?.switchToNewThread) {
      try {
        await runtime.switchToNewThread();
        return;
      } catch (error) {
        console.error("Error using runtime to create new thread:", error);
        // Fall through to manual method if runtime method fails
      }
    }
    
    // Fallback to manual creation if runtime method isn't available or failed
    try {
      setCurrentThreadLoading(true);
      const { thread_id } = await createThread(axiosInstance, selectedShop);
      threadIdRef.current = thread_id;
      // // Only update UI threads if we got a valid thread_id
      // if (thread_id) {
      //   setUiThreads(prevThreads => [{
      //     status: "regular",
      //     threadId: thread_id,
      //     title: "New Chat",
      //   }, ...prevThreads]);
      // }
    } catch (error) {
      console.error("Failed to create new thread:", error);
    } finally {
      setCurrentThreadLoading(false);
    }
  }

  const fetchThreads = async (with_loading = true) => {
    if (with_loading) {
      setThreadsLoading(true);
    }
    const threads = await getThreads(axiosInstance, selectedShop);
    setRawThreads(threads);
    if (with_loading) {
      setThreadsLoading(false);
    }
  };

  const runtime = useLangGraphRuntime({
    threadId: threadIdRef.current,
    threads: selectedOption === "history" ? filteredThreads : uiThreads,
    adapters: {
      speech: new WebSpeechSynthesisAdapter(),
    },
    unstable_allowCancellation: true,
    fetchThreads: fetchThreads,
    stream: async (messages, options) => {
      const { abortSignal, runId } = options || {};

      if (abortSignal) {
        abortSignal.addEventListener("abort", async () => {
          await cancelRun(threadIdRef.current, runId(), axiosInstance);
        });
      }

      if (!threadIdRef.current) {
        await createNewThread();
      }
      const threadId = threadIdRef.current;

      if (messages.length === 1 && messages[0].type === "human") {
        setUiThreads((uiThreads) =>
          uiThreads.map((thread) =>
            thread.threadId === threadId
              ? { ...thread, title: messages[0].content }
              : thread
          )
        );
      }

      return sendMessage(
        {
          threadId,
          messages,
          abortSignal,
        },
        axiosInstance,
        selectedShop
      );
    },

    onSwitchToNewThread: async () => {
      // Add a flag to prevent infinite recursion
      const isAlreadySwitching = threadIdRef.current === null;
      
      // Only create a new thread if we're not already in the process of creating one
      if (!isAlreadySwitching) {
        // Temporarily clear the threadId to prevent nested calls
        const oldThreadId = threadIdRef.current;
        threadIdRef.current = null;
        
        try {
          // Use the manual thread creation logic to ensure thread is created
          setCurrentThreadLoading(true);
          const { thread_id } = await createThread(axiosInstance, selectedShop);
          
          if (!thread_id) {
            // If thread creation failed, restore old ID and throw error
            threadIdRef.current = oldThreadId;
            throw new Error("Failed to create thread - no thread ID returned");
          }
          
          threadIdRef.current = thread_id;
          
          // // Update UI threads with the new thread
          // setUiThreads(prevThreads => [{
          //   status: "regular",
          //   threadId: thread_id,
          //   title: "New Chat",
          // }, ...prevThreads]);
          
        } catch (error) {
          // Restore the previous threadId if creation fails
          threadIdRef.current = oldThreadId;
          throw error;
        } finally {
          setCurrentThreadLoading(false);
        }
      }
    },
    onSwitchToThread: async (threadId) => {
      setCurrentThreadLoading(true);
      const state = await getThreadState(threadId, axiosInstance, selectedShop);
      threadIdRef.current = threadId;
      setCurrentThreadLoading(false);

      // Access the conversation history which contains all past conversations
      const conversationHistory = state.values?.conversation_history?.run_summaries || [];
      
      // Create an array to hold all messages
      const allMessages = [];
      
      // Map through the conversation history to create alternating user and assistant messages
      conversationHistory.forEach((convo, index) => {
        // Add user message
        allMessages.push({
          type: 'human',
          id: `${threadId}-user-query-${index}`,
          content: convo.user_query || "",
        });
        
        // Add assistant message
        allMessages.push({
          type: 'ai',
          id: `${threadId}-assistant-response-${index}`,
          content: convo.final_answer || "",
          metadata: {
            custom: {
              current_step: "",
              reasoning: convo.reasoning || [],
              source: convo.source || "",
              final_answer: convo.final_answer || "",
              query_result: convo.query_result || {},
              plotly_image: convo.plotly_image || "",
              plotly_json_path: convo.plotly_json_path || "",
              expanded_user_query: convo.expanded_user_query || "",
              final_sql: convo.final_sql || "",
              query_data_processed: convo.query_data_processed || "",
              report_title: convo.report_title || "",
              query_tags: convo.query_tags || [],
              query_explanation: convo.query_explanation || "",
              progress_update: convo.progress_update || null,
              steps: convo.steps || []
            }
          }
        });
      });
      
      return { messages: allMessages };
    },
  });

  useEffect(() => {
    fetchThreads();
    if (!threadIdRef.current) {
      createNewThread();
    }
  }, [selectedShop]);

  const handleResetMainView = () => {
    setSelectedOption("chat");
  };

  const options = [
    { id: "chat", label: "New Chat", icon: "chat_outlined" },
    { id: "discover", label: "Discover", icon: "explore_outlined" },
    { id: "history", label: "Chat History", icon: "history_outlined" }
  ];

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <GlobalStyles />
      <MDBox height="83vh" sx={{ overflow: "hidden" }}>
        <article className="h-[83vh] overflow-hidden text-sm">
            <div className="flex h-full flex-col md:flex-row md:gap-6">
            <MDBox className="flex flex-col flex-shrink-0" sx={{ gap: 2 }}>
            <div className={`hidden md:flex px-3 my-2 bg-transparent flex-col transition-all duration-300 z-10 ${
        collapsed ? "w-16" : "w-64"
      }`}
      style={{ 
        minWidth: collapsed ? "64px" : "256px", 
        width: collapsed ? "64px" : "256px", 
        maxWidth: collapsed ? "64px" : "256px", 
        height: "100%" 
      }}
    >
            <MDBox mb={3}>
            <Sidenav 
                options={options}
                setSelectedOption={setSelectedOption}
                selectedOption={selectedOption}
                threads={uiThreads}
                threadsLoading={threadsLoading}
                collapsed={collapsed}
                setCollapsed={setSidebarCollapsed}
              />
            </MDBox>
            </div>
            <MobileThreadList onCreateNewThread={createNewThread}/>
            </MDBox>
            <div 
              className="flex-grow my-2 pl-4 pr-4 w-full md:w-auto bg-white overflow-hidden"
              style={{
                minWidth: collapsed ? "calc(100% - 100px)" : "calc(100% - 300px)",
                borderRadius: "0.50rem",
                boxShadow: "0 2px 5px 0 rgba(0, 0, 0, 0.1)",
                '@media (min-width: 900px)': {
                  maxWidth: collapsed ? "calc(100% - 100px)" : "calc(100% - 300px)"
                }
              }}
            >
              {(selectedOption === "chat" || selectedOption === "old_chat") && <MyThread currentThreadLoading={currentThreadLoading} />}
              {selectedOption === "discover" && <Discover onClose={handleResetMainView} />}
              {selectedOption === "history" && (
                <ChatHistory 
                  threads={filteredThreads} 
                  threadsLoading={threadsLoading} 
                  onClose={handleResetMainView} 
                  searchTerm={searchTerm}
                  onSearchChange={handleSearchTermChange}
                />
              )}
            </div>
            </div>
        </article>
      </MDBox>
    </AssistantRuntimeProvider>
  );
}

export default MyAssistant;