// Category definitions with their respective colors
export const SUGGESTION_CATEGORIES = {
  GROWTH_FINANCE: {
    label: "GROWTH & FINANCE",
    backgroundColor: "#E0F2E9",
    textColor: "#1B5E20",
    icon: null // Can be set in components as needed (e.g., logoShopify in Discover component)
  },
  CUSTOMERS_RETENTION: {
    label: "CUSTOMERS & RETENTION",
    backgroundColor: "#E3F2FD",
    textColor: "#0D47A1",
    icon: null // Can be set in components as needed
  },
  MARKETING_PERFORMANCE: {
    label: "MARKETING PERFORMANCE",
    backgroundColor: "#FFF3E0",
    textColor: "#E65100",
    icon: null // Can be set in components as needed
  },
  PLATFORM_PERFORMANCE: {
    label: "PLATFORM PERFORMANCE",
    backgroundColor: "#F3E5F5",
    textColor: "#4A148C",
    icon: null // Can be set in components as needed
  }
};

// Define all prompts with their categories that can be used across components
export const getPrompts = () => [
  { 
    text: "What was my sales this quarter, how does it compare with previous quarter & year?", 
    category: SUGGESTION_CATEGORIES.GROWTH_FINANCE 
  },
  { 
    text: "What are the top products where I've acquired most number of new customers this year?", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "What are the top 5 campaigns by adspend on Google over last 3 months?", 
    category: SUGGESTION_CATEGORIES.MARKETING_PERFORMANCE 
  },
  { 
    text: "How much time in days do customers take on average between their first and second order?", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Provide my AdSpend and Revenue for Google Ads for the last year month-over-month.", 
    category: SUGGESTION_CATEGORIES.MARKETING_PERFORMANCE 
  },
  { 
    text: "What was my MoM growth in revenue over the last 12 months?", 
    category: SUGGESTION_CATEGORIES.GROWTH_FINANCE 
  },
  { 
    text: "Was there Y-o-Y growth year to date?", 
    category: SUGGESTION_CATEGORIES.GROWTH_FINANCE 
  },
  { 
    text: "Compare my revenue for new vs. returning customers over the last 12 months.", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Give me the top channels & their ROAS over the last 7 days.", 
    category: SUGGESTION_CATEGORIES.MARKETING_PERFORMANCE 
  },
  { 
    text: "What are the top 2 frequently bought together products?", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "What's the website conversion rate product category wise?", 
    category: SUGGESTION_CATEGORIES.PLATFORM_PERFORMANCE 
  },
  { 
    text: "What were my top-performing video creatives on Meta last week?", 
    category: SUGGESTION_CATEGORIES.MARKETING_PERFORMANCE 
  },
  { 
    text: "Are there any high-drop-off points we need to fix?", 
    category: SUGGESTION_CATEGORIES.PLATFORM_PERFORMANCE 
  },
  { 
    text: "Show me a cohort table for the last full 12 months and Y-o-Y revenue and customer count.", 
    category: SUGGESTION_CATEGORIES.GROWTH_FINANCE 
  },
  { 
    text: "Show me key metrics for top pages, website analytics per device, and web stats by country.", 
    category: SUGGESTION_CATEGORIES.PLATFORM_PERFORMANCE 
  },
  { 
    text: "What is the average session duration for visitors coming from Google, Meta, and TikTok Ads over the last 30 days?", 
    category: SUGGESTION_CATEGORIES.PLATFORM_PERFORMANCE 
  },
  { 
    text: "Give me my Customer Lifetime Value (LTV) for 2024, compare with last year.", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Which product is bought most in first, second, 3rd and 4th order?", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Show me the cost per click for Google and Facebook Ads outfits over the last 30 days.", 
    category: SUGGESTION_CATEGORIES.MARKETING_PERFORMANCE 
  },
  { 
    text: "What % of my orders have a discount code? I want to know the average % over the last 90 days broken down by day of the week.", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Rank countries by order revenue and new customers for the last quarter.", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  },
  { 
    text: "Which product categories to expand or shorten based on performance?", 
    category: SUGGESTION_CATEGORIES.CUSTOMERS_RETENTION 
  }
];