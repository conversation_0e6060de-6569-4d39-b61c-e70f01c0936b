// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import Divider from "@mui/material/Divider";
import Skeleton from "@mui/material/Skeleton";
import {ThreadList, ThreadListItemPrimitive, ThreadListPrimitive} from "@assistant-ui/react";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

// Material Dashboard 2 PRO React context
import { useMaterialUIController } from "@/context";

import {useTranslation} from "react-i18next";

function Sidenav({
    collapsed,
    setCollapsed,
  options,
  selectedOption,
  setSelectedOption,
  threads = [],
  threadsLoading = false
}) {
  const [controller] = useMaterialUIController();
  const { miniSidenav, loginConfig, darkMode, shopConfig } = controller;

  const {t} = useTranslation();

  let showSubscriptionBanner = shopConfig.subscription_enabled
  && shopConfig.planDetails?.planType == "free";

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const SidenavThreadListItem = ({ onSelect }) => {
    return (
      <ThreadListItemPrimitive.Root>
      <ThreadListItemPrimitive.Trigger
        className="w-full"
        asChild
        onClick={onSelect}
      >
      <MDBox
        component="div"
        onClick={() => setSelectedOption("old_chat")}
        sx={{
          cursor: "pointer",
          p: 1.5,
          borderRadius: "8px",
          backgroundColor: "rgba(0, 0, 0, 0.02)",
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.05)",
            transform: "translateY(-2px)",
          },
          mb: 1.5,
          width: "100%",
          transition: "all 0.2s ease-in-out",
          display: "flex",
          alignItems: "center",
          boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
          overflow: "hidden",
        }}
      >
        <Icon color="secondary" sx={{ mr: 1.5, fontSize: "1.1rem", flexShrink: 0 }}>chat_bubble_outline</Icon>
        <MDTypography
          variant="button"
          color="dark"
          fontWeight="regular"
          sx={{
            display: "-webkit-box",
            WebkitBoxOrient: "vertical",
            WebkitLineClamp: 2,
            overflow: "hidden",
            fontSize: "0.875rem",
            lineHeight: "1.25",
            flex: 1,
            textOverflow: "ellipsis",
            whiteSpace: "normal"
          }}
        >
          <ThreadListItemPrimitive.Title />
        </MDTypography>
      </MDBox>
      </ThreadListItemPrimitive.Trigger>
      </ThreadListItemPrimitive.Root>
    );
  };

  const renderSidenavItems = options.map(({ icon, label, id }, key) => {
    const itemKey = `item-${key}`;
    
    const navItem = (
      <MDTypography
        component="a"
        onClick={() => setSelectedOption(id)}
        variant="button"
        fontWeight="regular"
        textTransform="capitalize"
        sx={({
          borders: { borderRadius },
          functions: { pxToRem },
          palette: { light },
          transitions,
        }) => ({
          display: "flex",
          cursor: "pointer",
          alignItems: "center",
          borderRadius: borderRadius.md,
          backgroundColor: selectedOption === id ? light.main : "transparent",
          padding: collapsed ? `${pxToRem(10)} 0` : `${pxToRem(10)} ${pxToRem(16)}`,
          transition: transitions.create("background-color", {
            easing: transitions.easing.easeInOut,
            duration: transitions.duration.shorter,
          }),
          justifyContent: collapsed ? "center" : "flex-start",
          width: collapsed ? "40px" : "100%",
          minWidth: collapsed ? "40px" : "100%",
          maxWidth: collapsed ? "40px" : "100%",
          margin: 0,
          "&:hover": {
            backgroundColor: light.main,
          },
        })}
      >
        <MDBox mr={collapsed ? 0 : 1.5} lineHeight={1} color={darkMode ? "white" : "dark"} display="flex" justifyContent="center" alignItems="center">
          <Icon fontSize="small">{icon}</Icon>
        </MDBox>
        {!collapsed && t(label)}
      </MDTypography>
    );

    let displayItem = (
      <MDBox key={itemKey} component="li" pt={key === 0 ? 0 : 1} sx={{
        display: "flex",
        justifyContent: collapsed ? "center" : "flex-start",
        alignItems: "center",
        width: collapsed ? "100%" : "100%",
      }}>
        {collapsed ? (
          <MDTooltip title={t(label)} placement="right" arrow>
            {navItem}
          </MDTooltip>
        ) : (
          navItem
        )}
      </MDBox>
    );

    if (id === "chat") {
      displayItem = (
        <ThreadListPrimitive.New asChild>
          {displayItem}
        </ThreadListPrimitive.New>
      )
    }

    return displayItem;
  });

  return (
    <Card
      sx={{
        borderRadius: ({ borders: { borderRadius } }) => borderRadius.lg,
        position: "sticky",
        top: showSubscriptionBanner ? "4rem" : "1%",
        transition: "width 0.3s ease",
        width: collapsed ? "64px" : "240px",
        maxWidth: collapsed ? "64px" : "240px",
        height: "fit-content",
        maxHeight: "calc(100vh - 100px)",
        display: "flex",
        flexDirection: "column",
        marginRight: "12px",
        overflow: "hidden",
      }}
    >
      <MDBox
        display="flex"
        alignItems="center"
        justifyContent={collapsed ? "center" : "space-between"}
        p={2}
        sx={{
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        {!collapsed && (
          <MDTypography variant="h6" fontWeight="medium">
            Prashna AI
          </MDTypography>
        )}
        {collapsed ? (
            <IconButton onClick={toggleCollapse} sx={{ mx: collapsed ? 'auto' : 0 }}>
              <MenuIcon />
            </IconButton>
        ) : (
          <IconButton onClick={toggleCollapse} sx={{ mx: collapsed ? 'auto' : 0 }}>
            <MenuOpenIcon />
          </IconButton>
        )}
      </MDBox>
      <MDBox
        component="ul"
        display="flex"
        flexDirection="column"
        p={2}
        m={0}
        sx={{
          listStyle: "none",
          alignItems: collapsed ? "center" : "flex-start",
          width: "100%",
          gap: 1.5,
        }}
      >
        {renderSidenavItems}
      </MDBox>

      {/* Chat History Section (visible only in expanded mode) */}
      {!collapsed && threads.length > 0 && (
        <MDBox px={2} sx={{ flex: 1, display: "flex", flexDirection: "column", overflow: "hidden" }}>
          <Divider sx={{ my: 2 }} />
          <MDBox
            sx={{
              flex: 1,
              overflowY: "auto",
              maxHeight: "250px",
              pb: 2,
              px: 0.5,
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,0.2)',
                borderRadius: '3px',
              }
            }}
          >
            <MDBox sx={{display: "flex", alignItems: "center", justifyContent: "space-between"}}>
              <MDTypography variant="button" color="secondary" fontWeight="medium" mb={1} sx={{borderBottom: "1px solid rgba(0, 0, 0, 0.12)"}}>
                {"Recent chats"}
              </MDTypography>
            </MDBox>
            <MDBox mt={1} />
            {threadsLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <MDBox
                  key={index}
                  sx={{
                    p: 1.5,
                    borderRadius: "8px",
                    backgroundColor: "rgba(0, 0, 0, 0.02)",
                    mb: 1.5,
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                    overflow: "hidden",
                  }}
                >
                  <Skeleton 
                    variant="circular" 
                    width={20} 
                    height={20} 
                    sx={{ mr: 1.5, flexShrink: 0 }} 
                  />
                  <Skeleton 
                    variant="text" 
                    width="85%" 
                    height={20} 
                  />
                </MDBox>
              ))
            ) : (
              threads.length > 0 && (
                <ThreadList.Items
                  components={{ ThreadListItem: SidenavThreadListItem }}
                />
              )
            )}
          </MDBox>
        </MDBox>
      )}
    </Card>
  );
}

export default Sidenav;
