import { useState, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";

export const useLangGraphMessages = ({ stream , fetchThreads }) => {
  const [interrupt, setInterrupt] = useState();
  const [messages, setMessages] = useState([]);
  const [followupSuggestions, setFollowupSuggestions] = useState([]);
  const abortControllerRef = useRef(null);
  const runIdRef = useRef(null);

  const sendMessage = useCallback(
    async (newMessages, config) => {
      // Reset runId for each new human message
      runIdRef.current = null;

      // ensure all messages have an ID
      newMessages = newMessages.map((m) => (m.id ? m : { ...m, id: uuidv4() }));

      const messagesMap = new Map();
      const addMessages = (newMessages) => {
        if (newMessages.length === 0) return;
        for (const message of newMessages) {
          messagesMap.set(message.id ?? uuidv4(), message);
        }
        setMessages([...messagesMap.values()]);
      };

      const processStreamChunk = (chunk) => {
        if (
          chunk.event === "messages/partial" ||
          chunk.event === "messages/complete"
        ) {
          addMessages(chunk.data);
        } else if (chunk.event === "updates") {
          setInterrupt(chunk.data.__interrupt__?.[0]);
        }  else if (chunk.event === "metadata") {
          // save the run id from data and reuse it to assign message.id to the message
          const {run_id} = chunk.data;
          if (run_id) {
            runIdRef.current = run_id;
            setFollowupSuggestions([]);
          }
        } else if (chunk.event === "error") {
          const {message} = chunk.data;
          console.error("error", message);
          const assistantMessage = {
            type: 'ai',
            id: `${runIdRef.current}-full-answer`,
            content: "",
            metadata: {
              custom: {
                error: message,
              }
            }
          };
          addMessages([assistantMessage]);
        } else if (chunk.event === "values" || chunk.event === "custom") {
          const {
            steps,
            current_step,
            final_answer,
            query_result,
            plotly_image,
            plotly_json_path,
            followup_questions,
            report_title,
            query_tags,
            query_explanation,
            expanded_user_query,
            final_sql,
            query_data_processed,
            reasoning,
            source,
            progress_update
          } = chunk.data;

          if (!!followup_questions && followup_questions.length > 0) {
            setFollowupSuggestions(followup_questions.map((question) => ({
              prompt: question,
              method: "replace",
              autoSend: true,
            })));
          }

          // TODO: this will fetch threads on each final_answer, do this only when the final_answer is received for the first time in a thread
          // The purpose of this is to update the name in sidebar from `question title` to actual thread name
          if (!!final_answer) {
            fetchThreads(false);
          }

          const assistantMessage = {
            type: 'ai',
            id: `${runIdRef.current}-full-answer`,
            content: final_answer ?? "",
            metadata: {
              custom: {
                steps: (!!steps && steps.length > 0) ? steps : [],
                current_step: (!!current_step) ? current_step : "",
                reasoning: (!!reasoning) ? reasoning : [],
                source: (!!source) ? source : "",
                final_answer: (!!final_answer) ? final_answer : "",
                query_result: (!!query_result) ? query_result : {},
                plotly_image: (!!plotly_image) ? plotly_image : "",
                plotly_json_path: (!!plotly_json_path) ? plotly_json_path : "",
                report_title: (!!report_title) ? report_title : "",
                query_tags: (!!query_tags) ? query_tags : [],
                query_explanation: (!!query_explanation) ? query_explanation : "",
                expanded_user_query: (!!expanded_user_query) ? expanded_user_query : "",
                final_sql: (!!final_sql) ? final_sql : "",
                query_data_processed: (!!query_data_processed) ? query_data_processed : 0,
                progress_update: (!!progress_update) ? progress_update : null,
              }
            }
          };
          addMessages([assistantMessage]);
        }
      }

      addMessages([...messages, ...newMessages]);

      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await stream(newMessages, {
        ...config,
        runId: () => runIdRef.current,
        abortSignal: abortController.signal,
      });

      const reader = response.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = ""; // accumulator for incomplete event blocks

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode and accumulate the partial data.
        buffer += decoder.decode(value, { stream: true });

        // Process complete event blocks, using "\n\n" as separator.
        let delimiterIndex;
        while ((delimiterIndex = buffer.indexOf("\n\n")) !== -1) {
          // Extract one complete event block.
          const rawEvent = buffer.slice(0, delimiterIndex);
          buffer = buffer.slice(delimiterIndex + 2); // retain remaining data

          // Process the raw event
          const lines = rawEvent.split("\n").filter(line => line.trim());
          let eventType = null;
          let dataPayload = null;

          for (const line of lines) {
            if (line.startsWith("event:")) {
              eventType = line.replace("event:", "").trim();
            } else if (line.startsWith("data:")) {
              const dataStr = line.replace("data:", "").trim();
              try {
                dataPayload = JSON.parse(dataStr);
              } catch (error) {
                console.error("Error parsing data payload:", dataStr, error);
              }
            }
          }

          if (eventType && dataPayload !== null) {
            processStreamChunk({ event: eventType, data: dataPayload });
          }
        }
      }

      // Optionally process any remaining data in the buffer if it makes sense to do so.
      if (buffer.trim().length) {
        const lines = buffer.split("\n").filter(line => line.trim());
        let eventType = null;
        let dataPayload = null;
        for (const line of lines) {
          if (line.startsWith("event:")) {
            eventType = line.replace("event:", "").trim();
          } else if (line.startsWith("data:")) {
            const dataStr = line.replace("data:", "").trim();
            try {
              dataPayload = JSON.parse(dataStr);
            } catch (error) {
              console.error("Error parsing trailing data payload:", dataStr, error);
            }
          }
        }
        if (eventType && dataPayload !== null) {
          processStreamChunk({ event: eventType, data: dataPayload });
        }
      }
    },
    [messages, stream],
  );

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, [abortControllerRef]);

  return { interrupt, messages, sendMessage, cancel, setMessages, followupSuggestions };
};