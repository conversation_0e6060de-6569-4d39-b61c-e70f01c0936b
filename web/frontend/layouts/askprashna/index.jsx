import MyAssistant from "@/layouts/askprashna/components/MyAssistant";
import React from "react";
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import MDBox from "@/components/MDBox";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import { useMaterialUIController } from "@/context";


export default function AskPrashna() {
  const [controller, dispatch] = useMaterialUIController();
  const {shopConfig} = controller;

  const isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  const isPrashnaDisabled = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.prashna_ai ?? false);

  return (
    <DashboardLayout>
      <DashboardNavbar sections={[]} />
      <DashboardFeatureBlock feature={"prashna_ai"} />
      <MDBox
        mt={1.5}
        sx={{
          height: "100%",
          width: "100%",
          borderRadius: "16px",
          overflow: "hidden",
          backgroundColor: "white",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {!isPrashnaDisabled && <MyAssistant />}
      </MDBox>
      
    </DashboardLayout>
  );
}
