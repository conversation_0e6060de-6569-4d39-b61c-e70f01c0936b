import React from "react";
import axios from "axios";
import dayjs from 'dayjs';
import { CSVLink } from "react-csv";

import FrequencyHeatmap from "@/layouts/lifetimevalue/cohortfrequency/FrequencyHeatmap";

import FormatFilter from "@/components/Filters/FormatFilter";
import PillBar from '@/components/PillBar';
import DatePickerAnt  from "@/components/Filters/DatePickerFilter";
import {RelativeGroupingFilter} from "@/components/Filters/TimeFrameFilter";
import FilterDrawer from "@/components/Filters/FilterDrawer";
import ReviewBar from "@/examples/ReviewBar";
import MDPagination from "@/components/MDPagination";
import FeatureBlurBox from "@/components/FeatureBlurBox";

// Material Dashboard 2 PRO React components
import Divider from "@mui/material/Divider";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import MDBox from "@/components/MDBox";
import MDTooltip from "@/components/MDTooltip";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import Grid from "@mui/material/Grid";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { Element } from 'react-scroll';

let cancelToken;
import {useCancellableAxios, tracker, useMaterialUIController} from "@/context";
import { useTranslation } from "react-i18next";

const txnFrequencyBreakdowns = [
    "acquisition_period",
    "product_title",
    "product_type",
    "product_vendor",
    "sku",
    "product_tags",
    "shipping_address_country",
    "shipping_address_province",
    "shipping_address_city",
    "order_tags",
    "customer_tags"
];

export default function Dashboard(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig, shopConfig} = controller;
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    return (
        <DashboardRoot
            {...props}
            dispatch={dispatch}
            loginConfig={loginConfig}
            shopConfig={shopConfig}
            selectedShop={selectedShop}
            axiosInstance={axiosInstance}
            {...selectedFilters}
            t={t}
         />
    )
}

const isProductBreakdown = (breakdown) => {
    return breakdown == "product_title"
        || breakdown == "product_type"
        || breakdown == "product_vendor"
        || breakdown == "sku"
        || breakdown == "product_tags";
}

class DashboardRoot extends React.Component {
    constructor(props) {
        super(props);

        // defaults
        this.state = {
            frequencyGroup : 5,
            visiblePageStart: 1,
            single_product_order : 'enabled',
            breakdown : "acquisition_period",
            format: "value",
            loading: true,
            response: {},
            pageNumber: 1
        };

        this.fetchData = this.fetchData.bind(this);
        this.handleCustomerFrequencyExport = this.handleCustomerFrequencyExport.bind(this);
        this.handleFrequencyGroupChange = this.handleFrequencyGroupChange.bind(this);
        this.handleSingleProductOrderChange = this.handleSingleProductOrderChange.bind(this);
        this.handleBreakdownChange = this.handleBreakdownChange.bind(this);
        this.setPageNumber = this.setPageNumber.bind(this);
        this.handleFormatChange = this.handleFormatChange.bind(this);
        this.handleVisiblePageChange = this.handleVisiblePageChange.bind(this);
    }

    trackerEvent = (event_str, props = {}) => {
        tracker.event(event_str, props);
    };

    handleFormatChange = (fo) => {
        this.setState({ format: fo });
    };

    handleBreakdownChange = (breakdown) => {

        tracker.mixpanel.track(`Switch Breakdown`, {
            report: "cohort frequency",
            old_value: this.state.breakdown,
            new_value: breakdown
        });

        const {shopConfig} = this.props;
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false; 
        let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.transaction_frequency ?? false)

        if (isSubscriptionInActive && breakdown != "acquisition_period") {
            NiceModal.show(PaywallDialog, {feature : "transaction_frequency"})
            this.trackerEvent("Paywall", {feature: "transaction_frequency"})
            return false;
        }

        this.setState({breakdown : breakdown}, function () {
            this.fetchData(this.state);
        });
    };

    handleSingleProductOrderChange = (val) => {
        this.setState({ single_product_order: val }, function () {
            this.fetchData(this.state);
        });
    };

    setPageNumber = (page) => {
        if (page < 1) {
            return false
        }

        let {response} = this.state
        if (!response || !response.max_pages) {
            return false
        }

        if (page > response.max_pages) {
            return false
        }

        this.setState({pageNumber : page}, function () {
            this.fetchData(this.state, true, false);
        });
    };

    handleVisiblePageChange = (newVisiblePageNumber) => {
        let {visiblePageStart, response} = this.state
        let maxPages = response && response.max_pages ? response.max_pages : 1;

        console.log(newVisiblePageNumber, visiblePageStart, maxPages)

        if (!maxPages) {
            return;
        }

        if (newVisiblePageNumber < 1 || newVisiblePageNumber >= maxPages-1) {
            return;
        }

        if (newVisiblePageNumber < visiblePageStart || newVisiblePageNumber >= visiblePageStart + 1) {
            this.setState({visiblePageStart : newVisiblePageNumber});
        }
    }

    componentDidUpdate = (prevProps) => {
        // Typical usage (don't forget to compare props):
        let shopChange = prevProps.selectedShop != this.props.selectedShop
        let filterChange = prevProps.start_date.getTime() != this.props.start_date.getTime()
            || prevProps.end_date.getTime() != this.props.end_date.getTime()
            || prevProps.time_frame != this.props.time_frame
            || prevProps.filter_version != this.props.filter_version;

        if (shopChange || filterChange) {
            this.fetchData(this.state);
        }
    }

    componentDidMount = () => {
        this.fetchData(this.state, false);
    };

    fetchData = (
        {frequencyGroup, breakdown, pageNumber},
        should_track = true,
        resetPageNumber = true
    ) => {

        const {start_date, end_date, time_frame, applied_filters, axiosInstance} = this.props;
        const {metric, single_product_order} = this.state;

        var setState = (newState) => {
            return this.setState(newState);
        };

        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to new request.");
        }

        //Save the cancel token for the current request
        cancelToken = axios.CancelToken.source();

        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            time_frame,
            breakdown,
            page_number: resetPageNumber ? 1 : pageNumber,
            frequency_group : frequencyGroup,
            single_product_order : single_product_order,
            applied_filters: applied_filters ?? {},
        };

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, report: "cohort frequency", metric: metric});
        }

        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }

        setState({ loading: true });
        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/cohorts", reqData, { cancelToken: cancelToken.token })
            .then(function (response) {
                if (resetPageNumber) {
                    setState({ pageNumber: 1, visiblePageStart : 1 });
                }
                setState({ loading: false, response: response.data });
            })
            .catch((err) => {
                console.log("error", err);
                if (!axios.isCancel(err)) {
                    setState({ loading: false });
                }
            });
    };

    handleFrequencyGroupChange = (value) => {
        this.setState({frequencyGroup : value}, function () {
            this.fetchData(this.state);
        });
    };

    componentWillUnmount = () => {
        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to unmount.");
        }
    }

    getCustomerFrequencyExportData = () => {

        const {response,  loading, format} = this.state;
        const {cohort_frequency, order_count} = response;

        if (loading || order_count === 0) {
            return {headers :[], data :[]}
        }

        if (!cohort_frequency || Object.keys(cohort_frequency).length == 0) {
            return {headers :[], data :[]}
        }

        var { xLabels, yLabels, table, data } = cohort_frequency.frequency;

        let headers = [
            {
                label : "Acquisition Period",
                key: "period"
            },
            {
                label : "Cohort Size",
                key: "new_customers"
            }
        ];

        xLabels.map((x, i) => {
            headers.push({
                label : x,
                key : `ind:${i}`,
            })
        });

        let exportData = [];

        const getDataByReference = (ref) => {
            if (!(ref in cohort_frequency.data)) {
                return "";
            }

            let format_to_use = format ?? "value";

            if (format_to_use in cohort_frequency.data[ref]) {
                return cohort_frequency.data[ref][format_to_use];
            }

            return "0";
        };

        for (var yi in yLabels) {
            let rowObj = {
                period : yLabels[yi],
                new_customers: 'new_customers' in table ? (table.new_customers[yi].val || "0") : "0"
            }

            for (var xi in xLabels) {
                rowObj[`ind:${xi}`] = "0"
            }

            let period_row = data[yi] || [];
            for (var pi in period_row) {
                let val = getDataByReference(period_row[pi])
                rowObj[`ind:${pi}`] = val
            }
            
            exportData.push(rowObj);
        }

        return {headers, data: exportData}
    }

    handleCustomerFrequencyExport = () => {
        const {shopConfig} = this.props;
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        if (isSubscriptionEnabled) {
            if (!shopConfig.planDetails?.features?.transaction_frequency_export) {
                NiceModal.show(PaywallDialog, {feature : "transaction_frequency_export"})
                this.trackerEvent("Paywall", {feature: "transaction_frequency_export"})
                return false
            }
        }
        this.trackerEvent("Data Exported", {report: "cohort frequency"})
    }

    render() {
        const {
            metric,
            format,
            loading,
            response,
            frequencyGroup,
            breakdown,
            single_product_order,
            pageNumber,
            visiblePageStart
        } = this.state;
        const {t, shopConfig, time_frame} = this.props;

        const {
            currency,
            order_count,
            cohort_frequency,
        } = response;

        let maxPages = response && response.max_pages ? response.max_pages : 1;

        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false; 
        let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.transaction_frequency ?? false)
        let isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.transaction_frequency_export ?? false)

        let formatFilterProps = {
            format,
            use_currency : false,
            currency,
            handleFormatChange: this.handleFormatChange,
        };

        let frequencyHeatmapProps = {
            frequency : cohort_frequency && 'frequency' in cohort_frequency ? cohort_frequency.frequency : {},
            order_count,
            metric,
            getDataByReference : function (ref, custom_format) {
                if (!(ref in cohort_frequency.data)) {
                    return "";
                }
    
                let format_to_use = custom_format || format;
    
                if (format_to_use in cohort_frequency.data[ref]) {
                    return cohort_frequency.data[ref][format_to_use];
                }
    
                return "";
            },
            getDataObj : function (ref) {
                if (!(ref in cohort_frequency.data)) {
                    return {};
                }

                return cohort_frequency.data[ref];
            },
            loading,
            format,
            time_frame,
            breakdown
        };

        let freqExportData = {headers :[], data :[]}
        try {
            freqExportData = this.getCustomerFrequencyExportData()
        } catch(err) {
            console.log(err)
        }

        let frequencyExportButton = (
            <CSVLink
                data={freqExportData.data}
                headers={freqExportData.headers}
                filename={"Customer Frequency.csv"}
                onClick={this.handleCustomerFrequencyExport}
            >
                <MDBox>
                    <MDButton variant="outlined" color="dark" size="small">
                        <Icon>description</Icon>
                        &nbsp;{t("export-csv")}
                        {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                    </MDButton>
                </MDBox>
            </CSVLink>
        );

        let frequencyGroupOptions = [
            {
                label : t("frequency-upto-x", {count : 5}),
                value : 5
            },
            {
                label : t("frequency-upto-x", {count : 10}),
                value : 10
            },
            {
                label : t("frequency-upto-x", {count : 15}),
                value : 15
            },
            {
                label : t("frequency-upto-x", {count : 20}),
                value : 20
            }
        ];

        let breakdownOptions = txnFrequencyBreakdowns.map((b) => {

            if (isSubscriptionInActive && b != "acquisition_period") {
                return {
                    label : t(b),
                    isPremiumOption : true,
                    value : b
                }
            }

            return {
                label : t(b),
                value : b
            }
        })

        let singleProductOrderOptions = [
            {
                label : t("enabled"),
                value : 'enabled'
            },
            {
                label : t("disabled"),
                value : 'disabled'
            }
        ];

        const generatePageNumbers = () => {
            return [...Array(Math.min(3, maxPages)).keys()].map(i => visiblePageStart + i);
        };

        const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        let tooltip_title = t("p-date-block-title-3-m");
        if (time_limit === "1_year") {
            tooltip_title = t("p-date-block-title-1-yr")
        }

        return (
            <>
                <MDBox mb={3} mt={2}>
                    <Card elevation={0} mb={4} my={4}>
                        <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" pl={2} pb={2}>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}} mr={0.5}>
                                    {t("acquisition-period")}  &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <DatePickerAnt report="cohort frequency"/>
                        </Grid>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("group-by")}</MDTypography>
                            <RelativeGroupingFilter report="cohort frequency" exclude={["day"]} />
                        </Grid>
                        {breakdownOptions.length > 1 && <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("breakdown-by")}</MDTypography>
                            <PillBar
                                isDropdown={true}
                                options={breakdownOptions}
                                value={breakdown}
                                onChange={this.handleBreakdownChange}
                            />
                        </Grid>}
                        {isProductBreakdown(breakdown) && <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>
                                {t("single-product-order")} &nbsp;{<MDTooltip title={t("single-product-orders-tooltip")}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <PillBar
                                options={singleProductOrderOptions}
                                value={single_product_order}
                                onChange={this.handleSingleProductOrderChange}
                            />
                        </Grid>}
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>
                                {t("refine-cohorts")} &nbsp;{<MDTooltip title={t("filters-tooltip")}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <FilterDrawer feature="transaction_frequency" />
                        </Grid>
                        </Grid>
                    </Card>
                </MDBox>

                <MDBox mb={4} mt={2}>
                    <Element name="section-txn-freq">
                    <Card sx={{ width: "100%" }} mb={3} my={4}>
                        <MDBox display="flex" width={"100%"}>
                            <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={1} px={2} width={"100%"}>
                                <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">{t("cust-txn-freq")}</MDTypography>
                                <MDBox display="flex">
                                    <MDBox ml={1}>
                                        <PillBar
                                            isDropdown={true}
                                            options={frequencyGroupOptions}
                                            value={frequencyGroup}
                                            onChange={this.handleFrequencyGroupChange}
                                        />
                                    </MDBox>
                                    <MDBox ml={1}>
                                        {frequencyExportButton}
                                    </MDBox>
                                    <MDBox ml={1}>
                                        <FormatFilter {...formatFilterProps} />
                                    </MDBox>
                                </MDBox>
                            </MDBox>
                        </MDBox>
                        <Divider />
                        <MDBox px={1} pt={1} pb={3}>
                            <FeatureBlurBox feature="transaction_frequency">
                            <FrequencyHeatmap {...frequencyHeatmapProps} />
                            </FeatureBlurBox>
                            {!!maxPages && maxPages > 1 && <MDBox mt={2}> 
                            <MDPagination variant="contained">
                                <MDPagination item onClick = {() => this.handleVisiblePageChange(visiblePageStart - 1)}>
                                <Icon>keyboard_arrow_left</Icon>
                                </MDPagination>
                                {generatePageNumbers().map((page) => {
                                return (
                                    <MDPagination item key={page} active={pageNumber == page} onClick = {() => this.setPageNumber(page)}>
                                    {page}
                                    </MDPagination>
                                );
                                })}
                                <MDPagination item onClick = {() => this.handleVisiblePageChange(visiblePageStart + 1)}>
                                <Icon>keyboard_arrow_right</Icon>
                                </MDPagination>
                            </MDPagination>
                            </MDBox>}
                        </MDBox>
                    </Card>
                    </Element>
                </MDBox>
                <ReviewBar />
            </>
        );
    }
}
