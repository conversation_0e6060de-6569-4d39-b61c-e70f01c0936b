import React from "react";
import HeatMap from "@/components/Heatmap";
import Skeleton from "@mui/material/Skeleton";
import MDTooltip from "@/components/MDTooltip";
import Empty from "@/components/EmptyChart";
import { useTranslation } from "react-i18next";
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: "white",
        padding: 0,
        margin: 0,
        // color: 'rgba(0, 0, 0, 0.87)',
        // boxShadow: theme.shadows[1],
        fontSize: 12,
    },
    [`& .${tooltipClasses.arrow}`]: {
        color: "white",
    },
  }));


function SkeletonDataTable() {
    var row = (ind) => (
        <tr>
            {new Array(ind).fill(0).map((a, ai) => (
                <td key={ai} style={{ padding: "3px 0" }}>
                    <Skeleton variant="rectangular" height={35} width={100} sx={{borderRadius:"8px"}} animation="wave" />
                </td>
            ))}
        </tr>
    );
    return (
        <table style={{ width: "100%", borderSpacing: "10px" }}>
            <tbody>
                {row(7)}
                {row(7)}
                {row(7)}
            </tbody>
        </table>
    );
}


function getCellToolTip(
    cohort_name,
    cohort_size,
    display_value,
    display_percentage,
    index
) {
    const {t} = useTranslation();
    return (
        <Card elevation={10}>
            <MDBox p={1} variant="gradient" px={2.5} display="flex" flexDirection="column" alignItems="flex-start">
                <MDTypography variant="button" fontWeight="regular" ml={0.4} fontSize="13px" mb={1.5} >
                    <span dangerouslySetInnerHTML={{__html: t("cohort-freq-tooltip-1", {display_value, display_percentage, index})}} />
                </MDTypography>
                {cohort_name && (
                    <MDTypography variant="caption" color="secondary" fontWeight="regular" fontSize="inherit">
                        <span dangerouslySetInnerHTML={{__html: t("cohort-freq-tooltip-2", {cohort_size, cohort_name})}} />
                        <br/>
                    </MDTypography>
                )}
            </MDBox>
        </Card>
    );
}

export default function FrequencyHeatmap(props) {
    const {t} = useTranslation();
    return <FrequencyHeatmapRoot {...props} t={t} />;
}

class FrequencyHeatmapRoot extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        const {
            getDataByReference,
            getDataObj,
            loading,
            frequency,
            format,
            order_count,
            time_frame,
            t,
            breakdown,
        } = this.props;

        if (loading) {
            return <SkeletonDataTable />;
        }

        if (order_count === 0) {
            return (
                <Empty
                    description={
                        <p>
                            {t("no-orders-found")}
                        </p>
                    }
                />
            );
        }

        if (!frequency || Object.keys(frequency).length == 0) {
            return (
                <Empty
                    description={
                        <p>
                            {t("something-went-wrong")}
                        </p>
                    }
                />
            );
        }

        var { xLabels, yLabels, data, table, table_sum } = frequency;
        let isTimeCohorts = breakdown == "acquisition_period";

        yLabels = yLabels.map((x) => {
            let renderX = x;
            if (x.length > 22) {
                renderX = x.slice(0, 22) + "..";
            }
    
            let renderColor = "#475666";
            let renderTitle = x
            if (x == "<MISSING>") {
                renderX = t("missing-or-empty");
                renderColor = "#f44336";
                renderTitle = t("missing-data-tooltip")
            }
    
            return <MDTooltip placement="right" title={renderTitle}>
                    <div style={{ color: renderColor }}>{renderX}</div>
            </MDTooltip>
        });

        xLabels = xLabels.map((x) => {
            let labelNode = (
                <div style={{ fontWeight: "500", color: "#475666" }}>{x}</div>
            );

            let tooltipNode = null;

            if (tooltipNode) {
                return (
                    <MDTooltip placement="top" title={tooltipNode}>
                        {labelNode}
                    </MDTooltip>
                );
            }

            return labelNode;
        });

        var data_values = data.map((row) =>
            row.map((ref) => getDataByReference(ref))
        );

        var prependXLabels = [];
        var prependTable = [];

        var appendXLabels = [];
        var appendTable = [];

        var totalRow = [];
        let xLabelsName = t("txn-freq");

        let xLabelsBottom = [];

        if (!!table && Object.keys(table).length > 0) {
            let newCustomersNodeTooltip = (
                <div className="custom-ant-tooltip-basic">
                    {isTimeCohorts ? t("acq-cust-time", {time_frame}) : t("acq-cust-cohort")}
                </div>
            );

            let newCustomersNode = (
                <MDTooltip placement="top" title={newCustomersNodeTooltip}>
                    <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                        <u>{t("new-customers")}</u>
                    </div>
                </MDTooltip>
            );

            prependXLabels.push(newCustomersNode);
            prependTable.push(table.new_customers);
        }

        if (!!table_sum && Object.keys(table_sum).length > 0) {
            totalRow.push(table_sum.new_customers);
            xLabels.map((x) => {
                totalRow.push("");
            });
        }

        function customCellRender(value, x, y) {
            var ref = data[y][x];
            if (!ref) {
                return null;
            }

            var display = getDataByReference(ref, `display_${format}`);
            let refData = getDataObj(ref);

            let cohort_name =
                !!refData && "cohort_name" in refData ? refData.cohort_name : null;
            let bucket_index =
                !!refData && "bucket_index" in refData ? refData.bucket_index : null;
            let cohort_size =
                !!refData && "cohort_size" in refData ? refData.cohort_size : null;

            var display_value = getDataByReference(ref, `display_value`);
            var display_percentage = getDataByReference(ref, `display_percentage`);
            let tooltipNode = getCellToolTip(
                cohort_name,
                cohort_size,
                display_value,
                display_percentage,
                bucket_index
            );

            return (
                <HtmlTooltip placement="right" title={tooltipNode}>
                    <div>{display}</div>
                </HtmlTooltip>
            );

        }

        let originNodeTooltip = (
            isTimeCohorts ? 
                <div className="custom-ant-tooltip-basic">{t("acquired-tooltip", {time_frame})}</div>
                : <div className="custom-ant-tooltip-basic">{t("first-order-tooltip", {breakdown : t(breakdown)})}</div>
        );
    
        let originNode = (
            <MDTooltip placement="right" title={originNodeTooltip}>
                <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                    <u>{isTimeCohorts ? t("acquired-in") : t("first-order-breakdown", {breakdown : t(breakdown)})}</u>
                </div>
            </MDTooltip>
        );

        const flatArray = data_values.map(r => r.filter((e, i) => i != 0)).reduce((i, o) => [...o, ...i], []);
        const maxNew = Math.max(...flatArray);
        const minNew = Math.min(...flatArray);

        return (
            <HeatMap
                yLabelTextAlign={"center"}
                xLabelsName={xLabelsName}
                xLabelsBottom={xLabelsBottom}
                totalRow={isTimeCohorts ? totalRow : []}
                prependXLabels={prependXLabels}
                prependTable={prependTable}
                appendXLabels={appendXLabels}
                appendTable={appendTable}
                xLabels={xLabels}
                originNode={originNode}
                yLabels={yLabels}
                xLabelsLocation={"top"}
                yLabelWidth={isTimeCohorts ? 100 : 180}
                data={data_values}
                height={38}
                title={() => { }}
                background="#1A73E8"
                onClick={() => { }}
                cellStyle={(bg, value, min, max, data, x, y) => {

                    let val_weight = 1.1 - (maxNew - value) / (maxNew - minNew);

                    if (x == 0) {
                        // first column not needed
                        val_weight = 0.1
                    }

                    let color = val_weight < 0.4 ? "#475666" : "#ffffff";
                    let cursor = "pointer";
                    let visibility = "visible";
                    let background = `rgb(216, 27, 96, ${val_weight})`;
                    return {
                        background,
                        fontSize: "12px",
                        fontWeight: "500",
                        color,
                        cursor,
                        visibility,
                    };
                }}
                cursor="pointer"
                cellRender={customCellRender}
            />
        );
    }
}
