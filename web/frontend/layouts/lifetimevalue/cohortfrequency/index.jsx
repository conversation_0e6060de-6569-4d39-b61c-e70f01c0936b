import CohortFrequency from "./CohortFrequency";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";

function CustomerFrequency() {

  const sections=["section-txn-freq"];
  return (
    <DashboardLayout>
      <DashboardNavbar sections={sections}/>
      <DashboardFeatureBlock feature={"transaction_frequency"} />
      <CohortFrequency />
      <Footer />
    </DashboardLayout>
  );
}

export default CustomerFrequency;
