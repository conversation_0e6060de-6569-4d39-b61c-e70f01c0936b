import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
// antd imports
import Icon from "@mui/material/Icon";
import MDButton from "@/components/MDButton";


// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import avatarImage from "@/assets/images/sumit.jpeg";

import {tracker} from "@/context";
import {useTranslation} from "react-i18next";

export default function BookCallCard(props) {
    const [closed, setClose] = useState(false);
    const {t} = useTranslation();

    if (closed) {
        return null
    }

    return (
        <MDBox mt={3} mb={3} >
        <Grid container spacing={3} justifyContent="center" alignItems={"center"}>
            <Grid item xs={12}>
                <Card>
                <MDBox py={3} px={2}>
                <MDBox display="flex" alignItems="center">
                <MDBox mr={3}>
                    <MDAvatar size="xxl" src={avatarImage} alt="Sumit" />
                </MDBox>
                <MDBox lineHeight={1}>
                    <MDTypography variant="h6" fontWeight="medium" mb={1}>
                        {t("book-call-title")}
                    </MDTypography>
                    <MDBox mb={2}>
                        <MDTypography variant="button" color="dark">
                            {t("book-call-desc")}
                        </MDTypography>
                    </MDBox>
                    <Link to="/book-call">
                    <MDButton variant="gradient" color="info" size="small" onClick={() => {tracker.event("BookCall Started", {
                        report: "cohort analysis"
                    })}}>
                        <Icon>event_available</Icon>&nbsp;
                        {t("book-call-cta")}
                    </MDButton>
                    </Link>
                    <MDButton variant="text" color="secondary" size="small" onClick={() => {
                        tracker.event("BookCall Closed", {report: "cohort analysis"})
                        setClose(true)
                    }}>
                        {t("later")}
                    </MDButton>
                </MDBox>
                </MDBox>
                </MDBox>
                </Card>
            </Grid>
        </Grid>
        </MDBox>
    );
}