import React from "react";

// antd imports
import {Tooltip} from "antd";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React examples
import DefaultStatisticsCard from "@/examples/Cards/StatisticsCards/DefaultStatisticsCard";

// @mui material components
import Grid from "@mui/material/Grid";
import { Skeleton } from "@mui/material";

var ValueCell = ({loading, children}) => {
    if (loading) {
        return (<Skeleton variant="rectangular" width={"100%"}  />)
    }

    return <MDTypography>{children}</MDTypography>
}

export default class Highlights extends React.Component {
    render() {
        const { highlights, loading } = this.props;

        if (!highlights || Object.keys(highlights).length == 0) {
            var aov = 0;
            var ltv_60 = 0;
            var ltv_90 = 0;
            var ltv_180 = 0;
        } else {
            var { aov, ltv_60, ltv_90, ltv_180 } = highlights;
        }

        let aov_title = (
            <div>
                <span className="stat-title" style={{ marginRight: "5px" }}>AOV</span>
                <Tooltip
                    title={
                        <div style={{ fontSize: "13px", padding: "5px" }}>
                            Total revenue from all new customers in selected duration by total
                            no. of orders
                        </div>
                    }
                    placement="right"
                >
                    <span style={{ fontSize: "14px" }}>
                        <InfoOutlinedIcon />
                    </span>
                </Tooltip>
            </div>
        );
        let aov_formatter_na = (
            <span style={{ fontSize: "18px", marginRight: "5px" }}>
                NA{" "}
                <span style={{ fontSize: "14px" }}>
                    <Tooltip
                        title={
                            <div style={{ fontSize: "13px", padding: "5px" }}>
                                Not enough data.
                            </div>
                        }
                    >
                        <InfoOutlinedIcon />
                    </Tooltip>
                </span>
            </span>
        );
        let LtvStatTitle = ({ days }) => {
            return (
                <div>
                    <span className="stat-title" style={{ marginRight: "5px" }}>{days} Day LTV</span>
                    <Tooltip
                        title={
                            <div style={{ fontSize: "13px", padding: "5px" }}>
                                {`Customers lifetime value by ${days}th Day after first order, from acquired customers in selected duration`}
                            </div>
                        }
                    >
                        <span style={{ fontSize: "14px" }}>
                            <InfoOutlinedIcon />
                        </span>
                    </Tooltip>
                </div>
            );
        };

        var ltv_formatter_na = (
            <span style={{ fontSize: "18px", marginRight: "5px" }}>
                NA{" "}
                <span style={{ fontSize: "14px" }}>
                    <Tooltip
                        title={
                            <div style={{ fontSize: "13px", padding: "5px" }}>
                                Not enough data. Try expanding time duration.
                            </div>
                        }
                    >
                        <InfoOutlinedIcon />
                    </Tooltip>
                </span>
            </span>
        );

        return (
            <MDBox py={2}>
                <MDBox mb={2}>
                <Grid container spacing={3}>
                    <Grid item xs={12} sm={3}>
                        <DefaultStatisticsCard
                            title={aov_title}
                            count={!!aov ? (<ValueCell loading={loading}>{aov}</ValueCell>) : aov_formatter_na}
                        />
                    </Grid>
                    <Grid item xs={12} sm={3}>
                        <DefaultStatisticsCard
                            title={<LtvStatTitle days={60} />}
                            count={!!ltv_60 ? (<ValueCell loading={loading}>{ltv_60}</ValueCell>) : ltv_formatter_na}
                        />
                    </Grid>
                    <Grid item xs={12} sm={3}>
                        <DefaultStatisticsCard
                            title={<LtvStatTitle days={90} />}
                            count={!!ltv_90 ? (<ValueCell loading={loading}>{ltv_90}</ValueCell>) : ltv_formatter_na}
                        />
                    </Grid>
                    <Grid item xs={12} sm={3}>
                        <DefaultStatisticsCard
                            title={<LtvStatTitle days={180} />}
                            count={!!ltv_180 ? (<ValueCell loading={loading}>{ltv_180}</ValueCell>) : ltv_formatter_na}
                        />
                    </Grid>
                </Grid>
                </MDBox>
            </MDBox>
        );
    }
}
