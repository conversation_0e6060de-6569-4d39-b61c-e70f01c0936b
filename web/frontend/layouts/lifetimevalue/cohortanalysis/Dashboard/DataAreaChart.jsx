import React from "react";
import {
    AreaChart,
    LineChart,
    Line,
    Area,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
} from "recharts";
import { metrics } from "@/components/Filters/MetricSelector";
import { relativeGroups } from "@/components/Filters/TimeFrameFilter";
// antd imports
import {Col, Row, Checkbox, Spin, Progress} from "antd";
import Empty from "@/components/EmptyChart";
import CircularProgress from "@mui/material/CircularProgress";
import MDBox from "@/components/MDBox";
import Card from "@mui/material/Card";
import MDTypography from "@/components/MDTypography";
import numeral from "numeral";

// @mui material components
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import { useTranslation } from "react-i18next";
import { getColors } from "@/util";


function getCellToolTip(
    payload,
    metric,
    cohort_name,
    display_value,
    display_percentage,
    display_cumulative_total,
    tf_name,
    index,
    fill_ref_color,
    t
) {

    if (metric in metrics) {
        let info = metrics[metric].tooltip(
            display_value,
            display_percentage,
            display_cumulative_total,
            tf_name,
            index,
            t
        );

        let mx = Math.max(...payload.map((p) => p.value))

        let bucket_name = ""
        if (payload.length > 0 && payload[0].payload) {
            bucket_name = payload[0].payload.bucket ?? "";
        }

        return (
            <Card p={2} raised style={{backgroundColor: "#f0f2f5", zIndex : 9999}}>
            <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" style={{userSelect: "none", minWidth:"300px"}} >
                {cohort_name && (
                    <MDTypography variant={"button"} p={1} color="inherit" fontWeight="medium" style={{color : !!fill_ref_color ? fill_ref_color : "green"}}>
                        {cohort_name != "Weighted Avg" ? "Cohort:" : ""} {cohort_name}
                    </MDTypography>
                )}
                {cohort_name && info && <MDBox px={1} mb={2}>
                    <MDTypography variant="caption" color="text" fontWeight="regular">
                        {info}
                    </MDTypography>
                </MDBox>}

                <TableContainer sx={{ height: "100%", boxShadow: "none", borderTopLeftRadius:"0", borderTopRightRadius:"0"}}>
                <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" style={{userSelect: "none", minWidth:"300px"}} >
                    <MDTypography variant="button" fontWeight="regular" my={1}>
                        {bucket_name}
                    </MDTypography>
                </MDBox>
                <Table size="small" >
                    <TableBody>
                        {payload && payload.length > 0 && payload.map((p, ind) => {
                            if (ind >= 5) {
                                return null
                            }
                            var percent = 10
                            if (mx > 0) {
                                percent = (p.value*100/mx)
                            } else {
                                percent = 100
                            }
                            return (
                                <TableRow key={p.name ?? ""}>
                                    <TableCell padding="none" align="center" sx={{ border: 0, color : p.color }}>
                                        <MDBox display="flex" flexDirection="column">
                                            <MDTypography
                                                variant="caption"
                                                color="text"
                                                fontWeight="medium"
                                                textTransform="capitalize"
                                                style={{color : p.color}}
                                            >
                                                {!!p.name ? (p.name.length > 30 ? (p.name.substring(0, 30) + "...") : p.name) : ""}
                                            </MDTypography>
                                        </MDBox>
                                    </TableCell>
                                    <TableCell padding="none" align="center" sx={{ border: 0, color : p.color }}>
                                        <MDBox display="flex" flexDirection="column">
                                            <Progress
                                                style={{width:'100px'}}
                                                percent={percent}
                                                showInfo={false}
                                                strokeWidth={10}
                                                strokeColor={p.color}
                                            />
                                        </MDBox>
                                    </TableCell>
                                    <TableCell padding="none" align="center" sx={{ border: 0, color : p.color }}>
                                        <MDBox display="flex" flexDirection="column">
                                            <MDTypography variant="button" fontWeight="regular" textTransform="capitalize">
                                                {p.value ? numeral(p.value).format(metric == "acc_order_count_per_customer" ? "0,0.[00]" : "0,0") : ""}
                                            </MDTypography>
                                        </MDBox>
                                    </TableCell>
                                </TableRow>
                            )
                        })}
                    </TableBody>
                </Table>
                {payload.length >= 5 && <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" style={{userSelect: "none", minWidth:"300px"}}>
                    <MDTypography variant="caption" fontWeight="light" my={2} sx={{maxWidth: "250px"}}>
                            {t("trend-more-cohorts")}
                        </MDTypography>
                </MDBox>}
                </TableContainer>
            </MDBox>
            </Card>
        );
    }

    return null;
}

const CustomAreaTooltip = ({ active, payload, label, metric }) => {
    if (active && payload && payload.length) {
        return (
            <div
                className="custom-tooltip-full"
                style={{ backgroundColor: "rgba(255,255,255,0.9)" }}
            >
                <div className="custom-ant-tooltip-basic" style={{ width: 170 }}>
                    <div style={{ fontSize: "13px", marginBottom: "5px" }}>Cohorts</div>
                    <div>
                        {payload.reverse().map((p, index) => {
                            if (!p || !p.value || !p.name) {
                                return null;
                            }

                            let showVal = p.value;
                            if (
                                p.dataKey &&
                                p.payload &&
                                p.payload.dataPayload &&
                                p.payload.dataPayload[p.dataKey] &&
                                p.payload.dataPayload[p.dataKey][metric]
                            ) {
                                let refData = p.payload.dataPayload[p.dataKey];
                                showVal = refData[metric].display_value ?? "";
                            }

                            return (
                                <div
                                    key={`item-${index}`}
                                    style={{
                                        color: p.color ?? "grey",
                                        paddingBottom: "2px",
                                        fontSize: "12.5px",
                                    }}
                                >
                                    <Row gutter={6}>
                                        <Col span={15}>{p.name}</Col>
                                        <Col span={9} style={{ textAlign: "left" }}>
                                            {showVal}
                                        </Col>
                                    </Row>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        );
    }

    return null;
};

const CustomLineTooltip = ({
    active,
    payload,
    label,
    refData,
    metric,
    time_frame,
    fill_ref_color,
    t
}) => {
    if (active && payload && payload.length) {

        let cohort_name = ""
        let bucket_index = ""
        let display_value = ""
        let display_percentage = ""
        let display_cumulative_total = ""

        if (refData && (metric in refData)) {
            cohort_name = refData.cohort_name ?? "";
            bucket_index = refData.bucket_index ?? "";
            display_value = refData[metric].display_value ?? "";
            display_percentage = refData[metric].display_percentage ?? "";
            display_cumulative_total = refData[metric].display_cumulative_total ?? "";
        }

        let tooltipNode = getCellToolTip(
            payload,
            metric,
            cohort_name,
            display_value,
            display_percentage,
            display_cumulative_total,
            time_frame,
            bucket_index,
            fill_ref_color,
            t
        );

        return tooltipNode;
    }

    return null;
};

class CustomizedLabel extends React.Component {
    render() {
        const { x, y, stroke, value } = this.props;

        return (
            <text
                x={x}
                y={y}
                dx={-14}
                fill={stroke}
                fontSize={12}
                textAnchor="middle"
            >
                {numeral(value).format("0.[0]a")}
            </text>
        );
    }
}

const CustomLegend = (props) => {
    const { payload, handleLabelCheckboxClick, selected } = props;
    const {t} = useTranslation();
    let color = "brown";
    return (
        <ul
            style={{
                listStyleType: "none",
                height: 250,
                overflow: "auto",
                paddingRight: "10px",
            }}
        >
            <li key={`item-all`}>
                <Checkbox
                    checked={selected.length == 0}
                    style={{
                        color: color,
                        cursor: "pointer",
                        "--background-color": color,
                        "--border-color": color,
                    }}
                    onChange={() => {
                        return handleLabelCheckboxClick("reset");
                    }}
                >
                    {t("show-all-cohorts")}
                </Checkbox>
            </li>

            {payload.map((entry, index) => {
                const { color, value, dataKey } = entry;
                let inactive = false;
                if (selected.length) {
                    inactive = selected.indexOf(dataKey) == -1;
                }
                return (
                    <li key={`item-${index}`}>
                        <Checkbox
                            checked={!inactive}
                            style={{
                                color: color,
                                cursor: "pointer",
                                "--background-color": color,
                                "--border-color": color,
                            }}
                            onChange={() => {
                                return handleLabelCheckboxClick(dataKey);
                            }}
                        >
                            {value.length > 30 ? (value.substring(0, 30) + "...") : value}
                        </Checkbox>
                    </li>
                );
            })}
        </ul>
    );
}

export default class DataChart extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            mouse_over_line: "",
            selected: [],
            fill_ref_color: "",
            selected_reference: "",
        };
        this.activeDotMouseLeave = this.activeDotMouseLeave.bind(this);
        this.onLineMouseOut = this.onLineMouseOut.bind(this);
        this.handleLabelCheckboxClick = this.handleLabelCheckboxClick.bind(this);
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        return null;
        if (nextProps.area_chart && Object.keys(nextProps.area_chart) != 0) {
            var { cohorts } = nextProps.area_chart;
            for (var ck in cohorts) {
                var cohort = cohorts[ck];
                if (cohort.dataKey == prevState.selected) {
                    // selected cohort still exists, do not reset selected
                    return null;
                }
            }
        }

        return {
            selected: "",
        };
    }

    handleLabelCheckboxClick = (dk) => {
        if (dk == "reset") {
            this.setState({ selected: [] });
            return;
        }

        this.setState((prevState) => {
            if (prevState.selected.indexOf(dk) != -1) {
                return { selected: prevState.selected.filter((s) => s != dk) };
            } else {
                return { selected: prevState.selected.concat([dk]) };
            }
        });
    };

    activeDotMouseOver = (dk, e, lineObj) => {
        if (
            lineObj &&
            lineObj.payload &&
            lineObj.dataKey &&
            lineObj.payload.dataPayload &&
            lineObj.payload.dataPayload[lineObj.dataKey]
        ) {
            this.setState({
                selected_reference:
                    lineObj.payload.dataPayload[lineObj.dataKey].reference ?? "",
                fill_ref_color: lineObj.fill ?? "",
                mouse_over_line: dk,
            });
        }
    };

    activeDotMouseLeave = () => {
        this.setState({ selected_reference: "", mouse_over_line: "" });
    };

    onLineMouseOut = () => {
        this.setState({
            mouse_over_line: "",
        });
    };

    onLineMouseOver = (dk) => {
        this.setState({
            mouse_over_line: dk,
        });
    };

    renderLineChart = () => {
        const {
            metric,
            getDataByReference,
            getDataObj,
            currency,
            line_chart,
            time_frame,
            t,
            firstOrderToggle,
            breakdown,
        } = this.props;
        const {
            selected,
            selected_reference,
            fill_ref_color,
            mouse_over_line,
        } = this.state;

        let lineCohorts = line_chart.cohorts;
        // hack = removing weighted_avg cohort for other metrics
        if (metric != "acc_total_sales_per_customer" || breakdown != "acquisition_period") {
            lineCohorts = lineCohorts.filter((ch) => ch.dataKey != "weighted_avg");
        }

        var lineCohortData = [];
        for (var bk in line_chart.data) {
            let bucketName = line_chart.data[bk]["bucket"]
            if (firstOrderToggle && bucketName == 0) {
                continue;
            }

            var bucket = {
                bucket: `${relativeGroups[time_frame].full} ${bucketName}`,
                dataPayload: {},
            };

            for (var ck in lineCohorts) {
                var cohort = lineCohorts[ck];
                if (cohort.dataKey in line_chart.data[bk]) {
                    bucket[cohort.dataKey] = getDataByReference(
                        line_chart.data[bk][cohort.dataKey],
                        "value"
                    );
                    let refObj = getDataObj(line_chart.data[bk][cohort.dataKey]);
                    refObj.reference = line_chart.data[bk][cohort.dataKey];
                    bucket.dataPayload[cohort.dataKey] = refObj;
                }
            }

            lineCohortData.push(bucket);
        }

        let yAxisLabelText = `${t(metrics[metric].label)}`;
        if (metrics[metric].format == "currency") {
            yAxisLabelText += ` (${currency})`;
        }

        let refData = getDataObj(selected_reference);
        const colorArr = getColors(lineCohorts.length);
        const Lines = lineCohorts.map((a, ai) => {
            let strokeOpacity = 1;
            let strokeWidth = 2.5;
            if (!!mouse_over_line) {
                strokeOpacity = mouse_over_line == a.dataKey ? 1.5 : 0.5;
                strokeWidth = mouse_over_line == a.dataKey ? 2.5 : 1;
            }
            return (
                <Line
                    label={selected.length == 1 && <CustomizedLabel />}
                    activeDot={{
                        cursor: "pointer",
                        onMouseOver: this.activeDotMouseOver.bind(this, a.dataKey),
                        onMouseLeave: this.activeDotMouseLeave,
                    }}
                    onMouseOver={this.onLineMouseOver.bind(this, a.dataKey)}
                    onMouseOut={this.onLineMouseOut}
                    hide={
                        selected.length == 0 ? false : selected.indexOf(a.dataKey) == -1
                    }
                    type="monotoneX"
                    name={a.name}
                    dot={false}
                    strokeWidth={strokeWidth}
                    strokeOpacity={strokeOpacity}
                    key={a.dataKey}
                    dataKey={a.dataKey}
                    stroke={colorArr[ai]}
                />
            );
        });

        return (
            <ResponsiveContainer width="100%" height={400} style={{padding:"5px"}}>
                <LineChart
                    data={lineCohortData}
                    margin={{ top: 10, right: 0, left: 0, bottom: 10 }}
                >
                    <CartesianGrid strokeDasharray="3 3" />
                    <Tooltip
                        filterNull
                        content={
                            <CustomLineTooltip
                                t={t}
                                refData={refData}
                                metric={metric}
                                time_frame={time_frame}
                                fill_ref_color={fill_ref_color}
                            />
                        }
                    />
                    <XAxis
                        dataKey="bucket"
                        padding={{ right: 20 }}
                        margin={{ bottom: 20 }}
                        label={{
                            value: `${relativeGroups[time_frame].full}(s) after first order`,
                            position: "bottom",
                            offset: 0,
                        }}
                    />
                    <YAxis
                        label={{
                            value: yAxisLabelText,
                            angle: -90,
                            position: "insideBottomLeft",
                            dy: 0,
                            dx: 10,
                        }}
                        interval={0}
                        tickFormatter={(value) => {
                            return numeral(value).format("0.[0]a")
                        }}
                        width={100}
                        domain={["auto", "auto"]}
                    />
                    <Legend
                        align="right"
                        verticalAlign="middle"
                        layout="vertical"
                        content={
                            <CustomLegend
                                handleLabelCheckboxClick={this.handleLabelCheckboxClick}
                                selected={selected}
                            />
                        }
                    />
                    {Lines}
                </LineChart>
            </ResponsiveContainer>
        );
    };

    renderAreaChart = () => {
        const {
            metric,
            getDataByReference,
            getDataObj,
            currency,
            area_chart,
            time_frame,
            t,
        } = this.props;
        const { selected } = this.state;

        var areaCohortData = [];
        for (var bk in area_chart.data) {
            var bucket = {
                bucket: area_chart.data[bk]["bucket"],
                dataPayload: {},
            };

            for (var ck in area_chart.cohorts) {
                var cohort = area_chart.cohorts[ck];
                if (cohort.dataKey in area_chart.data[bk]) {
                    bucket[cohort.dataKey] = getDataByReference(
                        area_chart.data[bk][cohort.dataKey],
                        "value"
                    );
                    let refObj = getDataObj(area_chart.data[bk][cohort.dataKey]);
                    refObj.reference = area_chart.data[bk][cohort.dataKey];
                    bucket.dataPayload[cohort.dataKey] = refObj;
                }
            }

            areaCohortData.push(bucket);
        }

        let yAxisLabelText = `${t(metrics[metric].label)}`;
        if (metrics[metric].format == "currency") {
            yAxisLabelText += ` (${currency})`;
        }

        const colorArr = getColors(area_chart.cohorts.length);
        const gradients = area_chart.cohorts.map((a, ai) => {
            return (
                <linearGradient
                    key={`color${ai}`}
                    id={`color${ai}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                >
                    <stop offset="5%" stopColor={colorArr[ai]} stopOpacity={0.3} />
                    <stop offset="95%" stopColor={colorArr[ai]} stopOpacity={0.3} />
                </linearGradient>
            );
        });
        const Areas = area_chart.cohorts.map((a, ai) => {
            return (
                <Area
                    // label={selected.length == 1 && <CustomizedLabel />}
                    activeDot={{ custor: "pointer" }}
                    hide={
                        selected.length == 0 ? false : selected.indexOf(a.dataKey) == -1
                    }
                    type="monotone"
                    dot={{ strokeWidth: 1 }}
                    name={a.name}
                    key={a.dataKey}
                    dataKey={a.dataKey}
                    stroke={colorArr[ai]}
                    strokeWidth={2}
                    strokeOpacity={1}
                    cursor="pointer"
                    fillOpacity={1}
                    stackId="1"
                    fill={`url(#color${ai})`}
                />
            );
        });

        return (
            <ResponsiveContainer width="100%" height={350}>
                <AreaChart
                    data={areaCohortData}
                    margin={{ top: 10, right: 0, left: 0, bottom: 0 }}
                >
                    <defs>{gradients}</defs>
                    <Tooltip filterNull content={<CustomAreaTooltip metric={metric} />} />
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                        dataKey="bucket"
                        padding={{ right: 20 }}
                        label={{
                            value: `${relativeGroups[time_frame].full}(s) after first order`,
                            position: "bottom",
                            offset: 15,
                        }}
                    />
                    <YAxis
                        label={{
                            value: yAxisLabelText,
                            angle: -90,
                            position: "insideBottomLeft",
                            dy: -10,
                            dx: 10,
                        }}
                        width={80}
                    />
                    <Legend
                        align="right"
                        verticalAlign="middle"
                        layout="vertical"
                        content={
                            <CustomLegend
                                handleLabelCheckboxClick={this.handleLabelCheckboxClick}
                                selected={selected}
                            />
                        }
                    />
                    {Areas}
                </AreaChart>
            </ResponsiveContainer>
        );
    };

    render() {
        const { metric, loading, area_chart, line_chart, order_count, t } = this.props;

        if (
            order_count === 0 ||
            !area_chart ||
            Object.keys(area_chart).length == 0 ||
            !line_chart ||
            Object.keys(line_chart).length == 0
        ) {

            if (loading) {
                return (
                    <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="250px" height="100%">
                        <CircularProgress color="secondary" />
                    </MDBox>
                );
            }

            return (
                <Empty
                    description={
                        <p>
                            {order_count === 0 ? t("no-orders-found") : t("something-went-wrong")}
                        </p>
                    }
                />
            );
        }

        return (
            <Spin
                indicator={
                    <CircularProgress color="secondary" />
                }
                spinning={loading}
            >
                <div
                    style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    {metrics[metric].chart == "area" && this.renderAreaChart()}
                    {metrics[metric].chart == "line" && this.renderLineChart()}
                </div>
            </Spin>
        );
    }
}
