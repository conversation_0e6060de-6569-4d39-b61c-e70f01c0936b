import React from "react";
import axios from "axios";
import dayjs from 'dayjs';
import { CSVLink } from "react-csv";

import DataChart from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/DataAreaChart.jsx";
import BookCallCard from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/BookCallCard.jsx";
import WriteReviewCard from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/WriteReviewCard.jsx";
import LTVTrend from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/LTVTrend.jsx";
import CohortAnalysis from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/CohortAnalysis";
import Switch from "@mui/material/Switch";
import PillBar from '@/components/PillBar';
import DatePickerAnt  from "@/components/Filters/DatePickerFilter";
import {RelativeGroupingFilter} from "@/components/Filters/TimeFrameFilter";
import ConnectionCard from "@/components/FacebookConnector";
import FilterDrawer from "@/components/Filters/FilterDrawer";
import { metrics } from "@/components/Filters/MetricSelector";
import ReviewBar from "@/examples/ReviewBar";
import { Element } from 'react-scroll'

// Material Dashboard 2 PRO React components
import Divider from "@mui/material/Divider";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import MDBox from "@/components/MDBox";
import MDTooltip from "@/components/MDTooltip";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import Grid from "@mui/material/Grid";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";

let cancelToken;
import {useCancellableAxios, tracker, useMaterialUIController, setSelectedFilters} from "@/context";
import { useTranslation } from "react-i18next";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import FeatureBlurBox from "@/components/FeatureBlurBox";

let presets = {
    "ltv-cohorts": {
        "breakdowns": ["acquisition_period"],
        "feature_flag": "ltv_cohorts",
        "export_flag" : "ltv_cohorts_export"
    },
    "product-cohorts": {
        "breakdowns": ["product_title", "product_type", "product_vendor", "sku", "product_tags"],
        "feature_flag": "product_cohorts",
        "export_flag" : "product_cohorts_export"
    },
    "location-cohorts": {
        "breakdowns": ["shipping_address_province", "shipping_address_country", "shipping_address_city"],
        "feature_flag": "location_cohorts",
        "export_flag" : "location_cohorts_export"
    },
    "custom-cohorts": {
        "breakdowns": ["order_tags", "customer_tags"],
        "feature_flag": "custom_cohorts",
        "export_flag" : "custom_cohorts_export"
    }
}

export default function Dashboard(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig, shopConfig} = controller;
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    const feat = presets[props.preset]?.feature_flag;

    let setRequestType = (val) => {
        setSelectedFilters(dispatch, {request_type : val})
    }

    return (
        <>
            <DashboardFeatureBlock feature={feat} />
            <DashboardRoot
                {...props}
                dispatch={dispatch}
                loginConfig={loginConfig}
                shopConfig={shopConfig}
                selectedShop={selectedShop}
                axiosInstance={axiosInstance}
                {...selectedFilters}
                setRequestType={setRequestType}
                t={t}
            />
        </>
    )
}

class DashboardRoot extends React.Component {
    constructor(props) {
        super(props);

        // defaults
        this.state = {
            firstOrderToggle: true,
            single_product_order : 'enabled',
            breakdown : "acquisition_period",
            format: "value",
            metric: "acc_total_sales_per_customer",
            loading: true,
            response: {},
            alerts: {},
            pageNumber: 1
        };

        this.fetchData = this.fetchData.bind(this);
        this.handleCustomerCohortExport = this.handleCustomerCohortExport.bind(this);
        this.handleFirstOrderToggleChange = this.handleFirstOrderToggleChange.bind(this);
        this.handleSingleProductOrderChange = this.handleSingleProductOrderChange.bind(this);
        this.handleBreakdownChange = this.handleBreakdownChange.bind(this);
        this.handleMetricChange = this.handleMetricChange.bind(this);
        this.setPageNumber = this.setPageNumber.bind(this);
        this.handleFormatChange = this.handleFormatChange.bind(this);
    }

    trackerEvent = (event_str, props = {}) => {
        tracker.event(event_str, props);
    };

    handleFormatChange = (fo) => {
        this.setState({ format: fo });
    };

    handleBreakdownChange = (breakdown) => {

        tracker.mixpanel.track(`Switch Breakdown`, {
            report: "cohort analysis",
            old_value: this.state.breakdown,
            new_value: breakdown
        });

        const {shopConfig, t, preset} = this.props;
        const feat = presets[preset]?.feature_flag;

        let isSubscriptionInActive = shopConfig.subscription_enabled && !(shopConfig.planDetails?.features?.[feat] ?? false)
        if (isSubscriptionInActive && breakdown != "acquisition_period") {
            NiceModal.show(PaywallDialog, {feature :feat})
            this.trackerEvent("Paywall", {feature: feat})
            return false;
        }

        this.setState({breakdown : breakdown}, function () {
            this.fetchData(this.state);
        });
    };

    handleSingleProductOrderChange = (val) => {
        this.setState({ single_product_order: val }, function () {
            this.fetchData(this.state);
        });
    };

    setPageNumber = (page) => {
        if (page < 1) {
            return false
        }

        let {response} = this.state
        if (!response || !response.max_pages) {
            return false
        }

        if (page > response.max_pages) {
            return false
        }

        this.setState({pageNumber : page}, function () {
            this.fetchData(this.state, true, false);
        });
    };

    handleMetricChange = (metric) => {
        tracker.mixpanel.track(`Switch Metric`, {
            report: "cohort analysis",
            old_value: this.state.metric,
            new_value: metric
        });

        // const { updateIntercom } = this.props;
        // if (updateIntercom) {
        //     updateIntercom();
        // }
        this.setState({ metric: metric });
    };

    componentDidUpdate = (prevProps) => {
        // Typical usage (don't forget to compare props):
        let shopChange = prevProps.selectedShop != this.props.selectedShop
        let filterChange = prevProps.start_date.getTime() != this.props.start_date.getTime()
            || prevProps.end_date.getTime() != this.props.end_date.getTime()
            || prevProps.time_frame != this.props.time_frame
            || prevProps.filter_version != this.props.filter_version

        let presetChange = prevProps.preset != this.props.preset

        if (shopChange || filterChange) {
            this.fetchData(this.state);
        }

        if (presetChange) {
            this.setState({breakdown : presets[this.props.preset].breakdowns[0]}, function () {
                this.fetchData(this.state);
            });
        }
    }

    componentDidMount = () => {
        if (this.props.preset) {
            this.setState({breakdown : presets[this.props.preset].breakdowns[0]}, function () {
                this.fetchData(this.state, false);
            });
        } else {
            this.fetchData(this.state, false);
        }
    };

    fetchData = (
        {breakdown, pageNumber},
        should_track = true,
        resetPageNumber = true
    ) => {

        const {start_date, end_date, time_frame, applied_filters, request_type, setRequestType, axiosInstance, preset} = this.props;
        const {metric, single_product_order} = this.state;

        const feat = presets[preset]?.feature_flag;

        var setState = (newState) => {
            return this.setState(newState);
        };

        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to new request.");
        }

        //Save the cancel token for the current request
        cancelToken = axios.CancelToken.source();

        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            time_frame,
            breakdown,
            page_number: resetPageNumber ? 1 : pageNumber,
            frequency_group : 5, // default
            single_product_order : single_product_order,
            applied_filters: applied_filters ?? {},
        };

        if (request_type != "" || breakdown != "acquisition_period") {
            reqData.feature_access = feat;
        }

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, report: "cohort analysis", metric: metric});
        }

        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }

        if (!!request_type) {
            reqData.request_type = request_type;
        }

        setState({ loading: true });
        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/cohorts", reqData, { cancelToken: cancelToken.token })
            .then(function (response) {
                if (resetPageNumber) {
                    setState({ pageNumber: 1 });
                }
                setState({ loading: false, response: response.data });
                setRequestType("");
            })
            .catch((err) => {
                console.log("error", err);
                if (!axios.isCancel(err)) {
                    setState({ loading: false });
                }
            });
    };

    componentWillUnmount = () => {
        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to unmount.");
        }
    }

    handleFirstOrderToggleChange = (event) => {
        this.setState((prevState, props) => {
            return {firstOrderToggle : !prevState.firstOrderToggle}
         });
    };

    getCohortAnalysisExportData = () => {

        const {response, metric, loading, format} = this.state;
        const {heatmap, data, order_count} = response;

        if (loading || order_count === 0) {
            return {headers :[], data :[]}
        }

        if (!heatmap || Object.keys(heatmap).length == 0) {
            return {headers :[], data :[]}
        }

        var { xLabels, yLabels, table } = heatmap;

        let headers = [
            {
                label : "Acquisition Period",
                key: "period"
            },
            {
                label : "Cohort Size",
                key: "new_customers"
            }
        ];

        xLabels.map((x, i) => {
            headers.push({
                label : x,
                key : `ind:${i}`,
            })
        });

        let exportData = [];

        const getDataByReference = (ref) => {
            if (!(ref in data)) {
                return "";
            }

            let format_to_use = format ?? "value";

            if (metric in data[ref] && format_to_use in data[ref][metric]) {
                return data[ref][metric][format_to_use];
            }

            return "0";
        };

        for (var yi in yLabels) {
            let rowObj = {
                period : yLabels[yi],
                new_customers: 'new_customers' in table ? (table.new_customers[yi].val || "0") : "0"
            }

            for (var xi in xLabels) {
                rowObj[`ind:${xi}`] = "0"
            }

            let period_row = heatmap.data[yi] || [];
            for (var pi in period_row) {
                let val = getDataByReference(period_row[pi])
                rowObj[`ind:${pi}`] = val
            }
            
            exportData.push(rowObj);
        }

        return {headers, data: exportData}
    }

    handleCustomerCohortExport = () => {
        const {metric} = this.state;
        const {shopConfig, preset} = this.props;
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        const feat = presets[preset]?.export_flag;
        if (isSubscriptionEnabled) {
            if (!(shopConfig.planDetails?.features?.[feat] ?? false)) {
                NiceModal.show(PaywallDialog, {feature : feat})
                this.trackerEvent("Paywall", {feature: feat, metric: metric})
                return false
            }
        }
        this.trackerEvent("Data Exported", {report: "cohort analysis", metric: metric})
    }

    render() {
        const {
            metric,
            format,
            loading,
            response,
            alerts,
            facebook,
            firstOrderToggle,
            breakdown,
            single_product_order,
            pageNumber
        } = this.state;
        const {loginConfig, t, preset, shopConfig, time_frame} = this.props;

        const {
            is_blur_active,
            heatmap,
            area_chart,
            line_chart,
            data,
            currency,
            order_count,
            trends
        } = response;

        let showConnector = facebook ? (facebook.showConnector ?? false) : false
        showConnector  = showConnector && loginConfig.admin
        const feat = presets[preset]?.feature_flag;
        const export_feat = presets[preset]?.export_flag;
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[feat] ?? false)
        let isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[export_feat] ?? false)

        const getDataByReference = (ref, custom_format) => {
            if (!(ref in data)) {
                return "";
            }

            let format_to_use = custom_format || format;

            if (metric in data[ref] && format_to_use in data[ref][metric]) {
                return data[ref][metric][format_to_use];
            }

            return "";
        };

        const getDataObj = (ref) => {
            if (!(ref in data)) {
                return {};
            }

            return data[ref];
        };

        let formatFilterProps = {
            format,
            use_currency : metrics[metric].format == "currency",
            currency,
            handleFormatChange: this.handleFormatChange,
        };
        let dataChartProps = {
            firstOrderToggle,
            area_chart,
            line_chart,
            order_count,
            metric,
            loading,
            getDataByReference,
            getDataObj,
            format,
            currency,
            time_frame,
            t,
            breakdown
        };
        let dataHeatmapProps = {
            heatmap,
            order_count,
            is_blur_active,
            metric,
            getDataByReference,
            getDataObj,
            loading,
            format,
            time_frame,
            breakdown
        };

        let exportData = {headers :[], data :[]}
        try {
            exportData = this.getCohortAnalysisExportData()
        } catch(err) {
            console.log(err)
        }

        let cohortExportButton = (
            <CSVLink
                data={exportData.data}
                headers={exportData.headers}
                filename={"Customer Cohorts.csv"}
                onClick={this.handleCustomerCohortExport}
            >
                <MDBox>
                    <MDButton variant="outlined" color="dark" size="small">
                        <Icon>description</Icon>
                        &nbsp;{t("export-csv")}
                        {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                    </MDButton>
                </MDBox>
            </CSVLink>
        );

        let cohortAnalysisProps = {
            metric,
            cohortExportButton,
            dataHeatmapProps,
            formatFilterProps,
            pageNumber,
            maxPages : response && response.max_pages ? response.max_pages : 1,
            setPageNumber : this.setPageNumber
        }

        let metricOptions = [];
        for (let key in metrics) {
            metricOptions.push({
                label : t(metrics[key].label),
                value : key
            })
        }

        let breakdownOptions = []
        if (preset && preset in presets) {
            presets[preset].breakdowns.map((b) => {
                breakdownOptions.push({
                    label : t(b),
                    isPremiumOption : isSubscriptionInActive,
                    value : b
                })
            })
        }

        let singleProductOrderOptions = [
            {
                label : t("enabled"),
                value : 'enabled'
            },
            {
                label : t("disabled"),
                value : 'disabled'
            }
        ]

        const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        let tooltip_title = t("p-date-block-title-3-m");
        if (time_limit === "1_year") {
            tooltip_title = t("p-date-block-title-1-yr")
        }

        return (
            <>
                <MDBox mb={3} mt={2}>
                    <Card elevation={0} mb={4} my={4}>
                        <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" pl={2} pb={2}>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}} mr={0.5}>
                                    {t("acquisition-period")}  &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <DatePickerAnt report="cohort analysis"/>
                        </Grid>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("group-by")}</MDTypography>
                            <RelativeGroupingFilter report="cohort analysis" exclude={["day"]} />
                        </Grid>
                        {breakdownOptions.length > 1 && <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("breakdown-by")}</MDTypography>
                            <PillBar
                                isDropdown={true}
                                options={breakdownOptions}
                                value={breakdown}
                                onChange={this.handleBreakdownChange}
                            />
                        </Grid>}
                        {preset == "product-cohorts" && <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>
                                {t("single-product-order")} &nbsp;{<MDTooltip title={t("single-product-orders-tooltip")}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <PillBar
                                options={singleProductOrderOptions}
                                value={single_product_order}
                                onChange={this.handleSingleProductOrderChange}
                            />
                        </Grid>}
                        <Divider variant="fullWidth" sx={{width: "100%"}} />
                        <Grid item sx={{paddingTop: "0px !important"}}>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("select-metric")}</MDTypography>
                            <PillBar
                                isDropdown={true}
                                options={metricOptions}
                                value={metric}
                                onChange={this.handleMetricChange}
                            />
                        </Grid>
                        <Grid item xs={3} sx={{paddingTop: "0px !important"}} pr={2}>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>
                                {t("refine-cohorts")} &nbsp;{<MDTooltip title={t("filters-tooltip")}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <FilterDrawer feature={"cohort_filters"} />
                        </Grid>
                        </Grid>
                    </Card>
                </MDBox>

                {alerts && alerts.book_call && (<BookCallCard selectedShop={this.props.selectedShop}/>)}
                {alerts && alerts.review && (<WriteReviewCard selectedShop={this.props.selectedShop} />)}
                {showConnector && <MDBox ><ConnectionCard  /></MDBox>}

                <Grid container spacing={3}>
                    {breakdown == "acquisition_period" && <Grid item xs={12} md={12}>
                        <LTVTrend trends={trends} loading={loading} />
                    </Grid>}
                    {metric in metrics && (<Grid item xs={12} md={12}>
                        <Element name="section-cohort-analysis">
                            <CohortAnalysis {...cohortAnalysisProps} />
                        </Element>
                    </Grid>)}
                    {metric in metrics && (<Grid item xs={12}>
                        <Element name="section-retention-charts">
                        <Card sx={{ width: "100%" }}>
                        <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={1} px={2} width={"100%"}>
                            <MDBox display="flex">
                            </MDBox>
                            <MDBox display="flex" direction="row" alignItems="center">
                                <MDTypography variant="button" textTransform="capitalize" sx={{fontSize:"13px"}}>
                                    {t("show-first-order")}
                                </MDTypography>
                                <Switch checked={!firstOrderToggle} onChange={this.handleFirstOrderToggleChange} />
                            </MDBox>
                            </MDBox>
                            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
                            <MDBox p={2} pb={4}>
                                <FeatureBlurBox feature={feat} >
                                    <DataChart {...dataChartProps} />
                                </FeatureBlurBox>
                            </MDBox>
                        </Card>
                        </Element>
                    </Grid>)}
                </Grid>
                <ReviewBar subject={metric}/>
            </>
        );
    }
}
