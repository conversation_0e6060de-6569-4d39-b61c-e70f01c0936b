import React, { useMemo } from "react";
import {Link} from "react-router-dom";
import { tracker, useMaterialUIController} from "@/context";
import CategoriesList from "@/examples/Lists/CategoriesList";
import {useTranslation} from "react-i18next";

// Material Dashboard 2 PRO React components
import Divider from "@mui/material/Divider";
import logoShopify from "@/assets/images/shopify-logo.svg";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import Grid from "@mui/material/Grid";
import DefaultStatisticsCard from "@/examples/Cards/StatisticsCards/DefaultStatisticsCard";
import MDTooltip from "@/components/MDTooltip";
import Skeleton from "@mui/material/Skeleton";

export function ExploreByUsecase(props) {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, selectedFilters, loginConfig} = controller;
  const {t} = useTranslation();

  let exploreData = [
    {
      color: "warning",
      mdi_icon : "mdi mdi-home-analytics",
      name: <>{t("p-benchmarks")}</>,
      description: (
        <>
          <MDTypography variant="caption" color="text" fontWeight="regular">
            {t("benchmark-intro-2")}
          </MDTypography>
        </>
      ),
      route: "/benchmarks",
    },
    {
      color: "primary",
      mdi_icon : "mdi mdi-cart-heart",
      name: t("product-repurchase-name-1"),
      description: (
        <>
          <MDTypography variant="caption" color="text" fontWeight="regular">
            {t("product-repurchase-desc-1")}
          </MDTypography>
        </>
      ),
      route: "/product/repurchase-rate",
    },
    {
      color: "error",
      mdi_icon : "mdi mdi-account-clock-outline",
      name: t("rfm-name-1"),
      description: (
        <>
          <MDTypography variant="caption" color="text" fontWeight="regular">
            {t("rfm-desc-1")}
          </MDTypography>
        </>
      ),
      route: "/customer/rfm-segments",
    },
    {
      color: "success",
      mdi_icon: "mdi mdi-compare",
      name: t("cust-segment-name-1"),
      description: (
        <>
          <MDTypography variant="caption" color="text" fontWeight="regular">
            {t("cust-segment-desc-1")}
          </MDTypography>
        </>
      ),
      route: "/customer/new-vs-returning-customers",
    },
    {
      color: "info",
      mdi_icon : "mdi mdi-cash-multiple",
      name: t("cust-quartile-name-1"),
      description: (
        <MDTypography variant="caption" color="text" fontWeight="regular">
            {t("cust-quartile-desc-1")}
        </MDTypography>
      ),
      route: "/customer/customer-segments",
    },
  ];

  return (
    <CategoriesList
      title={t("explore-by-usecase")}
      categories={exploreData}/>
  );
}


export function LTVSummary(props) {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, selectedFilters, loginConfig} = controller;
  const {t} = useTranslation();

  if (!props.summary || props.summary.length == 0) {
    return null;
  }

  return (
      <Grid container spacing={1.2} p={1.6} pb={2.4} key="summary">
        {props.summary.map((a, index) => {

          let mTitle = (
            <MDTooltip placement="top" title={a.name} description={a.tooltip} logo={logoShopify}>
              <MDBox style={{borderBottom: "1px dotted #ccc", cursor: "default"}} display="flex" alignItems="center" p={0}>
                <MDBox component="img" src={logoShopify} alt="shopify" width="0.9rem" mx={0.1}/>
                <MDTypography component="span" fontWeight="regular" ml={0.4} fontSize="13.5px" color="dark" textTransform="capitalize"
                  variant="button" >
                    {a.name}
                </MDTypography>
              </MDBox>
            </MDTooltip>
          )

          let mCount = 
            <MDTypography variant="h4" fontWeight="regular" color="dark" my={0.4}>
              {a.value}
            </MDTypography>
          if (!!a.value_description) {
            mCount = <MDTooltip title={a.value_description} placement="top">
              <MDTypography variant="h4" fontWeight="regular" color="text" my={0.4}>
                {a.value}
              </MDTypography>
            </MDTooltip>
          }

          if (props.loading) {
            mCount = <Skeleton variant="text" width={100} />
          }

          return (
            <Grid item xs={12} md={6} lg={3} key={index}>
              <DefaultStatisticsCard
                key={a.key}
                isEmbedded={true}
                title={mTitle}
                count={mCount}
                diffLabel={null}
                vsLabel={null}
            />
            </Grid>
          )
        })}
      </Grid>
  );
}

export default function LTVTrend(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig} = controller;
    const {t} = useTranslation();

    const {reportLink, timePeriodDisplay} = props;


    let defaultSummary = {
      'aov' : {
          name : t("aov"),
          tooltip : t("ltv-trend-tip-p1"),
          value : "-"
      },
      'customer_count' : {
          name : t("new-cust"),
          tooltip : t("new-cust-tootltip"),
          value : "-"
      },
      'ltv1m' :  {
          name : t("ltvxm", {months: 1}),
          tooltip : t("ltv-trend-tip-p3", {months : 1}),
          value : "-"
      },  
      'ltv3m' :  {
          name : t("ltvxm", {months: 3}),
          tooltip : t("ltv-trend-tip-p3", {months : 3}),
          value : "-"
      },
      'ltv6m' :  {
          name : t("ltvxm", {months: 6}),
          tooltip : t("ltv-trend-tip-p3", {months : 6}),
          value : "-"
      },
      'ltv1y' :  {
          name : t("ltvxm", {months: 12}),
          tooltip : t("ltv-trend-tip-p3", {months : 12}),
          value : "-"
      },
      'ltv2y' :  {
          name : t("ltvxm", {months: 24}),
          tooltip : t("ltv-trend-tip-p3", {months : 24}),
          value : "-"
      },
      'ltv3y' :  {
          name : t("ltvxm", {months: 36}),
          tooltip : t("ltv-trend-tip-p3", {months : 36}),
          value : "-"
      }
    };


    let summary = useMemo(() => {

      if (!props.trends || props.trends.length == 0) {
        return Object.values(defaultSummary);
      }

      for (var k in defaultSummary) {
        let trend = props.trends.find(t => t.key == k);
        if (!trend) {
          continue;
        }

        if (trend.value == 0) {
          defaultSummary[k].value = "-";
          defaultSummary[k].value_description = t("ltv-trend-insufficient-data");
          continue;
        }

        defaultSummary[k].value = trend.value_display;
      }

      return Object.values(defaultSummary);
    }, [t, props.trends]);


    return (
      <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
          <Grid item key="title">
            <MDTooltip title={t("ltv-trend-tip")} placement="right">
              <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                {t("ltv-trend-title")}
              </MDTypography>
            </MDTooltip>
          </Grid>
          <Grid item key="action">
            <MDBox display="flex" direction="row" alignItems="center">
              <MDBox> 
              <MDButton mr={1} variant="outlined" color="secondary" component={Link} to="/benchmarks" sx={{fontSize:"12px", p:"5px !important"}} size="small">
                  <Icon fontSize="small">leaderboard</Icon>&nbsp;
                  {t("benchmarks")}&nbsp;
                  <Icon fontSize="small">arrow_outward</Icon>&nbsp;
              </MDButton>
              </MDBox>
            </MDBox>
          </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <LTVSummary summary={summary} loading={props.loading}/>
        {reportLink && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
        {reportLink && <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="space-between" alignItems="center" sx={{width :"100%"}}>
            <MDTooltip title={t("ltv-trend-tip")} placement="right">
                <MDTypography variant={"button"} color={"secondary"} fontWeight="regular" verticalAlign="middle" alignItems="center" style={{borderBottom: "1px dotted #ccc", cursor: "pointer"}}>
                  {t("time-period") + " : " + timePeriodDisplay}
                </MDTypography>
            </MDTooltip>
            <MDButton variant="outlined" color={"dark"} component={Link} to={reportLink} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                    {t("deepdive-btn")} &nbsp;
                    <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                </MDTypography>
            </MDButton>
        </MDBox>}
      </Card>
    );
}