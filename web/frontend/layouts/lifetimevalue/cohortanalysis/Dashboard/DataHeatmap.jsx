import React, {useState, useMemo} from "react";
import HeatMap from "@/components/Heatmap";
import { relativeGroups } from "@/components/Filters/TimeFrameFilter";
import Skeleton from "@mui/material/Skeleton";
import MDTooltip from "@/components/MDTooltip";
import Empty from "@/components/EmptyChart";
import { metrics } from "@/components/Filters/MetricSelector";

import { useTranslation } from "react-i18next";

import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { useMaterialUIController, tracker } from "@/context";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";


const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: "white",
        padding: 0,
        margin: 0,
        // color: 'rgba(0, 0, 0, 0.87)',
        // boxShadow: theme.shadows[1],
        fontSize: 12,
    },
    [`& .${tooltipClasses.arrow}`]: {
        color: "inherit",
    },
  }));

function SkeletonDataTable() {
    var row = (ind) => (
        <tr>
            {new Array(ind).fill(0).map((a, ai) => (
                <td key={ai} style={{ padding: "3px 0" }}>
                    <Skeleton variant="rectangular" height={35} width={100} sx={{borderRadius:"8px"}} animation="wave" />
                </td>
            ))}
        </tr>
    );
    return (
        <table style={{ width: "100%", borderSpacing: "8px" }}>
            <tbody>
                {row(8)}
                {row(7)}
                {row(6)}
                {row(5)}
                {row(3)}
            </tbody>
        </table>
    );
}

function getCellToolTip(
    metric,
    cohort_name,
    cohort_size,
    display_value,
    display_percentage,
    display_cumulative_total,
    tf_name,
    index,
    t
) {

    if (metric in metrics) {
        let info = metrics[metric].tooltip(
            display_value,
            display_percentage,
            display_cumulative_total,
            tf_name,
            index,
            t
        );
        if (info) {
            return (
                <Card elevation={10}>
                    <MDBox p={1} variant="gradient" px={2.5} display="flex" flexDirection="column" alignItems="flex-start">
                        <MDTypography variant="button" fontWeight="regular" ml={0.4} fontSize="13px" mb={1.5} >
                            {info}
                        </MDTypography>
                        {cohort_name && (
                            <MDTypography variant="caption" color="secondary" fontWeight="regular" fontSize="inherit">
                                <span dangerouslySetInnerHTML={{__html: t("cohort-analysis-cohort", {cohort_size, cohort_name})}} />
                                <br/>
                            </MDTypography>
                        )}
                    </MDBox>
                </Card>
            );
        }
    }

    return null;
}

export default function DataHeatmap(props) {
    const {t} = useTranslation();

    const [controller, dispatch] = useMaterialUIController();
    const {shopConfig} = controller;
    const [showTable, setShowTable] = useState(false);
    const {
        getDataByReference,
        getDataObj,
        breakdown,
        loading,
        heatmap,
        format,
        order_count,
        is_blur_active,
        time_frame,
        metric,
        source_config_id,
        shortView
    } = props;

    let isBlurActive = !!is_blur_active;
    let isTimeCohorts = breakdown == "acquisition_period";

    let blurBlockContent = !isBlurActive ? null : (
        <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            position="absolute"
            width="80%"
            top="0"
            left="20%"
            right="0"
            maxHeight="50px"
            bottom="30%"
            margin="auto"
            sx={{
                backgroundColor: "transparent",
                zIndex: 2
            }}
        >
            <MDButton variant="contained" color="info" size="small" onClick={() => {
                // Not ideal but it's how it is - LTV cohorts - we show for 6 months with 3 months blurred out
                tracker.event("Paywall", {feature: isTimeCohorts ? "data_time_limit" : "product_cohorts"});
                NiceModal.show(PaywallDialog, {feature: isTimeCohorts ? "data_time_limit" : "product_cohorts"})
            }}>
                {t("start-trial")}
                <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={1} />
            </MDButton>
        </MDBox>
    )

    if (loading) {
        return <MDBox style={{overflowX: "auto"}}><SkeletonDataTable /></MDBox>;
    }

    if (order_count === 0) {
        return (
            <Empty
                description={
                    <p>
                        {t("no-orders-found")}
                    </p>
                }
            />
        );
    }

    if (!heatmap || Object.keys(heatmap).length == 0) {
        return (
            <Empty
                description={
                    <p>
                        {t("something-went-wrong")}
                    </p>
                }
            />
        );
    }

    var { xLabels, yLabels, data, table, table_sum, weighted } = heatmap;

    yLabels = yLabels.map((x) => {

        let renderX = x;
        if (x.length > 22) {
            renderX = x.slice(0, 22) + "..";
        }

        let renderColor = "#475666";
        let renderTitle = x
        if (x == "<MISSING>") {
            renderX = t("missing-or-empty");
            renderColor = "#f44336";
            renderTitle = t("missing-data-tooltip")
        }

        return <MDTooltip placement="right" title={renderTitle}>
                <div style={{ color: renderColor }}>{renderX}</div>
        </MDTooltip>
    });

    xLabels = xLabels.map((x, ind) => {
        let labelNode = (
            <div style={{ fontWeight: "500", color: "#475666" }}>{
                ind == 0 ? x : `${relativeGroups[time_frame].full} ${x}`
            }</div>
        );

        let tooltipNode = null;
        if (x == "0") {
            labelNode = (
                <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                    <u>{ind == 0 ? x : `${relativeGroups[time_frame].full} ${x}`}</u>
                </div>
            );
            tooltipNode = (
                <div className="custom-ant-tooltip-basic">
                    {t("first-tf-tooltip", {tf_full : relativeGroups[time_frame].full, time_frame: time_frame})}
                </div>
            );
        }

        if (tooltipNode) {
            return (
                <MDTooltip placement="right" title={tooltipNode}>
                    {labelNode}
                </MDTooltip>
            );
        }

        return labelNode;
    });

    var data_values = data.map((row) =>
        row.map((ref) => getDataByReference(ref))
    );

    function customCellRender(value, x, y) {
        var ref = data[y][x];
        if (!ref) {
            return null;
        }

        var display = getDataByReference(ref, `display_${format}`);
        let refData = getDataObj(ref);
        let cohort_name =
            !!refData && "cohort_name" in refData ? refData.cohort_name : null;
        let bucket_index =
            !!refData && "bucket_index" in refData ? refData.bucket_index : null;
        let cohort_size =
            !!refData && "cohort_size" in refData ? refData.cohort_size : null;
        let is_blur =
            !!refData && "is_blur" in refData ? refData.is_blur : false;

        if (is_blur)  {
            return (
                <HtmlTooltip arrow={true} placement="right"  title={
                    <Card elevation={10}>
                        <MDBox p={1} variant="gradient" px={2.5} display="flex" flexDirection="column" alignItems="flex-start">
                            <MDTypography variant="button" fontWeight="regular" ml={0.4} fontSize="13px" >
                                {t("upgrade-to-see")}
                            </MDTypography>
                        </MDBox>
                    </Card>
                }>
                    <div>{display}</div>
                </HtmlTooltip>
            );
        }

        var display_value = getDataByReference(ref, `display_value`);
        var display_percentage = getDataByReference(ref, `display_percentage`);
        var display_cumulative_total = getDataByReference(ref, `display_cumulative_total`);
        let tooltipNode = getCellToolTip(
            metric,
            cohort_name,
            cohort_size,
            display_value,
            display_percentage,
            display_cumulative_total,
            time_frame,
            bucket_index,
            t
        );

        return (
            <HtmlTooltip arrow={true} placement="right"  title={tooltipNode}>
                <div>{display}</div>
            </HtmlTooltip>
        )
    }

    var prependXLabels = [];
    var prependTable = [];

    var appendXLabels = [];
    var appendTable = [];

    var totalRow = [];
    let xLabelsName = shortView ? "" : t("time-first-order", {tf_full : relativeGroups[time_frame].full})

    let xLabelsBottom = [];
    if (
        !!weighted &&
        Object.keys(weighted).length != 0 &&
        metric == "acc_total_sales_per_customer" &&
        isTimeCohorts
    ) {
        for (var bk in weighted) {
            let format_to_use = `display_${format}`;
            if (format_to_use in weighted[bk]) {
                xLabelsBottom.push(weighted[bk][format_to_use]);
            } else {
                xLabelsBottom.push("");
            }
        }
    }

    let shouldDisplayCAC = false && !!table_sum && Object.keys(table_sum).length > 0 && ('total_adspend' in table_sum) && source_config_id != ''
    shouldDisplayCAC = shouldDisplayCAC && metric == 'acc_total_sales_per_customer'

    if (!!table && Object.keys(table).length > 0) {
        let newCustomersNodeTooltip = (
            <div className="custom-ant-tooltip-basic">
                {isTimeCohorts ? t("acq-cust-time", {time_frame}) : t("acq-cust-cohort")}
            </div>
        );

        let newCustomersNode = (
            <MDTooltip placement="right" title={newCustomersNodeTooltip}>
                <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                    <u>{t("cohort-size")}</u>
                </div>
            </MDTooltip>
        );

        let repeatNodeTooltip = (
            <div className="custom-ant-tooltip-basic">
                {t("repeat-cust-tooltip")}
            </div>
        );

        let repeatNode = (
            <MDTooltip placement="right" title={repeatNodeTooltip}>
                <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                    <u>{t("repeat-perc")}</u>
                </div>
            </MDTooltip>
        );

        prependXLabels.push(newCustomersNode);
        prependTable.push(table.new_customers);

        if (!shortView) {
            prependXLabels.push(repeatNode);
            prependTable.push(table.repeated_percentage);
        }

        appendXLabels.push(t("cohort-orders"));
        appendTable.push(table.total_orders);
        if (!shortView) {
            appendXLabels.push(t("cohort-revenue"));
            appendTable.push(table.total_revenue);
        }

        if (shouldDisplayCAC) { 
            appendXLabels.push("CAC");
            appendTable.push(table.total_adspend);
        }
    }

    if (!!table_sum && Object.keys(table_sum).length > 0) {
        totalRow.push(table_sum.new_customers);
        if (!shortView) {
            totalRow.push(table_sum.repeated_percentage);
        }
        totalRow.push(table_sum.total_orders);
        if (!shortView) {
            totalRow.push(table_sum.total_revenue);
        }
        if (shouldDisplayCAC) {
            totalRow.push(table_sum.total_adspend);
        }
        xLabels.map((x) => {
            totalRow.push("");
        });
    }

    let originNodeTooltip = (
        isTimeCohorts ? 
            <div className="custom-ant-tooltip-basic">{t("acquired-tooltip", {time_frame})}</div>
            : <div className="custom-ant-tooltip-basic">{t("first-order-tooltip", {breakdown : t(breakdown)})}</div>
    );

    let originNode = (
        <MDTooltip placement="right" title={originNodeTooltip}>
            <div style={{ fontWeight: "500", color: "#475666", cursor: "pointer" }}>
                <u>{isTimeCohorts ? t("acquired-in") : t("first-order-breakdown", {breakdown : t(breakdown)})}</u>
            </div>
        </MDTooltip>
    );

    let is_cumulative_metric = metrics[metric].isCumulative ?? false;
    const flatArray = data_values.map(r => r.filter((e, i) => i != 0)).reduce((i, o) => [...o, ...i], []);
    const minNew = Math.min(...flatArray);
    const maxNew = Math.max(...flatArray);

    return (
        <MDBox sx={{position: "relative"}}>
        <HeatMap
            yLabelTextAlign={"center"}
            xLabelsName={xLabelsName}
            xLabelsBottom={xLabelsBottom}
            totalRow={isTimeCohorts ? totalRow : []}
            prependXLabels={prependXLabels}
            prependTable={prependTable}
            appendXLabels={appendXLabels}
            appendTable={appendTable}
            xLabels={xLabels}
            originNode={originNode}
            yLabels={yLabels}
            xLabelsLocation={"top"}
            yLabelWidth={isTimeCohorts ? 100 : 180}
            data={data_values}
            height={38}
            title={() => { }}
            background="#388257"
            onClick={() => { }}
            cellStyle={(bg, value, min, max, data_val, x, y) => {
                var ref = data[y][x];
                var is_blur = false;
                if (!!ref) {
                    let refData = getDataObj(ref);
                    is_blur = !!refData && "is_blur" in refData ? refData.is_blur : false;
                }
                if (is_blur) {
                    return {
                        background : !is_cumulative_metric && x == 0 ? "#fff" : (metric == "acc_order_count_per_customer" ? "rgb(244, 67, 53, 0.8)" : "rgb(52, 71, 103, 0.8)"),
                        fontSize: "12px",
                        fontWeight: "500",
                        filter: "blur(5px)",
                        userSelect: "none",
                        color : !is_cumulative_metric && x == 0 ? "#475666" : "#ffffff",
                        cursor : "default",
                        visibility : "visible",
                    }
                }

                let val_weight = 1.1;
                if (max != min) {
                    val_weight = 1.1 - (max - value) / (max - min);
                }

                if (!is_cumulative_metric) {
                    // metrics like customers and revenue
                    val_weight = 1.1;
                    if (maxNew != minNew) {
                        val_weight = 1.1 - (maxNew - value) / (maxNew - minNew);
                    }

                    if (x == 0) {
                        // first column
                        val_weight = 0.1
                    }
                }

                let color = val_weight < 0.4 ? "#475666" : "#ffffff";

                let cursor = "pointer";
                let visibility = "visible";
                let background = `rgb(52, 71, 103, ${val_weight})`;
                if (metric == "acc_order_count_per_customer") {
                    background = `rgb(244, 67, 53, ${val_weight})`;
                }

                if (!is_cumulative_metric && x == 0) {
                    return {
                        background: "#fff",
                        border : "1px solid rgba(0, 0, 0, 0.05)",
                        color : "#475666",
                        fontSize: "12px",
                        fontWeight: "500",
                        cursor,
                    }
                }

                return {
                    background,
                    fontSize: "12px",
                    fontWeight: "500",
                    color,
                    cursor,
                    visibility,
                };
            }}
            cursor="pointer"
            cellRender={customCellRender}
        />
        {blurBlockContent}
        </MDBox>
    );
}
