
// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import Dashboard from "./Dashboard";

const ORDERED_SECTIONS = [
  "section-cohort-analysis",
  "section-retention-charts"
]

function CohortAnalysisIndex(props) {

  return (
    <DashboardLayout>
      <DashboardNavbar sections={ORDERED_SECTIONS} />
      <Dashboard {...props} />
      <Footer />
    </DashboardLayout>
  );
}

export default CohortAnalysisIndex;
