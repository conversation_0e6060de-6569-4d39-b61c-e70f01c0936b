import React from "react";
// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDBadge from "@/components/MDBadge";
import DataTable from "@/examples/Tables/DataTable";
import { useTranslation } from 'react-i18next';
import Chart from "@/layouts/pages/widgets/components/Chart";
import MDTooltip from "@/components/MDTooltip";
import Icon from "@mui/material/Icon";
import Divider from "@mui/material/Divider";

const GrowthChart = ({results, isSubscriptionInActive, isEmbedded}) => {
    const {t} = useTranslation();

    if (!results) {
        return null
    }

    let revenueGrowth = [0,0,0,0,0,0,0,0,0,0,0,0]
    let newCustomerGrowth = [0,0,0,0,0,0,0,0,0,0,0,0]
    let orderGrowth = [0,0,0,0,0,0,0,0,0,0,0,0]
    let growthMonth = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].reverse()

    if ('revenueGrowth12m' in results.shop) {
        revenueGrowth = results.shop.revenueGrowth12m.split(",").map((x) => parseFloat(x));
    }

    if ('newCustomerGrowth12m' in results.shop) {
        newCustomerGrowth = results.shop.newCustomerGrowth12m.split(",").map((x) => parseFloat(x));
    }

    if ('orderGrowth12m' in results.shop) {
        orderGrowth = results.shop.orderGrowth12m.split(",").map((x) => parseFloat(x));
    }

    if ('growthMonth12m' in results.shop) {
        growthMonth = results.shop.growthMonth12m.split(",");
    }

    revenueGrowth = revenueGrowth.reverse()
    newCustomerGrowth = newCustomerGrowth.reverse()
    orderGrowth = orderGrowth.reverse()
    growthMonth = growthMonth.reverse()

    let growthCharts = React.useMemo(() => {

        let revenue_chart = <Chart chart={{
            labels: growthMonth,
            customOptions : {valFormatter : (val) => `${val}%`},
            datasets: [{label: t("revenue-growth-title"), color: "dark", data: revenueGrowth}]
        }} />;

        let new_customer_chart = <Chart chart={{
            labels: growthMonth,
            customOptions : {valFormatter : (val) => `${val}%`},
            datasets: [{label: t("new-customer-growth-title"), color: "dark", data: newCustomerGrowth}]
        }} />;


        let order_chart = <Chart chart={{
            labels: growthMonth,
            customOptions : {valFormatter : (val) => `${val}%`},
            datasets: [{label: t("order-growth-title"), color: "dark", data: orderGrowth}]
        }} />;

        return [revenue_chart, new_customer_chart, order_chart]
    }, [results])

    const growthTableData = {
        columns: [
          { Header: t("metric"), accessor: "metric" },
          { Header: t("your-trend"), accessor: "trend" },
          { Header: t("you"), accessor: "shop" },
          { Header: <MDTypography variant="caption" fontWeight="medium" color="success" textTransform="capitalize">{t("top-25")}</MDTypography>, accessor: "industry_p75", className: "center-aln" },
          { Header: <MDTypography variant="caption" fontWeight="medium" color="warning" textTransform="capitalize">{t("avg")}</MDTypography>, accessor: "industry", className: "center-aln"},
          { Header: <MDTypography variant="caption" fontWeight="medium" color="error" textTransform="capitalize">{t("bottom-25")}</MDTypography>, accessor: "industry_p25", className: "center-aln"},
        ],
        rows: [
          {
            metric: t("revenue-growth-title"),
            metricTooltip : "revenue-growth-tooltip",
            trend: growthCharts[0],
            shop: Math.round(parseFloat(results.shop.revenueGrowthLatest ?? 0)),
            industry: Math.round(parseFloat(results.industry.revenueGrowthLatest ?? 0)),
            industry_p25: Math.round(parseFloat(results.industry.revenueGrowthLatestP25 ?? 0)),
            industry_p75: Math.round(parseFloat(results.industry.revenueGrowthLatestP75 ?? 0)),
          },
          {
            metric: t("new-customer-growth-title"),
            metricTooltip : "new-customer-growth-tooltip",
            trend: growthCharts[1],
            shop: Math.round(parseFloat(results.shop.newCustomerGrowthLatest ?? 0)),
            industry: Math.round(parseFloat(results.industry.newCustomerGrowthLatest ?? 0)),
            industry_p25: Math.round(parseFloat(results.industry.newCustomerGrowthLatestP25 ?? 0)),
            industry_p75: Math.round(parseFloat(results.industry.newCustomerGrowthLatestP75 ?? 0)),
          },
          {
            metric: t("order-growth-title"),
            metricTooltip : "order-growth-tooltip",
            trend: growthCharts[2],
            shop: Math.round(parseFloat(results.shop.orderGrowthLatest ?? 0)),
            industry: Math.round(parseFloat(results.industry.orderGrowthLatest ?? 0)),
            industry_p25: Math.round(parseFloat(results.industry.orderGrowthLatestP25 ?? 0)),
            industry_p75: Math.round(parseFloat(results.industry.orderGrowthLatestP75 ?? 0)),
          }
        ],
    }

    for (var k in growthTableData.rows) {
        let row = growthTableData.rows[k]
        row.shop_color = row.shop <= row.industry_p25 ? "error" : (row.shop >= row.industry_p75 ? "success" : "warning")
        row.tooltip_text = row.shop <= row.industry_p25 ? t("b-tooltip-bottom-25-metric") : (row.shop >= row.industry_p75 ? t("b-tooltip-top-25-metric") : t("b-tooltip-avg-metric"))
        row.metric = (
            <MDBox>
                <MDTooltip title={t(row.metricTooltip)} >
                    <MDTypography variant="button" fontWeight="medium" color="text" style={{borderBottom: "1px dotted #ccc", cursor: "default"}}>
                        {row.metric}
                    </MDTypography>
                </MDTooltip>
                <br/>
                <MDTypography variant="button" fontWeight="regular" color="text">
                {t("last-m")} : {results.shop.growthMonthLatest ?? ""}
                </MDTypography>
            </MDBox>
        )
        for (var j in row) {
            let val = row[j]
            if (j == "metric" || j == "trend") {
                continue;
            }

            row[j] = (
                <MDBadge
                    variant="contained"
                    color={val >= 0 ? "success" : "error"}
                    badgeContent={`${val >= 0 ? "+": "-"}${Math.abs(val)}%`}
                    container />
            )

            if (j != "shop") {
                row[j] = (
                    <MDTypography variant="button" fontWeight="regular" textTransform="capitalize">
                        {j == "industry_p25" ? "< " : (j == "industry_p75" ? "> " : "")}{`${val >= 0 ? "": "-"}${Math.abs(val)}%`}
                    </MDTypography>
                )
            } else {
                row[j] = (
                    <MDTooltip title={<span dangerouslySetInnerHTML={{__html: row.tooltip_text ?? ""}} /> } placement="top">
                    <MDBadge
                        variant="contained"
                        color={row.shop_color}
                        badgeContent={`${val >= 0 ? "+": "-"}${Math.abs(val)}%`}
                        style={{cursor: "default"}}
                        container />
                    </MDTooltip>
                )
            }
        }
        growthTableData.rows[k] = row
    }

    if (isEmbedded) {
        return (
            <Grid container spacing={1.2} px={1.6} className={isSubscriptionInActive ? "blurBlock" : ""}>
                <Grid className="no-scrollbar" item xs={12} md={12} key={"benchmark-growth-title"} sx={{borderRadius:0, pb: 1}}>
                    <DataTable
                        table={growthTableData}
                        entriesPerPage={false}
                        showTotalEntries={false}
                        isSorted={false}
                        noEndBorder={true}
                    />
                </Grid>
            </Grid>
        )
    }

    return (
        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="flex-start" alignItems="flex-end">
          <Grid item display="flex" justifyContent="flex-start" alignItems="center">
            <MDTypography variant="h5" color="dark" fontWeight="regular" mr={1} className="card-title-default">
                {t("benchmark-growth-title")}
            </MDTypography>
            <MDTooltip title={t("benchmark-growth-desc")} placement="right">
                <Icon color="secondary" fontSize="small">info_outlined</Icon>
            </MDTooltip>
          </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <Grid container spacing={1.2} px={1.6} className={isSubscriptionInActive ? "blurBlock" : ""}>
            <Grid className="no-scrollbar" item xs={12} md={12} key={"benchmark-growth-title"} sx={{borderRadius:0, pb: 1}}>
                <DataTable
                    table={growthTableData}
                    entriesPerPage={false}
                    showTotalEntries={false}
                    isSorted={false}
                    noEndBorder={true}
                />
            </Grid>
        </Grid>
        </Card>
    );
}

export default GrowthChart;