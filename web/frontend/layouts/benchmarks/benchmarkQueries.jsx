
export const shopResults = {
    "measures": [
      "BenchmarkShops.primaryCurrency",
      "BenchmarkShops.shopCurrency",
      "BenchmarkShops.totalCustomers",
      "BenchmarkShops.totalOrders",
      "BenchmarkShops.oneTimers",
      "BenchmarkShops.twoTimers",
      "BenchmarkShops.threeTimers",
      "BenchmarkShops.fourToFiveTimers",
      "BenchmarkShops.fivePlusTimers",
      "BenchmarkShops.ltv1mLocal",
      "BenchmarkShops.ltv3mLocal",
      "BenchmarkShops.ltv6mLocal",
      "BenchmarkShops.ltv1yLocal",
      "BenchmarkShops.return1m",
      "BenchmarkShops.return3m",
      "BenchmarkShops.return6m",
      "BenchmarkShops.return1y",
      "BenchmarkShops.arpuLocal",
      "BenchmarkShops.aovLocal",
      "BenchmarkShops.totalRevenueLocal",
      "BenchmarkShops.revenueGrowth12m",
      "BenchmarkShops.newCustomerGrowth12m",
      "BenchmarkShops.orderGrowth12m",
      "BenchmarkShops.growthMonth12m",
      "BenchmarkShops.revenueGrowthLatest",
      "BenchmarkShops.newCustomerGrowthLatest",
      "BenchmarkShops.orderGrowthLatest",
      "BenchmarkShops.growthMonthLatest",
      "BenchmarkShops.championPercentage",
      "BenchmarkShops.loyalPercentage",
      "BenchmarkShops.promisingPercentage",
      "BenchmarkShops.newCustomersPercentage",
      "BenchmarkShops.needAttentionPercentage",
      "BenchmarkShops.shouldNotLoosePercentage",
      "BenchmarkShops.sleepersPercentage",
      "BenchmarkShops.lostPercentage",
    ]
}

export const industryResultsQuery = (industry, currency) => {
  return {
    "filters" : [
      {
        "member": "BenchmarkIndustry.onboardIndustry",
        "operator": "equals",
        "values": [industry]
      },
      {
        "member": "BenchmarkIndustry.shopCurrency",
        "operator": "equals",
        "values": [currency]
      },
    ],
    "measures": [
      "BenchmarkIndustry.shopCount",
      "BenchmarkIndustry.totalOrders",
      "BenchmarkIndustry.totalCustomers",
      "BenchmarkIndustry.totalRevenue",
      "BenchmarkIndustry.championPercentage",
      "BenchmarkIndustry.loyalPercentage",
      "BenchmarkIndustry.promisingPercentage",
      "BenchmarkIndustry.newCustomersPercentage",
      "BenchmarkIndustry.needAttentionPercentage",
      "BenchmarkIndustry.shouldNotLoosePercentage",
      "BenchmarkIndustry.sleepersPercentage",
      "BenchmarkIndustry.lostPercentage",
      "BenchmarkIndustry.championPercentageP25",
      "BenchmarkIndustry.loyalPercentageP25",
      "BenchmarkIndustry.promisingPercentageP25",
      "BenchmarkIndustry.newCustomersPercentageP25",
      "BenchmarkIndustry.needAttentionPercentageP25",
      "BenchmarkIndustry.shouldNotLoosePercentageP25",
      "BenchmarkIndustry.sleepersPercentageP25",
      "BenchmarkIndustry.lostPercentageP25",
      "BenchmarkIndustry.championPercentageP75",
      "BenchmarkIndustry.loyalPercentageP75",
      "BenchmarkIndustry.promisingPercentageP75",
      "BenchmarkIndustry.newCustomersPercentageP75",
      "BenchmarkIndustry.needAttentionPercentageP75",
      "BenchmarkIndustry.shouldNotLoosePercentageP75",
      "BenchmarkIndustry.sleepersPercentageP75",
      "BenchmarkIndustry.lostPercentageP75",
      "BenchmarkIndustry.ltv1mP5",
      "BenchmarkIndustry.ltv1mP25",
      "BenchmarkIndustry.ltv1mP50",
      "BenchmarkIndustry.ltv1mP75",
      "BenchmarkIndustry.ltv1mP95",
      "BenchmarkIndustry.ltv3mP5",
      "BenchmarkIndustry.ltv3mP25",
      "BenchmarkIndustry.ltv3mP50",
      "BenchmarkIndustry.ltv3mP75",
      "BenchmarkIndustry.ltv3mP95",
      "BenchmarkIndustry.ltv6mP5",
      "BenchmarkIndustry.ltv6mP25",
      "BenchmarkIndustry.ltv6mP50",
      "BenchmarkIndustry.ltv6mP75",
      "BenchmarkIndustry.ltv6mP95",
      "BenchmarkIndustry.ltv1yP5",
      "BenchmarkIndustry.ltv1yP25",
      "BenchmarkIndustry.ltv1yP50",
      "BenchmarkIndustry.ltv1yP75",
      "BenchmarkIndustry.ltv1yP95",
      "BenchmarkIndustry.return1mP5",
      "BenchmarkIndustry.return1mP25",
      "BenchmarkIndustry.return1mP50",
      "BenchmarkIndustry.return1mP75",
      "BenchmarkIndustry.return1mP95",
      "BenchmarkIndustry.return3mP5",
      "BenchmarkIndustry.return3mP25",
      "BenchmarkIndustry.return3mP50",
      "BenchmarkIndustry.return3mP75",
      "BenchmarkIndustry.return3mP95",
      "BenchmarkIndustry.return6mP5",
      "BenchmarkIndustry.return6mP25",
      "BenchmarkIndustry.return6mP50",
      "BenchmarkIndustry.return6mP75",
      "BenchmarkIndustry.return6mP95",
      "BenchmarkIndustry.return1yP5",
      "BenchmarkIndustry.return1yP25",
      "BenchmarkIndustry.return1yP50",
      "BenchmarkIndustry.return1yP75",
      "BenchmarkIndustry.return1yP95",
      "BenchmarkIndustry.arpuP5",
      "BenchmarkIndustry.arpuP25",
      "BenchmarkIndustry.arpuP50",
      "BenchmarkIndustry.arpuP75",
      "BenchmarkIndustry.arpuP95",
      "BenchmarkIndustry.aovP5",
      "BenchmarkIndustry.aovP25",
      "BenchmarkIndustry.aovP50",
      "BenchmarkIndustry.aovP75",
      "BenchmarkIndustry.aovP95",
      "BenchmarkIndustry.oneTimers",
      "BenchmarkIndustry.twoTimers",
      "BenchmarkIndustry.threeTimers",
      "BenchmarkIndustry.fourToFiveTimers",
      "BenchmarkIndustry.fivePlusTimers",
      "BenchmarkIndustry.oneTimersP25",
      "BenchmarkIndustry.twoTimersP25",
      "BenchmarkIndustry.threeTimersP25",
      "BenchmarkIndustry.fourToFiveTimersP25",
      "BenchmarkIndustry.fivePlusTimersP25",
      "BenchmarkIndustry.oneTimersP75",
      "BenchmarkIndustry.twoTimersP75",
      "BenchmarkIndustry.threeTimersP75",
      "BenchmarkIndustry.fourToFiveTimersP75",
      "BenchmarkIndustry.fivePlusTimersP75",
      "BenchmarkIndustry.revenueGrowthLatest",
      "BenchmarkIndustry.newCustomerGrowthLatest",
      "BenchmarkIndustry.orderGrowthLatest",
      "BenchmarkIndustry.growthMonthLatest",
      "BenchmarkIndustry.revenueGrowthLatestP25",
      "BenchmarkIndustry.newCustomerGrowthLatestP25",
      "BenchmarkIndustry.orderGrowthLatestP25",
      "BenchmarkIndustry.revenueGrowthLatestP75",
      "BenchmarkIndustry.newCustomerGrowthLatestP75",
      "BenchmarkIndustry.orderGrowthLatestP75",
    ]
  }
}