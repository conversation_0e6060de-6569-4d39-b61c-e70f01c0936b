import React, {useState} from "react";
import {tracker, useMaterialUIController} from "@/context";
import {Link} from "react-router-dom";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import Icon from "@mui/material/Icon";
import MDTypography from "@/components/MDTypography";
import {shopResults, industryResultsQuery} from './benchmarkQueries';
import GrowthChart from "@/layouts/benchmarks/GrowthChart";
import { cubejsApi } from "@/context";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { useTranslation } from 'react-i18next';

import CircularProgress from "@mui/material/CircularProgress";
import MDTooltip from "@/components/MDTooltip";
import { InfoOutlinedIcon } from "@/examples/Icons";

const GatheringData = () => {
    const {t} = useTranslation();
    return (
        <MDBox 
            display="flex"
            alignItems="center"
            flexDirection="column"
            justifyContent="center"
            height="350px"
            mb={4}
            >
                <MDBox
                    component="img"
                    pl={3}
                    sx={{
                        width:"30%",
                    }}
                    src={"https://illustrations.popsy.co/white/meditation-outdoors.svg"}
                    alt={"gethering-data"}
                />
                <MDTypography variant="button" color="secondary" fontWeight="regular" mt={1} mx={10}>
                    {t("benchmark-nodata-desc")}
                </MDTypography>
        </MDBox>
    )
}

var getResults = async function (selectedShop, industry, currency) {
    let result = {
        shop : {},
        industry : {}
    }

    try {
        let cubeApi = cubejsApi(selectedShop, "BenchmarkShops");
        let currShopResults = await cubeApi.load(shopResults)
        let shopResultsData = currShopResults.tablePivot()
        if (shopResultsData.length != 0) {
            for (var k in shopResultsData[0]) {
                result.shop[k.split(".")[1]] = shopResultsData[0][k]
            }
        } else {
            return result
        }

        if (result.shop.shopCurrency == "0") {
            return result
        }

        let cubeApiIndustry = cubejsApi(selectedShop, "BenchmarkIndustry");
        let currIndustryResults = await cubeApiIndustry.load(industryResultsQuery(industry, currency));
        let industryResultsData = currIndustryResults.tablePivot()
        if (industryResultsData.length != 0) {
            for (var k in industryResultsData[0]) {
                result.industry[k.split(".")[1]] = industryResultsData[0][k]
            }
        }

        return result
    } catch (error) {
        console.log(error)
        return result
    }
}

function GrowthChartDashboard({reportLink}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;
    const {t} = useTranslation();

    let isSubscriptionInActive =  shopConfig.subscription_enabled && !(shopConfig.planDetails?.features?.industry_benchmarks ?? false);
    const [results, setResults] = React.useState({});
    const [loading, setLoading] = React.useState(true);

    const industry = shopConfig.shop?.onboard_industry ?? "";
    const currency = shopConfig.shop?.currency ?? "";

    React.useEffect(() => {
        setLoading(true)
        getResults(selectedShop, industry, currency).then((data) => {
            setResults(data)
            setLoading(false)
        })
    }, [selectedShop, industry, currency, t]);

    let noData = !results || !results.shop || Object.keys(results.shop).length == 0
    noData = noData || !results || !results.industry || Object.keys(results.industry).length == 0 || results.industry.shopCount < 4

    let blurBlockContent = !isSubscriptionInActive ? null : (
        <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            position="relative"
            width="80%"
            margin="auto"
            top={"-50%"}
            sx={{
                backgroundColor: "transparent",
                zIndex: 2
            }}
        >
            <MDButton variant="contained" color="info" size="small" onClick={() => {
                tracker.event("Paywall", {feature: "industry_benchmarks"});
                NiceModal.show(PaywallDialog, {feature : "industry_benchmarks"})
            }}>
                {t("start-trial-get-access")}
                <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={1} />
            </MDButton>
        </MDBox>
    );

    return (
        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
            <Grid item display="flex" justifyContent="flex-start" alignItems="center">
            <MDTypography variant="h5" color="secondary" fontWeight="regular" mr={1} className="card-title-default">
                {t("benchmark-growth-title")} {t("benchmarks")}  <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.5} />
            </MDTypography>
            <MDTooltip title={t("benchmark-growth-desc")} placement="right">
                <MDBox><InfoOutlinedIcon /></MDBox>
            </MDTooltip>
            </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <Grid container spacing={1.2} p={1.6} pb={isSubscriptionInActive ? 0 : 2.4}>
            {loading ? (
                <MDBox px={1} width="100%" height="100%" mx="auto">
                    <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%" sx={{minHeight:300}}>
                        <CircularProgress color="secondary" />
                    </Grid>
                </MDBox>
            ) : (noData ? <GatheringData/> : <Grid item xs={12} md={12} lg={12} sx={{minHeight:300}}>
                    <GrowthChart isEmbedded={true} results={results} isSubscriptionInActive={isSubscriptionInActive} />
                    {blurBlockContent}
                </Grid>)
            }
        </Grid>
        {!loading && !noData && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
        {!loading && !noData && <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="end" sx={{width :"100%"}}>
            <MDButton variant="outlined" color={"dark"} component={Link} to={"/benchmarks"} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                    {t("deepdive-btn")} &nbsp;
                    <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                </MDTypography>
            </MDButton>  
        </MDBox>}
        </Card>
    );
}

export default GrowthChartDashboard;