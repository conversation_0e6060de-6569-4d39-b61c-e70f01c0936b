import {useRef, useState } from "react";
import Dialog from '@mui/material/Dialog';
import { useNavigate } from "react-router-dom";

// Material Dashboard 2 PRO React components
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import InputAdornment from "@mui/material/InputAdornment";
import MDInput from "@/components/MDInput";
import CircularProgress from "@mui/material/CircularProgress";
import logoShopify from "@/assets/images/shopify-logo.svg";
import { signOutUser } from "@/utils/auth";
import { useTranslation } from "react-i18next";

function AddStore(props) {
  return (
    <BasicLayout image={bgImage}>
      <MDBox height="100vh" display="flex" flexDirection="column" justifyContent="center" alignItems="center">
        <AddStoreRoot {...props} signOutOption={true} />
      </MDBox>
    </BasicLayout>
  );
}

export const AddStoreDialog = NiceModal.create(() => {
  const modal = useModal();
  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
      <AddStoreRoot signOutOption={false} />
    </Dialog>
  );
});

function AddStoreRoot({signOutOption}) {

    const {t} = useTranslation();
    const navigate = useNavigate();
    const [shopValue, setShopValue] = useState("");
    const [loginStart, setLoginStart] = useState(false);
    const shopifyBtnEl = useRef(null);

  // Shopify login submit
  const handleShopifySubmit = (ev) => {
    ev.preventDefault();
    if (!!shopifyBtnEl && !!shopifyBtnEl.current) {
      shopifyBtnEl.current.click();
    }
  }

  return (
      <Card>
      <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" m={4}>
      <MDTypography variant="h4" gutterBottom>
        {t("add-new-store")}
      </MDTypography>
      <MDTypography variant="button">
        {t("add-store-desc")}
      </MDTypography>
        <MDBox component="form" role="form" onSubmit={handleShopifySubmit} mt={3} mb={1} width="100%">
            <MDBox mb={2}>
            <MDInput
                variant="outlined"
                fullWidth
                onChange={(e) => {setShopValue(e.target.value)}}
                label={t("store-address")}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <MDBox component="img" src={logoShopify} alt="shopify" width="1rem" mx={0.1} />
                    </InputAdornment>
                  ),
                  autoComplete:"off",
                  endAdornment: (
                  <InputAdornment position="end" sx={{fontSize: "13px"}}>
                    <span >.myshopify.com</span>
                  </InputAdornment>)
                }}
                InputLabelProps={{ shrink: true }}
              />
            </MDBox>
            <MDBox mt={4} mb={1}>
              <MDButton
                ref={shopifyBtnEl}
                variant="gradient"
                color={shopValue == "" ? "light" : "info"}
                fullWidth
                fontWeight="bold"
                disabled={shopValue == ""}
                onClick={() => {setLoginStart(true)}}
                href={`/?shop=${shopValue.replace(".myshopify.com", "") + ".myshopify.com"}`}
              >
                {loginStart ? <CircularProgress size={20} color="white" /> : t("login-shopify")}
              </MDButton>
            </MDBox>
        </MDBox>
        {!!signOutOption && <MDTypography variant="button" onClick={() => signOutUser(navigate)} color="secondary" mt={1} fontWeight="regular" sx={{cursor: "pointer"}}>
          {t("sign-in-diff-acc")}
        </MDTypography>}
      </MDBox>
      </Card>
  );
}

export default AddStore;