import React from 'react';
import {useNavigate} from 'react-router-dom';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContentText from '@mui/material/DialogContentText';
import MDTypography from '@/components/MDTypography';
import MDButton from "@/components/MDButton";
import Box from "@mui/material/Box";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import MDBox from "@/components/MDBox";
import { useTranslation } from 'react-i18next';
import premiumTag from "@/assets/images/premium-tag.png";
import image from "@/assets/images/multi-store.png";
import { useMaterialUIController } from "@/context";

const MultiStoreDialog = NiceModal.create(({preferSignIn}) => {

  const modal = useModal();
  const {t} = useTranslation();
  const navigate  = useNavigate()
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig} = controller;

  // check if upgrade is needed to access the feature
  let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.["connect_multiple_accounts"] ?? false);

  const handleConnect = () => {
    
    if (isSubscriptionInActive) {
        navigate("/pricing")
        modal.hide()
        return false
    }

    navigate(!!preferSignIn ? "/sign-in" : "/sign-up")
    modal.hide()
  }

  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
    <DialogContent>
        <MDTypography variant="h4" textAlign="center" fontWeight="regular">
            {t("multi-store-dialog")}
        </MDTypography>
        <Box display="flex" justifyContent="center" alignItems="center">
            <MDBox component="img" src={image} alt="Brand" width="100%" my={1}  />
        </Box>
        <MDTypography variant="body2" textAlign="center" mt={2} fontWeight="regular">
            {t("multi-store-dialog-desc")}
        </MDTypography>
    </DialogContent>
    <DialogActions sx={{marginBottom : "10px"}}>
        <MDButton onClick={modal.hide} fullWidth size="small" variant="gradient" color="secondary">
            {t("cancel")}
        </MDButton>
        <MDButton autoFocus onClick={handleConnect} fullWidth size="small" variant="gradient" color="info">
            {preferSignIn ? t("multi-store-sign-in") : t("create-acc-login")}
            {isSubscriptionInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
        </MDButton>
    </DialogActions>
    </Dialog>
  );
});

export default MultiStoreDialog;