import { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { axiosInstance, setLoginConfig, useMaterialUIController } from "@/context";
import { toast } from 'react-toastify';

// @mui material components
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";
import Icon from "@mui/material/Icon";

import bgImage from "@/assets/images/bg-sign-up-cover.jpeg";
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import { app } from '@/firebase-config';
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { getMessageForFirebaseError } from "@/layouts/authentication/util";
import { useLocalStorage, SELECTED_SHOP_KEY } from '@/localstore';

export function Identification() {

  const navigate = useNavigate()
  const [searchParams] = useSearchParams();

  const presetEmail = searchParams.get("email");

  const [email, setEmail] = useState(presetEmail || "");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [emailSubmitStart, setEmailSubmitStart] = useState(false);
  const [flowType, setFlowType] = useState(null);
  const [passwordSubmitStart, setPasswordSubmitStart] = useState(false);
  const [selectionConfig, setSelectionConfig] = useState(null);
  const [subHeadingText, setSubHeadingText] = useState("Please enter email to identify");
  const [selectedShop, setSelectedShop] = useLocalStorage(SELECTED_SHOP_KEY, null);

  let location = useLocation();

  const [controller, dispatch] = useMaterialUIController();
  const { loginConfig } = controller;
  const loginShop = loginConfig?.shop

  useEffect(() => {
    const auth = getAuth(app);
    const unsubscribe = auth.onAuthStateChanged(user => {
      if (!!user && !!user.email) {
        // Then navigate with the state
        navigate(location.state && location.state.from ? location.state.from : '/');
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    switch (flowType) {
      case "register":
        setSubHeadingText("Please create a new password");
        break;
      case "login":
        setSubHeadingText("Please enter your password");
        break;
      default:
        setSubHeadingText("Please enter email to identify");
        break;
    }
  }, [flowType]);

  const handleEmailSubmit = () => {
    if (emailSubmitStart) {
      return;
    }

    setEmailSubmitStart(true);
    axiosInstance.post("/api/identify-user/email-submit", {
      email,
      shop_id: loginConfig?.shop?.shop_id
    })
      .then((response) => {
        setEmailSubmitStart(false);
        if (response.data && response.data.status) {
          setFlowType(response.data.flow);
          setSelectionConfig(response.data.config);
          setSelectedShop(loginShop?.myshopify_domain);
        } else {
          toast.error('Something went wrong. Please try again.');
        }
      })
      .catch((error) => {
        setEmailSubmitStart(false);
        toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
      });
  }

  const handlePasswordSubmit = (ev) => {
    ev.preventDefault();
    if (flowType == "register") {
      handleRegisterOnPasswordSubmit(ev);
    } else if (flowType == "login") {
      handleLoginOnPasswordSubmit(ev);
    } else {
      toast.error('Something went wrong. Please try again.');
    }
  }

  const handleRegisterOnPasswordSubmit = async (ev) => {
    ev.preventDefault();

    if (passwordSubmitStart) {
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setPasswordSubmitStart(true);

    try {
      const auth = getAuth(app);

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (!user) {
        toast.error('Something went wrong. Please try again.');
        return;
      }

      const idToken = await user.getIdToken();

      axiosInstance.post("/api/create-user", {
        id_token: idToken
      })
        .then((response) => {
          if (response.data && response.data.status) {
            toast.success('Registered Successfully');
            handleLoginOnPasswordSubmit(ev);
          } else {
            toast.error('Something went wrong. Please try again.');
          }
        })
        .catch((error) => {
          const errorMessage = getMessageForFirebaseError(error);
          toast.error(errorMessage);
        });
    } catch (error) {
      toast.error(error.message);
    }

    setPasswordSubmitStart(false);
  };

  const handleLoginOnPasswordSubmit = (ev) => {
    ev.preventDefault();
    if (passwordSubmitStart) {
      return;
    }

    setPasswordSubmitStart(true)
    const auth = getAuth(app);
    signInWithEmailAndPassword(auth, email, password)
      .catch((error) => {
        const errorMessage = getMessageForFirebaseError(error);
        toast.error(errorMessage);
      })
      .finally(() => {
        setPasswordSubmitStart(false);
      });
  }

  const handleEditEmail = () => {
    setFlowType(null);
    setPassword("");
    setConfirmPassword("");
  }

  return (
    <BasicLayout image={bgImage} chooseLanguage={false}>
      <MDBox py={4}></MDBox>
      <MDBox py={1.3}></MDBox>
      <Card>
        <MDBox pt={4} pb={1} px={3}>
          <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mb={2}>
            <MDTypography variant="button" color="secondary" textAlign="center" fontWeight="medium">
              Welcome to {loginShop?.name}
            </MDTypography>
            <MDTypography id="shopify-identity-sub-heading" variant="button" color="secondary" textAlign="center" fontWeight="regular">
              {subHeadingText}
            </MDTypography>
          </MDBox>
          <MDBox component="form" role="form" >
            {!flowType && (
              <MDBox mb={2}>
                <MDInput
                  type="email"
                  label="Email"
                  onChange={(e) => { setEmail(e.target.value) }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && email !== "") {
                      e.preventDefault();
                      handleEmailSubmit();
                    }
                  }}
                  value={email}
                  variant="standard"
                  fullWidth
                  placeholder="<EMAIL>"
                  InputLabelProps={{ shrink: true }}
                />
                <MDBox mt={3} mb={1}>
                  <MDButton
                    variant="gradient"
                    color={(email == "") ? "light" : "info"}
                    fullWidth
                    fontWeight="bold"
                    onClick={handleEmailSubmit}
                    disabled={email == ""}
                    sx={{
                      "&:disabled": {
                        color: "grey"
                      }
                    }}
                  >
                    {emailSubmitStart ? <CircularProgress size={20} color="white" /> : "Continue"}
                  </MDButton>
                </MDBox>
              </MDBox>
            )}
            {email && flowType && (flowType.includes("register") || flowType.includes("login")) && (
              <MDBox mb={2}>
                <MDTypography variant="body2" color="secondary"
                  sx={{
                    fontSize: "0.75rem",
                    fontWeight: 400,
                    lineHeight: 1.25,
                    letterSpacing: "0.00938em",
                    marginBottom: "1em",
                  }}
                >
                  Email: {email}&nbsp;
                  <Icon
                    sx={{
                      fontSize: "0.75rem",
                      cursor: "pointer",
                      verticalAlign: "top",
                      "&:hover": {
                        opacity: 0.7
                      }
                    }}
                    onClick={handleEditEmail}
                  >
                    edit_outlined
                  </Icon>
                </MDTypography>
                <MDBox mb={2}>
                  <MDInput
                    type="password"
                    label={flowType.includes("register") ? "New Password" : "Password"}
                    onChange={(e) => { setPassword(e.target.value) }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && password !== "" && (confirmPassword !== "" || !flowType.includes("login"))) {
                        e.preventDefault();
                        handlePasswordSubmit(e);
                      }
                    }}
                    value={password}
                    variant="standard"
                    fullWidth
                    placeholder="************"
                    InputLabelProps={{ shrink: true }}
                  />
                </MDBox>
                {flowType.includes("register") && (
                  <MDBox mb={2}>
                    <MDInput
                      type="password"
                      label="Confirm Password"
                      onChange={(e) => { setConfirmPassword(e.target.value) }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && password !== "" && confirmPassword !== "") {
                          e.preventDefault();
                          handlePasswordSubmit(e);
                        }
                      }}
                      value={confirmPassword}
                      variant="standard"
                      fullWidth
                      placeholder="************"
                      InputLabelProps={{ shrink: true }}
                    />
                  </MDBox>
                )}
                <MDBox mt={3} mb={1}>
                  <MDButton
                    variant="gradient"
                    color={(password == "") ? "light" : "info"}
                    fullWidth
                    fontWeight="bold"
                    onClick={handlePasswordSubmit}
                    disabled={password == ""}
                    sx={{
                      "&:disabled": {
                        color: "grey"
                      }
                    }}
                  >
                    {passwordSubmitStart ? <CircularProgress size={20} color="white" /> : flowType.includes("register") ? "Register" : "Login"}
                  </MDButton>
                </MDBox>
              </MDBox>
            )}
          </MDBox>
        </MDBox>
      </Card>
    </BasicLayout>
  );
}

export default Identification;
