import { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { Link } from "react-router-dom";


// @mui material components
import Card from "@mui/material/Card";
import logoShopify from "@/assets/images/shopify-logo.svg";
import InputAdornment from "@mui/material/InputAdornment";
import { toast } from 'react-toastify';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";

// import bgImage from "@/assets/images/bg-sign-in-cover.jpeg";
import bgImage from "@/assets/images/bg-sign-up-cover.jpeg";

// Authentication layout components
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import { app } from '@/firebase-config';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

function Cover() {

  const navigate = useNavigate()
  const [searchParams] = useSearchParams();

  const presetEmail = searchParams.get("email");

  const [shopValue, setShopValue] = useState("");
  const [loginStart, setLoginStart] = useState(false);
  const [userLoginStart, setUserLoginStart] = useState(false);
  const [email, setEmail] = useState(presetEmail || "");
  const [password, setPassword] = useState("");
  const shopifyBtnEl = useRef(null);

  let location = useLocation();

  // Shopify login submit
  const handleShopifySubmit = (ev) => {
    ev.preventDefault();
    if (!!shopifyBtnEl && !!shopifyBtnEl.current) {
      shopifyBtnEl.current.click();
    }
  }

  useEffect(() => {
    const auth = getAuth(app);
    const unsubscribe = auth.onAuthStateChanged(user => {
      if (!!user && !!user.email) {
        navigate(location.state && location.state.from ? location.state.from : '/')
      }
    });

    // return unregister observer
    return () => unsubscribe();
  }, []);

  let handleUserLogin = () => {

    if (userLoginStart) {
      return;
    }

    setUserLoginStart(true)
    const authentication = getAuth(app);
    signInWithEmailAndPassword(authentication, email, password)
      .then((userCred) => {
        // Login should get captured by onAuthStateChanged observer in App.jsx
        setUserLoginStart(false)
      })
      .catch((error) => {
        setUserLoginStart(false)
        if (error.code === 'auth/wrong-password') {
          toast.error('Incorrect password. Please try again');
        } else if (error.code === 'auth/user-not-found') {
          toast.error('Email is not registered. Please check again');
        } else if (error.code === 'auth/too-many-requests') {
          toast.error('Too many requests received');
        } else {
          toast.error(error.message);
        }
      })
  }

  return (
    <BasicLayout image={bgImage} chooseLanguage={false}>
      <MDBox py={4}></MDBox>
      {!presetEmail &&
        <Card>
          <MDBox
            variant="gradient"
            bgColor="info"
            borderRadius="lg"
            coloredShadow="info"
            mx={2}
            mt={-3}
            p={2}
            mb={1}
            textAlign="center"
          >
            <MDTypography variant="h6" fontWeight="medium" color="white" mt={1}>
              Sign In
            </MDTypography>
          </MDBox>
          <MDBox pt={4} pb={3} px={3}>
            <MDBox component="form" role="form" onSubmit={handleShopifySubmit}>
              <MDBox mb={1}>
                <MDInput
                  variant="outlined"
                  fullWidth
                  onChange={(e) => { setShopValue(e.target.value) }}
                  label="Store address"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <MDBox component="img" src={logoShopify} alt="shopify" width="1rem" mx={0.1} />
                      </InputAdornment>
                    ),
                    autoComplete: "off",
                    endAdornment: (
                      <InputAdornment position="end">
                        <span sytle={{ fontSize: "10px" }}>.myshopify.com</span>
                      </InputAdornment>)
                  }}
                  InputLabelProps={{ shrink: true }}
                />
              </MDBox>
              <MDBox mt={3} >
                <MDButton
                  ref={shopifyBtnEl}
                  variant="gradient"
                  color={shopValue == "" ? "light" : "info"}
                  fullWidth
                  fontWeight="bold"
                  disabled={shopValue == ""}
                  onClick={() => { setLoginStart(true) }}
                  href={`/?shop=${shopValue.replace(".myshopify.com", "") + ".myshopify.com"}`}
                >
                  {loginStart ? <CircularProgress size={20} color="white" /> : "Login with Shopify"}
                </MDButton>
              </MDBox>
            </MDBox>
            <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mt={2}>
              <MDTypography variant="caption" color="secondary">
                or Install the app from &nbsp;
                <MDTypography variant="caption" color="info" fontWeight="regular" component="a" href="https://apps.shopify.com/customer-lifetime-value" target="_blank" rel="noopener noreferrer">
                  Shopify App Store
                </MDTypography>
              </MDTypography>
            </MDBox>
          </MDBox>
        </Card>
      }
      <MDBox py={1.3}></MDBox>
      <Card>
        <MDBox pt={4} pb={1} px={3}>
          <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mb={2}>
            <MDTypography variant="button" color="secondary" textAlign="center" fontWeight="medium">
              Managing Multiples stores?
            </MDTypography>
            <MDTypography variant="button" color="secondary" textAlign="center" fontWeight="regular">
              Sign in below
            </MDTypography>
          </MDBox>

          <MDBox component="form" role="form" onSubmit={handleUserLogin} >
            <MDBox mb={2}>
              <MDInput
                type="email"
                label="Email"
                onChange={(e) => { setEmail(e.target.value) }}
                value={email}
                variant="standard"
                fullWidth
                placeholder="<EMAIL>"
                InputLabelProps={{ shrink: true }}
              />
            </MDBox>

            <MDBox mb={2}>
              <MDInput
                type="password"
                label="Password"
                variant="standard"
                onChange={(e) => { setPassword(e.target.value) }}
                fullWidth
                placeholder="************"
                InputLabelProps={{ shrink: true }}
              />
            </MDBox>

            <MDBox mt={3} mb={1}>
              <MDButton
                variant="gradient"
                color={(email == "" || password == "") ? "light" : "info"}
                fullWidth
                fontWeight="bold"
                onClick={handleUserLogin}
                disabled={email == "" || password == ""}
                sx={{
                  "&:disabled": {
                    color: "grey"
                  }
                }}
              >
                {userLoginStart ? <CircularProgress size={20} color="white" /> : "Login"}
              </MDButton>
            </MDBox>
          </MDBox>
        </MDBox>
        <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mb={1.5}>
          <Link to={`/reset-password${location.search}`}>
            <MDTypography variant="button" color="secondary">
              Reset Password?
            </MDTypography>
          </Link>
          <Link to={`/sign-up${location.search}`} state={{ from: location.state && location.state.from ? location.state.from : '/' }}>
            <MDTypography variant="button" color="secondary">
              Don't have an account? &nbsp;
              <MDTypography variant="button" color="info" fontWeight="medium" textGradient>
                Sign Up
              </MDTypography>
            </MDTypography>
          </Link>
        </MDBox>
      </Card>
    </BasicLayout>
  );
}

export default Cover;
