// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Material Dashboard 2 PRO React examples
import Default<PERSON><PERSON>bar from "@/examples/Navbars/DefaultNavbar";
import PageLayout from "@/examples/LayoutContainers/PageLayout";

function BasicLayout({ image, children, chooseLanguage }) {
  return (
    <PageLayout>
      <DefaultNavbar
        chooseLanguage={chooseLanguage}
        // action={{
        //   type: "external",
        //   route: "https://creative-tim.com/product/material-dashboard-pro-react",
        //   label: "buy now",
        // }}
        transparent
        light
      />
      <MDBox
        position="absolute"
        width="100%"
        minHeight="100vh"
        sx={{
          backgroundImage: ({ functions: { linearGradient, rgba }, palette: { gradients } }) =>
            image &&
            `${linearGradient(
              rgba(gradients.dark.main, 0.6),
              rgba(gradients.dark.state, 0.6)
            )}, url(${image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      />
      <MDBox px={1} width="100%" height="100vh" mx="auto">
        <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%">
          <Grid item xs={11} sm={9} md={5} lg={4} xl={3}>
            {children}
          </Grid>
        </Grid>
      </MDBox>
    </PageLayout>
  );
}

BasicLayout.defaultProps = {
  chooseLanguage: true,
};

// Typechecking props for the BasicLayout
BasicLayout.propTypes = {
  image: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  chooseLanguage: PropTypes.bool,
};

export default BasicLayout;
