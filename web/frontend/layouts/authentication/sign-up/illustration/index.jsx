import { useEffect, useState } from "react";

// react-router-dom components
import { Link, useNavigate, useLocation, useSearchParams } from "react-router-dom";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";
import { toast } from 'react-toastify';

// Authentication layout components
import IllustrationLayout from "@/layouts/authentication/components/IllustrationLayout";
// Image
import bgImage from "@/assets/images/illustrations/illustration-sign-up.png";
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from "firebase/auth";
import { app } from '@/firebase-config';
import ReCAPTCHA from "react-google-recaptcha";
import { getMessageForFirebaseError } from "@/layouts/authentication/util";
import { axiosInstance } from "@/context";

function Illustration() {

  const navigate = useNavigate()
  const [searchParams] = useSearchParams();

  const presetEmail = searchParams.get("email");
  const [email, setEmail] = useState(presetEmail || "");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [signUpStart, setSignUpStart] = useState(false);

  const [captchaValue, setCaptchaValue] = useState(null);
  const isBtnDisabled = !email || !password || !confirmPassword || !captchaValue;
  let location = useLocation();

  useEffect(() => {
    const auth = getAuth(app);
    const unsubscribe = auth.onAuthStateChanged(user => {
      if (!!user) {
        navigate(location.state && location.state.from ? location.state.from : '/')
      }
    });

    // return unregister observer
    return () => unsubscribe();
  }, [])


  const handleSignUp = async (event) => {
    event.preventDefault();

    if (signUpStart) {
      return;
    }

    if (!captchaValue) {
      // Handle missing captcha...
      toast.error('Please complete the captcha');
      return;
    }

    if (!email || !email.includes("@")) {
      toast.error('Please enter a valid work email address');
      return;
    }

    // if (email.endsWith("@gmail.com") || email.endsWith("@yahoo.com")) {
    //   toast.error('Please enter a valid password');
    //   return;
    // }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setSignUpStart(true);
    const auth = getAuth(app);

    try {

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (!user) {
        toast.error('Something went wrong. Please try again.');
        return;
      }

      toast.success('Registered Successfully');
      signInWithEmailAndPassword(auth, email, password)
        .catch((error) => {
          const errorMessage = getMessageForFirebaseError(error);
          toast.error(errorMessage);
        });
    } catch (error) {
      const errorMessage = getMessageForFirebaseError(error);
      toast.error(errorMessage);
    } finally {
      setSignUpStart(false);
    }
  };

  return (
    <IllustrationLayout
      title="Get Started for Free"
      description="Welcome to DataDrew Analytics"
      illustration={bgImage}
    >
      <MDBox pt={0} pb={3}>
        <MDBox component="form" role="form" onSubmit={handleSignUp}>
          <MDBox mb={2}>
            <MDInput
              variant="outlined"
              fullWidth
              value={email}
              onChange={(e) => { setEmail(e.target.value) }}
              label="Work Email"
              type="email"
              required
            />
          </MDBox>
          <MDBox mb={2}>
            <MDInput
              variant="outlined"
              fullWidth
              onChange={(e) => { setPassword(e.target.value) }}
              label="Password"
              type="password"
              required
            />
          </MDBox>
          <MDBox mb={2}>
            <MDInput
              variant="outlined"
              fullWidth
              onChange={(e) => { setConfirmPassword(e.target.value) }}
              label="Confirm Password"
              type="password"
              required
            />
          </MDBox>
          <MDBox mt={4} display="flex" alignItems="center" justifyContent="center">
            <ReCAPTCHA
              sitekey="6LdfjTUpAAAAAMQhnG31piPeu14xxHRp0ySd73Bc"
              onChange={setCaptchaValue}
            />
          </MDBox>
          <MDBox mt={4} mb={1}>
            <MDButton
              variant="gradient"
              color={(isBtnDisabled) ? "light" : "info"}
              fullWidth
              fontWeight="bold"
              type="submit"
              disabled={isBtnDisabled}
              sx={{
                "&:disabled": {
                  color: "grey"
                }
              }}
            >
              {signUpStart ? <CircularProgress size={20} color="white" /> : "Sign Up"}
            </MDButton>
          </MDBox>
        </MDBox>
        <MDBox mt={3} textAlign="center">
          <MDTypography variant="button" color="secondary">
            Already have an account?{" "}
            <MDTypography
              component={Link}
              to="/sign-in"
              variant="button"
              color="info"
              fontWeight="medium"
              textGradient
            >
              Sign In Instead
            </MDTypography>
          </MDTypography>
        </MDBox>
      </MDBox>
    </IllustrationLayout>
  );
}

export default Illustration;
