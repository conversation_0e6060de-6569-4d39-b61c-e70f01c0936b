
export const getMessageForFirebaseError = (error) => {
    let errorCode = error.code;
    let errorMessage = error.message;

    switch (errorCode) {
        case 'auth/email-already-in-use':
            errorMessage = 'Error: The email address is already in use. Please login instead.';
            break;
        case 'auth/invalid-email':
            errorMessage = 'Error: The email address is not valid.';
            break;
        case 'auth/operation-not-allowed':
            errorMessage = 'Error: Email/password accounts are not enabled. Reach out to Support';
            break;
        case 'auth/weak-password':
            errorMessage = 'Error: The password is not strong enough. Try again with a stronger password.';
            break;
        case 'auth/wrong-password':
            errorMessage = 'Error: Incorrect password. Please try again.';
            break;
        case 'auth/user-not-found':
            errorMessage = 'Error: The email is not registered. Please check again.';
            break;
        case 'auth/too-many-requests':
            errorMessage = 'Error: Too many requests received. Please try again later.';
    }

    return errorMessage;
}
