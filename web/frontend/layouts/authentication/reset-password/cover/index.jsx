import {useState} from "react";
// @mui material components
import Card from "@mui/material/Card";
import { Link } from 'react-router-dom';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import { toast } from 'react-toastify';
import MDButton from "@/components/MDButton";

// Authentication layout components
import CoverLayout from "@/layouts/authentication/components/CoverLayout";
import CircularProgress from "@mui/material/CircularProgress";

// Images
import bgImage from "@/assets/images/bg-sign-up-cover.jpeg";

import { getAuth, sendPasswordResetEmail } from "firebase/auth";

function Cover() {

  const [email, setEmail] = useState("");
  const [emailSent, setEmailSent] = useState(false);
  const [loading, setLoading] = useState(false);

  const resetPassword = () => {

    if (emailSent) {
      return;
    }

    setLoading(true)

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    const auth = getAuth();
    sendPasswordResetEmail(auth, email)
      .then(() => {
        toast.success("Password reset email sent")
        setEmailSent(true)
        setLoading(false)
      })
      .catch((error) => {
        const errorCode = error.code;
        const errorMessage = error.message;
        setLoading(false)
        console.log(errorCode, errorMessage)
        toast.error("Something went wrong")
      });

  }

  return (
    <CoverLayout coverHeight="50vh" image={bgImage}>
      <Card>
        <MDBox
          variant="gradient"
          bgColor="info"
          borderRadius="lg"
          coloredShadow="success"
          mx={2}
          mt={-3}
          py={2}
          mb={1}
          textAlign="center"
        >
          <MDTypography variant="h6" fontWeight="medium" color="white" mt={1}>
            Reset Password
          </MDTypography>
          <MDTypography display="block" variant="button" color="white" my={1}>
            You will receive an e-mail with a link to reset your password.
          </MDTypography>
        </MDBox>
        <MDBox pt={4} pb={3} px={3}>
          <MDBox component="form" role="form">
            <MDBox mb={4}>
              <MDInput
                onChange={(e) => {setEmail(e.target.value)}}
                type="email"
                label="Email"
                variant="standard"
                fullWidth />
            </MDBox>
            <MDBox mt={6} mb={1}>
              <MDButton
                variant="gradient"
                color={emailSent ? "success" : "info"}
                fullWidth
                onClick={resetPassword}
                disabled={emailSent || loading}>
                {loading ? <CircularProgress size={20} color="white" /> : (emailSent ? "Email Sent !" : "Reset")}
              </MDButton>
            </MDBox>
            {emailSent && (
              <MDBox mt={1} mb={1} display="flex" justifyContent="center">
              <Link to="/sign-in">
                <MDTypography variant="button" color="secondary" fontWeight="medium">
                  Sign In with new password?
                </MDTypography>
              </Link>
              </MDBox>
            )}
          </MDBox>
        </MDBox>
      </Card>
    </CoverLayout>
  );
}

export default Cover;
