const googleAdsSlugs = {
    "google-ads:cost" : {
        title: "google_ads.cost",
        description: "google_ads.cost-description",
        format_type: "currency",
    },
    "google-ads:clicks" : {
        title: "google_ads.clicks",
        description: "google_ads.clicks-description",
    },
    "google-ads:ctr" : {
        title: "google_ads.ctr",
        description: "google_ads.ctr-description",
        format_type: "percent",
    },
    "google-ads:cost_per_conversion_all" : {
        title: "google_ads.cost_per_conversion_all",
        description: "google_ads.cost_per_conversion_all-description",
        format_type: "currency",
    },
    "google-ads:conversions" : {
        title: "google_ads.conversions",
        description: "google_ads.conversions-description",
    },
    "google-ads:cost_per_conversion" : {
        title: "google_ads.cost_per_conversion",
        description: "google_ads.cost_per_conversion-description",
        format_type: "currency",
    },
    "google-ads:conversions_value" : {
        title: "google_ads.conversions_value",
        description: "google_ads.conversions_value-description",
        format_type: "currency",
    },
    "google-ads:conversions_value_all" : {
        title: "google_ads.conversions_value_all",
        description: "google_ads.conversions_value_all-description",
        format_type: "currency",
    },
    "google-ads:conversions_all" : {
        title: "google_ads.conversions_all",
        description: "google_ads.conversions_all-description",
    },
    "google-ads:cpc" : {
        title: "google_ads.cpc",
        description: "google_ads.cpc-description",
        format_type: "currency",
    },
    "google-ads:cpm" : {
        title: "google_ads.cpm",
        description: "google_ads.cpm-description",
        format_type: "currency",
    },
    "google-ads:engagements" : {
        title: "google_ads.engagements",
        description: "google_ads.engagements-description",
    },
    "google-ads:impressions" : {
        title: "google_ads.impressions",
        description: "google_ads.impressions-description",
    },
    "google-ads:order_rate" : {
        title: "google_ads.order_rate",
        description: "google_ads.order_rate-description",
        format_type: "percent",
    },
    "google-ads:order_rate_all" : {
        title: "google_ads.order_rate_all",
        description: "google_ads.order_rate_all-description",
        format_type: "percent",
    },
    "google-ads:revenue_per_click" : {
        title: "google_ads.revenue_per_click",
        description: "google_ads.revenue_per_click-description",
        format_type: "currency",
    },
    "google-ads:revenue_per_click_all" : {
        title: "google_ads.revenue_per_click_all",
        description: "google_ads.revenue_per_click_all-description",
        format_type: "currency",
    },
    "google-ads:roas" : {
        title: "google_ads.roas",
        description: "google_ads.roas-description",
        format_type: "roas",
    },
    "google-ads:roas_all" : {
        title: "google_ads.roas_all",
        description: "google_ads.roas_all-description",
        format_type: "roas",
    },
    "google-ads:rpm" : {
        title: "google_ads.rpm",
        description: "google_ads.rpm-description",
        format_type: "currency",
    },
    "google-ads:rpm_all" : {
        title: "google_ads.rpm_all",
        description: "google_ads.rpm_all-description",
        format_type: "currency",
    },
    "google-ads:value_per_conversion" : {
        title: "google_ads.value_per_conversion",
        description: "google_ads.value_per_conversion-description",
        format_type: "currency",
    },
    "google-ads:video_views" : {
        title: "google_ads.video_views",
        description: "google_ads.video_views-description",
    },
    "google-ads:view_through_conversions" : {
        title: "google_ads.view_through_conversions",
        description: "google_ads.view_through_conversions-description",
    },
    "google-ads:avg_conversion_value_all" : {
        title: "google_ads.avg_conversion_value_all",
        description: "google_ads.avg_conversion_value_all-description",
        format_type: "currency",
    },
    "google-ads:avg_conversion_value" : {
        title: "google_ads.avg_conversion_value",
        description: "google_ads.avg_conversion_value-description",
        format_type: "currency",
    }
}

const googleAdsMetrics = Object.keys(googleAdsSlugs).reduce((acc, key) => {
    let keyParts = key.split(":");
    acc[key] = {
        id: key,
        sources: [keyParts[0]],
        accessor: key,
        title : googleAdsSlugs[key].title ?? keyParts[1],
        description: googleAdsSlugs[key].description ?? "",
        format_type: googleAdsSlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default googleAdsMetrics;