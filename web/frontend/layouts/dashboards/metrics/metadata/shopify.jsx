const shopifySlugs = {
    "shopify:total_price" : {
        title : "total-revenue",
        description: "total-revenue-description",
        format_type: "currency"
    },
    "shopify:new_cust_order_count" : {
        title : "new-customer-orders",
        description: "new-customer-orders-description",
        format_type: "absolute"
    },
    "shopify:new_cust_total_price" : {
        title : "new-customer-revenue",
        description: "new-customer-revenue-description",
        format_type: "currency"
    },
    "shopify:returning_cust_total_price" : {
        title : "returning-customer-revenue",
        description: "returning-customer-revenue-description",
        format_type: "currency"
    },
    "shopify:new_cust_total_price_pct" : {
        title : "new-customer-revenue-pct",
        description: "new-customer-revenue-pct-description",
        format_type: "percent"
    },
    "shopify:returning_cust_total_price_pct" : {
        title : "returning-customer-revenue-pct",
        description: "returning-customer-revenue-pct-description",
        format_type: "percent"
    },
    "shopify:new_cust_count" : {
        title : "new-customer-count",
        description: "new-customer-count-description",
        format_type: "absolute"
    },  
    "shopify:cust_count" : {
        title : "customer-count",
        description: "customer-count-description",
        format_type: "absolute"
    },
    "shopify:returning_cust_count" : {
        title : "returning-customer-count",
        description: "returning-customer-count-description",
        format_type: "absolute"
    },
    "shopify:new_cust_count_pct" : {
        title : "new-customer-count-pct",
        description: "new-customer-count-pct-description",
        format_type: "percent"
    },
    "shopify:returning_cust_count_pct" : {
        title : "returning-customer-count-pct",
        description: "returning-customer-count-pct-description",
        format_type: "percent"
    },
    "shopify:order_count" : {
        title : "order-count",
        description: "order-count-description",
        format_type: "absolute"
    },
    "shopify:returning_cust_order_count" : {
        title : "returning-customer-order-count",
        description: "returning-customer-order-count-description",
        format_type: "absolute"
    },
    "shopify:new_cust_order_count_pct" : {
        title : "new-customer-order-count-pct",
        description: "new-customer-order-count-pct-description",
        format_type: "percent"
    },
    "shopify:returning_cust_order_count_pct" : {
        title : "returning-customer-order-count-pct",
        description: "returning-customer-order-count-pct-description",
        format_type: "percent"
    },
    "shopify:aov" : {
        title : "aov",
        description: "aov-description",
        format_type: "currency"
    },
    "shopify:new_cust_aov" : {
        title : "new-customer-aov",
        description: "new-customer-aov-description",
        format_type: "currency"
    },
    "shopify:returning_cust_aov" : {
        title : "returning-customer-aov",
        description: "returning-customer-aov-description",
        format_type: "currency"
    },
    "shopify:arpu" : {
        title : "arpu",
        description: "arpu-description",
        format_type: "currency"
    },
    "shopify:new_cust_arpu" : {
        title : "new-customer-arpu",
        description: "new-customer-arpu-description",
        format_type: "currency"
    },
    "shopify:returning_cust_arpu" : {
        title : "returning-customer-arpu",
        description: "returning-customer-arpu-description",
        format_type: "currency"
    },
    "shopify:rev_last1m" : {
        title : "revenue-last-1m",
        description: "revenue-last-1m-description",
        format_type: "currency"
    },
    "shopify:rev_last3m" : {
        title : "revenue-last-3m",
        description: "revenue-last-3m-description",
        format_type: "currency"
    },
    "shopify:rev_last6m" : {
        title : "revenue-last-6m",
        description: "revenue-last-6m-description",
        format_type: "currency"
    },
    "shopify:rev_last1y" : {
        title : "revenue-last-1y",
        description: "revenue-last-1y-description",
        format_type: "currency"
    }
}

const shopifyMetrics = Object.keys(shopifySlugs).reduce((acc, key) => {
    let keyParts = key.split(":");
    acc[key] = {
        id: key,
        sources: [keyParts[0]],
        accessor: key,
        title : shopifySlugs[key].title ?? key,
        description: shopifySlugs[key].description ?? "",
        format_type: shopifySlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default shopifyMetrics;