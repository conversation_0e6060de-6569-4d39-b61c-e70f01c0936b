import googleAdsMetrics from '@/layouts/dashboards/metrics/metadata/google_ads';
import googleAnalyticsMetrics from '@/layouts/dashboards/metrics/metadata/google_analytics';
import facebookAdsMetrics from '@/layouts/dashboards/metrics/metadata/facebook';
import shopifyMetrics from '@/layouts/dashboards/metrics/metadata/shopify';
import blendedMetrics from '@/layouts/dashboards/metrics/metadata/blended';
import adMetrics from '@/layouts/dashboards/metrics/metadata/ad';

const metrics = {
    ...shopifyMetrics,
    ...googleAdsMetrics,
    ...facebookAdsMetrics,
    ...blendedMetrics,
    ...googleAnalyticsMetrics,
    ...adMetrics
}

export function getMetricBySlug(slug) {
    if (slug in metrics) {
        return metrics[slug];
    }

    let slugParts = slug.split(":");
    let slugSource = slugParts[0];

    return {
        id: slug,
        sources: [slugSource],
        accessor: slug,
        title: slug,
        description: "",
        format_type: ""
    }
}

export const BLENDED = "blended";
export const SOURCE_FB = "facebook-marketing";
export const SOURCE_SHOPIFY = "shopify";
export const SOURCE_GOOGLE_ANALYTICS = "google-analytics-data-api";
export const SOURCE_GOOGLE_ADS = "google-ads";
export const SOURCE_KLAVIYO = 'klaviyo';
