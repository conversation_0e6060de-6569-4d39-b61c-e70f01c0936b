const googleAnalyticsSlugs = {
    "google-analytics-data-api:total_revenue" : {
        title: "google_analytics.total_revenue",
        description: "google_analytics.total_revenue-description",
        format_type: "currency",
    },
    "google-analytics-data-api:total_users" : {
        title: "google_analytics.total_users",
        description: "google_analytics.total_users-description",
    },
    "google-analytics-data-api:bounce_rate" : {
        title: "google_analytics.bounce_rate",
        description: "google_analytics.bounce_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:new_users" : {
        title: "google_analytics.new_users",
        description: "google_analytics.new_users-description",
    },
    "google-analytics-data-api:daily_active_users" : {
        title: "google_analytics.daily_active_users",
        description: "google_analytics.daily_active_users-description",
    },
    "google-analytics-data-api:sessions" : {
        title: "google_analytics.sessions",
        description: "google_analytics.sessions-description",
    },
    "google-analytics-data-api:engaged_sessions" : {
        title: "google_analytics.engaged_sessions",
        description: "google_analytics.engaged_sessions-description",
    },
    "google-analytics-data-api:screen_page_views" : {
        title: "google_analytics.screen_page_views",
        description: "google_analytics.screen_page_views-description",
    },
    "google-analytics-data-api:total_items_added_to_cart" : {
        title: "google_analytics.total_items_added_to_cart",
        description: "google_analytics.total_items_added_to_cart-description",
    },
    "google-analytics-data-api:ecommerce_purchases" : {
        title: "google_analytics.ecommerce_purchases",
        description: "google_analytics.ecommerce_purchases-description",
    },
    "google-analytics-data-api:users_non_unique" : {
        title: "google_analytics.users_non_unique",
        description: "google_analytics.users_non_unique-description",
    },
    "google-analytics-data-api:user_engagement_duration_secs" : {
        title: "google_analytics.user_engagement_duration_secs",
        description: "google_analytics.user_engagement_duration_secs-description",
    },
    "google-analytics-data-api:conversions" : {
        title: "google_analytics.conversions",
        description: "google_analytics.conversions-description",
    },
    "google-analytics-data-api:transactions" : {
        title: "google_analytics.transactions",
        description: "google_analytics.transactions-description",
    },
    "google-analytics-data-api:avg_order_value" : {
        title: "google_analytics.avg_order_value",
        description: "google_analytics.avg_order_value-description",
        format_type: "currency",
    },
    "google-analytics-data-api:conversion_rate" : {
        title: "google_analytics.conversion_rate",
        description: "google_analytics.conversion_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:revenue_per_user" : {
        title: "google_analytics.revenue_per_user",
        description: "google_analytics.revenue_per_user-description",
        format_type: "currency",
    },
    "google-analytics-data-api:engagement_rate" : {
        title: "google_analytics.engagement_rate",
        description: "google_analytics.engagement_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:new_customer_rate" : {
        title: "google_analytics.new_customer_rate",
        description: "google_analytics.new_customer_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:revenue_per_session" : {
        title: "google_analytics.revenue_per_session",
        description: "google_analytics.revenue_per_session-description",
        format_type: "currency",
    },
    "google-analytics-data-api:add_to_cart_rate" : {
        title: "google_analytics.add_to_cart_rate",
        description: "google_analytics.add_to_cart_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:cart_abandonment_rate" : {
        title: "google_analytics.cart_abandonment_rate",
        description: "google_analytics.cart_abandonment_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:engaged_conversion_rate" : {
        title: "google_analytics.engaged_conversion_rate",
        description: "google_analytics.engaged_conversion_rate-description",
        format_type: "percent",
    },
    "google-analytics-data-api:purchase_frequency" : {
        title: "google_analytics.purchase_frequency",
        description: "google_analytics.purchase_frequency-description",
    },
    "google-analytics-data-api:user_engagement_duration_mins" : {
        title: "google_analytics.user_engagement_duration_mins",
        description: "google_analytics.user_engagement_duration_mins-description",
    },
    "google-analytics-data-api:average_engagement_time" : {
        title: "google_analytics.average_engagement_time",
        description: "google_analytics.average_engagement_time-description",
    },
    "google-analytics-data-api:average_purchase_revenue" : {
        title: "google_analytics.average_purchase_revenue",
        description: "google_analytics.average_purchase_revenue-description",
    },
    "google-analytics-data-api:page_views_per_session" : {
        title: "google_analytics.page_views_per_session",
        description: "google_analytics.page_views_per_session-description",
    },
    "google-analytics-data-api:purchase_conversion_rate" : {
        title: "google_analytics.purchase_conversion_rate",
        description: "google_analytics.purchase_conversion_rate-description",
        format_type: "percent",
    }
}

const googleAnalyticsMetrics = Object.keys(googleAnalyticsSlugs).reduce((acc, key) => {
    let keyParts = key.split(":");
    acc[key] = {
        id: key,
        source: keyParts[0],
        accessor: key,
        title : googleAnalyticsSlugs[key].title ?? keyParts[1],
        description: googleAnalyticsSlugs[key].description ?? "",
        format_type: googleAnalyticsSlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default googleAnalyticsMetrics;