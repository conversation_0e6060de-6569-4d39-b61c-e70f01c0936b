const facebookAdsSlugs = {
    "facebook-marketing:spend": {
        title: "facebook_marketing.spend",
        description: "facebook_marketing.spend-description",
        format_type: "currency",
    },
    "facebook-marketing:clicks": {
        title: "facebook_marketing.clicks",
        description: "facebook_marketing.clicks-description",
    },
    "facebook-marketing:impressions": {
        title: "facebook_marketing.impressions",
        description: "facebook_marketing.impressions-description",
    },
    "facebook-marketing:link_click": {
        title: "facebook_marketing.link_click",
        description: "facebook_marketing.link_click-description",
    },
    "facebook-marketing:add_to_cart": {
        title: "facebook_marketing.add_to_cart",
        description: "facebook_marketing.add_to_cart-description",
    },
    "facebook-marketing:purchase": {
        title: "facebook_marketing.purchase",
        description: "facebook_marketing.purchase-description",
    },
    "facebook-marketing:landing_page_view": {
        title: "facebook_marketing.landing_page_view",
        description: "facebook_marketing.landing_page_view-description",
    },
    "facebook-marketing:website_lead": {
        title: "facebook_marketing.website_lead",
        description: "facebook_marketing.website_lead-description",
    },
    "facebook-marketing:website_view_content": {
        title: "facebook_marketing.website_view_content",
        description: "facebook_marketing.website_view_content-description",
    },
    "facebook-marketing:website_add_to_cart": {
        title: "facebook_marketing.website_add_to_cart",
        description: "facebook_marketing.website_add_to_cart-description",
    },
    "facebook-marketing:website_initiate_checkout": {
        title: "facebook_marketing.website_initiate_checkout",
        description: "facebook_marketing.website_initiate_checkout-description",
    },
    "facebook-marketing:website_purchase": {
        title: "facebook_marketing.website_purchase",
        description: "facebook_marketing.website_purchase-description",
    },
    "facebook-marketing:app_view_content": {
        title: "facebook_marketing.app_view_content",
        description: "facebook_marketing.app_view_content-description",
    },
    "facebook-marketing:app_add_to_cart": {
        title: "facebook_marketing.app_add_to_cart",
        description: "facebook_marketing.app_add_to_cart-description",
    },
    "facebook-marketing:app_initiate_checkout": {
        title: "facebook_marketing.app_initiate_checkout",
        description: "facebook_marketing.app_initiate_checkout-description",
    },
    "facebook-marketing:app_purchase": {
        title: "facebook_marketing.app_purchase",
        description: "facebook_marketing.app_purchase-description",
    },
    "facebook-marketing:add_to_cart_value": {
        title: "facebook_marketing.add_to_cart_value",
        description: "facebook_marketing.add_to_cart_value-description",
        format_type: "currency",
    },
    "facebook-marketing:website_purchase_value": {
        title: "facebook_marketing.website_purchase_value",
        description: "facebook_marketing.website_purchase_value-description",
        format_type: "currency",
    },
    "facebook-marketing:app_purchase_value": {
        title: "facebook_marketing.app_purchase_value",
        description: "facebook_marketing.app_purchase_value-description",
        format_type: "currency",
    },
    "facebook-marketing:roas": {
        title: "facebook_marketing.roas",
        description: "facebook_marketing.roas-description",
        format_type: "roas",
    },
    "facebook-marketing:cpc_link": {
        title: "facebook_marketing.cpc_link",
        description: "facebook_marketing.cpc_link-description",
        format_type: "currency",
    },
    "facebook-marketing:cpc_all": {
        title: "facebook_marketing.cpc_all",
        description: "facebook_marketing.cpc_all-description",
        format_type: "currency",
    },
    "facebook-marketing:ctr_link": {
        title: "facebook_marketing.ctr_link",
        description: "facebook_marketing.ctr_link-description",
        format_type: "percent",
    },
    "facebook-marketing:ctr_all": {
        title: "facebook_marketing.ctr_all",
        description: "facebook_marketing.ctr_all-description",
        format_type: "percent",
    },
    "facebook-marketing:cost_per_purchase": {
        title: "facebook_marketing.cost_per_purchase",
        description: "facebook_marketing.cost_per_purchase-description",
        format_type: "currency",
    },
    "facebook-marketing:cost_per_website_purchase": {
        title: "facebook_marketing.cost_per_website_purchase",
        description: "facebook_marketing.cost_per_website_purchase-description",
        format_type: "currency",
    },
    "facebook-marketing:cost_per_add_to_cart": {
        title: "facebook_marketing.cost_per_add_to_cart",
        description: "facebook_marketing.cost_per_add_to_cart-description",
        format_type: "currency",
    }
}

const facebookAdsMetrics = Object.keys(facebookAdsSlugs).reduce((acc, key) => {
    let keyParts = key.split(":");
    acc[key] = {
        id: key,
        sources: [keyParts[0]],
        accessor: key,
        title : facebookAdsSlugs[key].title ?? key,
        description: facebookAdsSlugs[key].description ?? "",
        format_type: facebookAdsSlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default facebookAdsMetrics;