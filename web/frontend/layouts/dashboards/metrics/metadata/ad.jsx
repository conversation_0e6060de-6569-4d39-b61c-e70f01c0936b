const adSlugs = {
    "ad:spend" : {
        title : "ad.spend",
        description: "ad.spend-desc",
        format_type: "currency"
    },
    "ad:impressions" : {
        title : "ad.impressions",
        description: "ad.impressions-desc"
    },
    "ad:clicks" : {
        title : "ad.clicks",
        description: "ad.clicks-desc"
    },
    "ad:ctr" : {
        title : "ad.ctr",
        description: "ad.ctr-desc",
        format_type: "percent"
    },
    "ad:cpc" : {
        title : "ad.cpc",
        description: "ad.cpc-desc",
        format_type: "currency"
    },
    "ad:cac" : {
        title : "ad.cac",
        description: "ad.cac-desc",
        format_type: "currency"
    },
    "ad:roas" : {
        title : "ad.roas",
        description: "ad.roas-desc",
        format_type: "roas"
    },
    "ad:cpm" : {
        title : "ad.cpm",
        description: "ad.cpm-desc",
        format_type: "currency"
    },
    "ad:order_rate" : {
        title : "ad.order_rate",
        description: "ad.order_rate-desc",
        format_type: "percent"
    },
    "ad:conversions_value" : {
        title : "ad.conversions_value",
        description: "ad.conversions_value-desc",
        format_type: "currency"
    },
    "ad:conversions" : {
        title : "ad.conversions",
        description: "ad.conversions-desc"
    },
    "ad:aov" : {
        title : "ad.aov",
        description: "ad.aov-desc",
        format_type: "currency"
    }
}

const adsMetrics = Object.keys(adSlugs).reduce((acc, key) => {
    acc[key] = {
        id: key,
        sources: [],
        accessor: key,
        title : adSlugs[key].title ?? key,
        description: adSlugs[key].description ?? "",
        format_type: adSlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default adsMetrics;