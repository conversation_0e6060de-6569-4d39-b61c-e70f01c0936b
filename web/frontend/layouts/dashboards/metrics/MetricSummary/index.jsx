// @mui material components
import React, {useState, useMemo, useEffect} from "react";
// Material Dashboard 2 PRO React examples
import {tracker, useCancellableAxios, useMaterialUIController} from "@/context";
import { useTranslation } from "react-i18next";
import MetricGrid from "@/layouts/dashboards/metrics/MetricGrid";
import {toast} from "react-toastify";
import {getMetricBySlug} from "@/layouts/dashboards/metrics/metadata";
import dayjs from 'dayjs';

const summary_config = {
    "facebook-marketing": {
        title: "account-summary",
        metrics: [
            "facebook-marketing:spend",
            "facebook-marketing:impressions",
            "facebook-marketing:link_click",
            "facebook-marketing:ctr_all",
    
            "facebook-marketing:cpc_link",
            "facebook-marketing:website_purchase",
            "facebook-marketing:roas",
            "facebook-marketing:cost_per_website_purchase"
    
            // "facebook-marketing:website_purchase_value",
            // "facebook-marketing:ctr_all",
    
        ],
        feature: "facebook_ads_overview",
        report_type: "metrics_summary"
    },
    "shopify": {
        title: "sales",
        metrics: [
            "shopify:total_price",
            "shopify:order_count",
            "shopify:cust_count",
            "shopify:aov",
            // "shopify:new_cust_aov",
            // "shopify:returning_cust_aov",
            // "shopify:new_cust_count_pct",
            // "shopify:returning_cust_count_pct"
        ],
        feature: "",
        report_type: "metrics_summary"
    },
    "blended" : {
        title: "section-blended-ads",
        metrics: [
            "blended:spend",
            "shopify:total_price",
            "shopify:new_cust_count",
            "blended:cac",

            "blended:clicks",
            "blended:ctr",
            "blended:cpc",
            "blended:roas"
        ],
        feature: "blended_ads_overview",
        report_type: "metrics_summary"
    },
    "google-ads": {
        title: "account-summary",
        metrics: [
            "google-ads:cost",
            "google-ads:impressions",
            "google-ads:clicks",
            "google-ads:ctr",

            "google-ads:cpc",
            "google-ads:conversions",
            "google-ads:roas",
            "google-ads:cost_per_conversion",

            // "google-ads:avg_conversion_value",
            // "google-ads:engagements",
            // "google-ads:revenue_per_click",
            // "google-ads:order_rate"
        ],
        feature: "google_ads_overview",
        report_type: "metrics_summary"
    }, 
    "google-analytics-data-api": {
        title: "account-summary",
        metrics: [
            "google-analytics-data-api:total_users",
            "google-analytics-data-api:sessions",
            "google-analytics-data-api:engaged_sessions",
            "google-analytics-data-api:conversions",
            "google-analytics-data-api:total_items_added_to_cart",
            "google-analytics-data-api:ecommerce_purchases",
            "google-analytics-data-api:total_revenue",
            "google-analytics-data-api:bounce_rate",
            "google-analytics-data-api:average_engagement_time",
            "google-analytics-data-api:conversion_rate",
            "google-analytics-data-api:revenue_per_user",
            "google-analytics-data-api:cart_abandonment_rate",
        ],
        feature: "google_analytics_overview",
        report_type: "metrics_summary"
    }
}

export default function MetricSummary({integrationRequestIds, reportLink, footerHelp, preset}) {

    const [metricData, setMetricData] = useState({});

    const [loading, setLoading] = useState(true);
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig} = controller;
    const {start_date, end_date, compare_start_date, compare_end_date, compare, time_frame} = selectedFilters;
    const [shouldTrack, setShouldTrack] = useState(false)

    let metrics = summary_config[preset].metrics;
    let feature = summary_config[preset].feature;
    let report_type = summary_config[preset].report_type;
    let title = summary_config[preset].title;

    const dates = useMemo(() => ({
      compare,
      start_date,
      end_date,
      compare_start_date,
      compare_end_date
    }), [compare, start_date, end_date, compare_start_date, compare_end_date]);

    const axiosInstance = useCancellableAxios();
    const fetchAccountMetrics = () => {
        let reqData = {
            type: report_type,
            filters : {
                start_date : dayjs(dates.start_date).format("YYYY-MM-DD"),
                end_date : dayjs(dates.end_date).format("YYYY-MM-DD"),
                compare: dates.compare,
                compare_start_date: dayjs(dates.compare_start_date).format("YYYY-MM-DD"),
                compare_end_date: dayjs(dates.compare_end_date).format("YYYY-MM-DD"),
                time_frame,
                integration_request_ids: integrationRequestIds,
                metrics : metrics,
            },
        };

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }
    
        setLoading(true);
        axiosInstance.post('/api/report/data', reqData)
            .then((response) => {
                if (!response.data || response.data.error) {
                    toast.error(t("something-went-wrong"));
                    return;
                }

                setMetricData(response.data?.data ?? {});
                setShouldTrack(true)
                setLoading(false);
            })
            .catch((error) => {
                console.log(error)
                setLoading(false);
          })
    }

    useEffect(() => {
        fetchAccountMetrics();
        return () => {
            setLoading(false);
            setShouldTrack(false);
            setMetricData({});
        };

    }, [dates, time_frame, selectedShop, integrationRequestIds]);

    let metricList = metrics.map(m => getMetricBySlug(m))
    const {t} = useTranslation();
    return (
        <MetricGrid
            metricList={metricList}
            reportLink={reportLink}
            title={title ?? t("account-summary")}
            footerHelp={footerHelp}
            feature={feature}
            loading={loading}
            metricData={metricData}
        />
    );
}