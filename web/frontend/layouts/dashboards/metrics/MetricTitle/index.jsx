import React from "react";
import { useTranslation } from "react-i18next";

import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

import logoShopify from "@/assets/images/shopify-logo.svg";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import {SOURCE_FB, SOURCE_SHOPIFY, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS, getMetricBySlug} from "@/layouts/dashboards/metrics/metadata"

const metricStyle = {
  // whiteSpace: "nowrap",
  overflow: "hidden",
  textOverflow: "ellipsis",
  // width: "60%",
  // maxWidth: "80%",
  display: "inline-block"
}

export const sourceLogo = (source) => {
  switch (source) {
    case SOURCE_GOOGLE_ADS:
      return logoGoogleAds;
    case SOURCE_GOOGLE_ANALYTICS:
      return logoGoogleAnalytics;
    case SOURCE_FB:
      return logoFacebook;
    case SOURCE_SHOPIFY:
      return logoShopify;
  }
}

const MetricTitle = ({metric, removeLogoInTitle}) => {

    const {t} = useTranslation();
    const {title, description, sources} = getMetricBySlug(metric);

    if (!description) {
      return <MDTypography variant="button" fontWeight="regular" color="dark" mr={0.4} sx={metricStyle}>{t(title)}</MDTypography>
    }

    let logoImages = (sources ?? []).map(source => sourceLogo(source));
  
    if (removeLogoInTitle) {
        return (
          <MDTooltip className="tooltip" arrow={false} placement="top" title={t(title)} description={t(description)} logos={logoImages}>
              <u><MDTypography variant="caption" color="dark" fontWeight="bold">{t(title)}</MDTypography></u>
          </MDTooltip>
        )
    }
  
    return (
      <MDTooltip placement="top" title={t(title)} description={t(description)} logos={logoImages}>
        <MDBox style={{borderBottom: "1px dotted #ccc", cursor: "default"}} display="flex" alignItems="center" p={0}>
          {logoImages.map((l, ind) => (<MDBox component="img" src={l} alt="shopify" width="1rem" mx={0.1} key={ind} />))}
          <MDTypography component="span" fontWeight="regular" ml={0.4} fontSize="13.5px" color="dark" textTransform="capitalize"
            variant="button" sx={metricStyle}>
              {t(title)}
          </MDTypography>
        </MDBox>
      </MDTooltip>
    )
}

export default MetricTitle;