// @mui material components
import React, {useState, useMemo, useEffect} from "react";
import { useTranslation } from "react-i18next";
import MetricSummary from "@/layouts/dashboards/metrics/MetricSummary";

export default function AccountSummary() {
  const {t} = useTranslation();

  const request_ids = useMemo(() => {
    return []
  }, []);

  return (
      <MetricSummary
          preset="shopify"
          integrationRequestIds={request_ids}
      />
  );
}

