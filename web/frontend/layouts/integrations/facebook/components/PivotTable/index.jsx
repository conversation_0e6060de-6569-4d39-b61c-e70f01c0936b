import React, { useMemo, useState, useEffect } from "react";

// @mui material components
import CircularProgress from "@mui/material/CircularProgress";
import Empty from "@/components/EmptyChart";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import DataTable from "@/examples/Tables/DataTable";

// Sales dashboard components
import DefaultCell from "@/layouts/dashboards/sales/components/DefaultCell";
import { useCancellableAxios, useMaterialUIController } from "@/context";
import {Spin} from "antd";

import Tooltip from '@mui/material/Tooltip';
import {getMetricFormatterFn} from '@/util.jsx';
import {getMetricBySlug} from "@/layouts/dashboards/metrics/metadata";
import { useTranslation } from "react-i18next";
import MDTypography from "@/components/MDTypography";
import MetricTitle from "@/layouts/dashboards/metrics/MetricTitle";
import {toast} from "react-toastify";
import dayjs from "dayjs";
import axios from "axios";

const DIMENSIONS = [
    "facebook-marketing:currency",
    "facebook-marketing:ad_id",
    "facebook-marketing:account_id",
    "facebook-marketing:campaign_id",
    "facebook-marketing:adset_id",
    "facebook-marketing:objective",
    "facebook-marketing:account_currency",
    "facebook-marketing:optimization_goal",
    "facebook-marketing:account_name",
    "facebook-marketing:campaign_name",
    "facebook-marketing:adset_name",
    "facebook-marketing:ad_name",
    "facebook-marketing:buying_type",
    "facebook-marketing:attribution_setting",
    "facebook-marketing:objective_id",
    "facebook-marketing:objective_name",
];


function DataTableRoot({data}) {
    const {t} = useTranslation();

    const aggNodeCell = ({value}) => {

        if (!value) {
            return <DefaultCell> </DefaultCell>
        }

        let name = value
        if (name.length > 30) {
            name = name.substring(0, 25) + "..."
        }

        return (
            <MDBox display="flex" alignItems="center" p={0}>
                <MDBox display="flex" flexDirection="column">
                    <Tooltip title={value}>
                        <u><DefaultCell>
                            {name}
                        </DefaultCell></u>
                    </Tooltip>
                </MDBox>
            </MDBox>
        )
    }


    let currency = ""
    if (data.length > 0) {
        currency = data[0]["facebook-marketing:account_currency"] ?? ""
    }

    const cellMetricFormatter = (m) => {
        return ({value}) => {
            let metric = getMetricBySlug(m)
            const formatter = getMetricFormatterFn(metric.format_type)
            return <DefaultCell>{formatter(value, currency)}</DefaultCell>
        }
    }

    const cellHeader = (slug) => {
        return <MetricTitle metric={slug} removeLogoInTitle={true}/>;
    }

    const cellHeader2 = (val) => {
        return (
            <MDTypography variant="caption" textTransform="capitalize" color="dark" fontWeight="bold">{val}</MDTypography>
        )
    }

    const aggregatedFn = (accessor, renderFn) => {

        const aggregateSumRow = (row) => {

            let rows = row.subRows ?? []
            if (rows.length == 0) {
                return {}
            }

            let responseAgg = {}
            for (var i = 0; i < rows.length; i++) {
                let row = rows[i]
                if (!row.original) {
                    return aggregateSumRow(row)
                }
                let rowData = row.original
                for (var k1 in rowData) {
                    if (DIMENSIONS.indexOf(k1) != -1) {
                        continue
                    }

                    if (!(k1 in responseAgg)) {
                        responseAgg[k1] = 0
                    }

                    responseAgg[k1] += parseFloat(rowData[k1])
                }
            }

            return responseAgg
        }

        return (table) => {
            let responseAgg = {}
            if (table && table.row) {
                responseAgg = aggregateSumRow(table.row)
            }

            responseAgg["facebook-marketing:roas"] = "facebook-marketing:spend" in responseAgg && responseAgg["facebook-marketing:spend"] > 0 ? (responseAgg["facebook-marketing:website_purchase_value"] / responseAgg["facebook-marketing:spend"])  : 0;
            responseAgg["facebook-marketing:cpc_link"] = "facebook-marketing:link_click" in responseAgg && responseAgg["facebook-marketing:link_click"] > 0 ? responseAgg["facebook-marketing:spend"] / responseAgg["facebook-marketing:link_click"] : 0;
            responseAgg["facebook-marketing:ctr_all"] = "facebook-marketing:impressions" in responseAgg && responseAgg["facebook-marketing:impressions"] > 0 ? (100 * responseAgg["facebook-marketing:clicks"] / responseAgg["facebook-marketing:impressions"]) : 0;
            responseAgg["facebook-marketing:ctr_link"] = "facebook-marketing:impressions" in responseAgg && responseAgg["facebook-marketing:impressions"] > 0 ? (100 * responseAgg["facebook-marketing:link_click"] / responseAgg["facebook-marketing:impressions"]) : 0;
            responseAgg["facebook-marketing:cost_per_purchase"] = "facebook-marketing:purchase" in responseAgg && responseAgg["facebook-marketing:purchase"] > 0 ? responseAgg["facebook-marketing:spend"] / responseAgg["facebook-marketing:purchase"] : 0;
            responseAgg["facebook-marketing:cost_per_website_purchase"] = "facebook-marketing:website_purchase" in responseAgg && responseAgg["facebook-marketing:website_purchase"] > 0 ? responseAgg["facebook-marketing:spend"] / responseAgg["facebook-marketing:website_purchase"] : 0;
            responseAgg["facebook-marketing:cost_per_add_to_cart"] = "facebook-marketing:add_to_cart" in responseAgg && responseAgg["facebook-marketing:add_to_cart"] > 0 ? responseAgg["facebook-marketing:spend"] / responseAgg["facebook-marketing:add_to_cart"] : 0;

            return renderFn({value : responseAgg[accessor] ?? 0})
        }
    }

    const dataTableData = {
        columns: [
            { Header: cellHeader2(t("campaign-name")), accessor: "facebook-marketing:campaign_name", className:"first-col-sticky", Cell: aggNodeCell},
            { Header: cellHeader2(t("adset")), accessor: "facebook-marketing:adset_name", Cell: aggNodeCell},
            { Header: cellHeader2("ad"), accessor: "facebook-marketing:ad_name", Cell: aggNodeCell}
        ],
        rows: data
    };

    const metrics = [
        "facebook-marketing:spend",
        "facebook-marketing:impressions",
        "facebook-marketing:link_click",
        "facebook-marketing:ctr_link",
        "facebook-marketing:cpc_link",
        "facebook-marketing:website_purchase",
        "facebook-marketing:website_purchase_value",
        "facebook-marketing:cost_per_website_purchase",
        "facebook-marketing:roas"
    ]

    // pushing the metrics
    metrics.forEach((m) => {
        let clm = { Header: cellHeader(m), accessor: m, className: "right-aln", Cell: cellMetricFormatter(m)}
        if (["facebook-marketing:impressions", "facebook-marketing:link_click", "facebook-marketing:website_purchase", "facebook-marketing:website_purchase_value"].indexOf(m) != -1) {
            clm.aggregate = 'sum'
        } else {
            // TODO - sort not working here
            clm.Aggregated = aggregatedFn(m, cellMetricFormatter(m))
        }
        dataTableData.columns.push(clm)
    })

    if (!data || data.length == 0) {
        return null
    }

    return (
        <DataTable
            canSearch
            isBasic={true}
            table={dataTableData}
            entriesPerPage={{
                defaultValue: 100,
                entries: [10, 25, 50, 100, 200, 500],
            }}
            showTotalEntries={false}
            // isSorted={false}
            // noEndBorder
            initialState={{
                groupBy : ["facebook-marketing:campaign_name", "facebook-marketing:adset_name"]
            }}
        />
    )
}


export default function PivotTable({integrationRequestId}) {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters} = controller;
    const {start_date, end_date} = selectedFilters;

    const [loading, setLoading]  = useState(true)
    const [data, setData] = useState([])
    const [shouldTrack, setShouldTrack] = useState(false)
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();
    const fetchPivotTable = () => {
        let reqData = {
            type: "facebook_pivot_table",
            filters : {
                start_date: dayjs(start_date).format("YYYY-MM-DD"),
                end_date: dayjs(end_date).format("YYYY-MM-DD"),
                integration_request_ids : [integrationRequestId]
            },
        };
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }
    
        setLoading(true);
        axiosInstance.post('/api/report/data', reqData)
            .then((response) => {
                if (!response.data || response.data.error) {
                    toast.error(t("something-went-wrong"));
                    return;
                }

                setData(response.data?.data ?? []);
                setShouldTrack(true)
                setLoading(false);
            })
            .catch((error) => {
                !axios.isCancel(error) && toast.error(t("something-went-wrong"));
                console.log(error)
                setLoading(false);
          })
    }

    useEffect(() => {
        fetchPivotTable();
        return () => {
            setLoading(false);
            setShouldTrack(false);
            setData([]);
        };

    }, [start_date, end_date, selectedShop, integrationRequestId]);

    let dataTable = useMemo(() => {
        return (<DataTableRoot data={data} />)
    }, [data])

    if (!data || data.length == 0) {
        if (loading) {
            return (
                <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="300px" height="100%">
                    <CircularProgress color="secondary" />
                </MDBox>
            )
        }

        return <Empty />;
    }

    return (
        <Spin spinning={loading} indicator={<CircularProgress color="secondary" />}>
            <MDBox>
                <MDBox pt={1}>
                    {dataTable}
                </MDBox>
            </MDBox>
        </Spin>
    );
}