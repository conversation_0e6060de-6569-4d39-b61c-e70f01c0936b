// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import Skeleton from "@mui/material/Skeleton";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React examples
import FunnelTimelineItem from "@/examples/Timeline/FunnelTimelineItem";
import TimelineList from "@/examples/Timeline/TimelineList";


function FunnelOverview(props) {
    let funnel = {}
    if (!!props.funnel && Object.keys(props.funnel).length != 0) {
        funnel = props.funnel
    }

    let loading = props.loading
    let loader = <Skeleton sx={{ bgcolor: "grey.700" }} variant="text" width={60} />

    return (
        <MDBox sx={{ height: "100%" }} >
        {/* <MDBox pt={3} px={3}>
            <MDTypography variant="h6" fontWeight="medium">
            Funnel overview
            </MDTypography>
            <MDBox mt={0}>
            <MDTypography variant="button" color="text" fontWeight="regular">
                <MDTypography display="inline" variant="body2" verticalAlign="middle">
                <Icon sx={{ color: ({ palette: { success } }) => success.main }}>arrow_upward</Icon>
                </MDTypography>
                &nbsp;
                <MDTypography variant="button" color="text" fontWeight="medium">
                24%
                </MDTypography>{" "}
                this month
            </MDTypography>
            </MDBox>
        </MDBox> */}
        <MDBox>
            <TimelineList title="Funnel Overview">
                <FunnelTimelineItem
                    color="success"
                    icon="touch_app"
                    title={!loading ? (funnel.link_click ?? '') : loader}
                    description="Link Clicks"
                />
                <FunnelTimelineItem
                    color="error"
                    icon="preview"
                    title={!loading ? (funnel.view_content ?? '') : loader}
                    description="View Content"
                />
                <FunnelTimelineItem
                    color="info"
                    icon="shopping_cart"
                    title={!loading ? (funnel.add_to_cart ?? '') : loader}
                    description="Add To Cart"
                />
                <FunnelTimelineItem
                    color="warning"
                    icon="credit_card"
                    title={!loading ? (funnel.initiate_checkout ?? '') : loader}
                    description="Initiate Checkout"
                />
                <FunnelTimelineItem
                    color="primary"
                    icon="shopping_bag"
                    title={!loading ? (funnel.purchase ?? '') : loader}
                    description="Purchase"
                    lastItem
                />
            </TimelineList>
        </MDBox>
        </MDBox>
    );
}

export default FunnelOverview;
