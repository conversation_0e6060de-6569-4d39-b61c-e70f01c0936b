import { useState, useEffect } from "react";

// @mui material components
import Grid from "@mui/material/Grid";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import {DashboardLoader} from "@/components/AppLoader";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import IntegrationCard from "@/layouts/integrations/components/IntegrationCard";
import {useMaterialUIController} from "@/context";
import { useSearchParams } from 'react-router-dom'
import { SOURCE_FB, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS, SOURCE_KLAVIYO, SOURCE_SHOPIFY } from "@/layouts/dashboards/metrics/metadata";
import { toast } from 'react-toastify';
import {useTranslation} from "react-i18next";


const SOURCE_TYPES = [
    SOURCE_FB,
    SOURCE_GOOGLE_ADS,
    SOURCE_GOOGLE_ANALYTICS,
    // SOURCE_SHOPIFY,
    // SOURCE_KLAVIYO
];

function IntegrationsDashboard(props) {
    return (
        <DashboardLayout>
            <DashboardNavbar />
            <MDBox mt={5} >
                <Integrations {...props} />
            </MDBox>
        </DashboardLayout>
    );
}

export const Integrations = ({st, request_id, setupMode}) => {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, integrations} = controller;
    const loader = integrations.loading ?? false;
    const { t } = useTranslation();
    const [searchParams, setSearchParams] = useSearchParams();
    let defaultSourceTypes = st ?? searchParams.get('st')
    let authState = searchParams.get('auth')
    useEffect(() => {
      if (!!authState && authState == 'success') {
        toast.success(t("integration.auth-success"));
      } else if (!!authState && authState == 'error') {
        toast.error(t("something-went-wrong"));
      }
    }, []);

    if (!defaultSourceTypes) {
        defaultSourceTypes = SOURCE_TYPES
    } else {
        defaultSourceTypes = defaultSourceTypes.split(',')
    }

    const [sourceTypes, setSourceTypes] = useState(defaultSourceTypes);

    return (
        <MDBox pb={3} width="100%" height="80vh">
            {loader && <MDBox><DashboardLoader /></MDBox>}
            {!loader && sourceTypes.length > 1 && 
                <MDBox>
                    <Grid container spacing={3} display="flex">
                        {sourceTypes.map((sourceType, index) => {
                            return (
                                <Grid item xs={12} md={12} lg={6} sx={{flex: 1}} key={index}>
                                    <MDBox height="100%">
                                        <IntegrationCard
                                            request_id={request_id}
                                            sourceType={sourceType}
                                            sourceIntegrations={!setupMode ? (integrations[sourceType] ?? []) : []} />
                                    </MDBox>
                                </Grid>
                            )
                        })}
                    </Grid>
                </MDBox>
            }
            {!loader && sourceTypes.length == 1 && 
                <MDBox height="100%">
                    <Grid container spacing={3} display="flex" alignItems={"center"} justifyContent={"center"} height="100%">
                        <Grid item lg={3} />
                        <Grid item xs={12} md={12} lg={6} sx={{flex: 1}}>
                            <MDBox mb={1.5} mt={1.5} height="100%">
                                <IntegrationCard
                                    request_id={request_id}
                                    sourceType={sourceTypes[0]}
                                    sourceIntegrations={!setupMode ? (integrations[sourceTypes[0]] ?? []) : []} />
                            </MDBox>
                        </Grid>
                        <Grid item lg={3} />
                    </Grid>
                </MDBox>
            }
        </MDBox>
    );
}

export default IntegrationsDashboard;
