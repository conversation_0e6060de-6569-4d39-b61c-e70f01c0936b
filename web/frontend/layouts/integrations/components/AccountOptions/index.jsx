import React, {useState} from "react";
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React examples
import { useTranslation } from 'react-i18next';
import TextField from '@mui/material/TextField';
import MDBox from "@/components/MDBox";
import Autocomplete from '@mui/material/Autocomplete';
import AddCircleOutlineOutlinedIcon from '@mui/icons-material/AddCircleOutlineOutlined';
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import { SOURCE_FB, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS } from "@/layouts/dashboards/metrics/metadata";

const SOURCE_LOGOS = {
    [SOURCE_GOOGLE_ADS] : logoGoogleAds,
    [SOURCE_GOOGLE_ANALYTICS] : logoGoogleAnalytics,
    [SOURCE_FB] : logoFacebook
}

function AccountOptionsDropdown({integrations, selectedAccount, setSelectedAccount, sourceType}) {
    const {t} = useTranslation();
    let activeOption = {}

    let icon = null;
    if (sourceType in SOURCE_LOGOS) {
        icon = <MDBox component="img" src={SOURCE_LOGOS[sourceType]} alt="logo" width="1rem" mx={0.1} />
    }

    let sourceIntegrations = sourceType in integrations ? integrations[sourceType] : [];
    let optionList = sourceIntegrations.map((intg) => {
        const source_config_id = intg.source_config_id ?? "";
        const source_config_name = intg.source_config_name ?? "";
        let source_name
        if (!!source_config_id && !!source_config_name) {
            source_name = `${source_config_name} (${source_config_id})`;
        } else if (!!source_config_id) {
            source_name = String(source_config_id);
        } else {
            source_name = t("new");
        }

        let option = {label : source_name, value : (intg.request_id ?? ""), icon}
        return option
    });

    optionList.push({label : t("add-new-account"), value : "add-new-account", icon: <AddCircleOutlineOutlinedIcon color="success"/>})

    optionList = optionList.map((option) => {
        if (option.value === selectedAccount) {
            activeOption = option
        }
        return option
    })

    return (
        <Autocomplete
            onChange={(event, newValue) => {
                if (newValue && newValue.value) {
                    setSelectedAccount(newValue.value)
                }
            }}
            getOptionLabel={(option) => {
                return option.label ?? ""
            }}
            id="account-selection"
            blurOnSelect
            isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
            }}
            disableClearable={true}
            options={optionList}
            value={activeOption}
            fullWidth
            sx={{ width: 300 }}
            renderInput={(params) => <TextField {...params} />}
            renderOption={(props, option) => {
                return (
                    <li {...props} key={option.value}>
                        <MDBox display="flex" flexDirection="row" lineHeight={0} alignItems="center">
                            {option.icon}&nbsp;
                            <MDTypography variant="button" color={"dark"}>{option.label}</MDTypography>
                        </MDBox>                            
                    </li>
                );
            }}
        />
    );
}

export default AccountOptionsDropdown;