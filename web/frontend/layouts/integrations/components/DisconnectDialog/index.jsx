import { toast } from "react-toastify";
import {useCancellableAxios, mixpanel, useMaterialUIController, fetchIntegrations} from "@/context";

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import MDTypography from "@/components/MDTypography";
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import MDButton from "@/components/MDButton";
import MDAvatar from "@/components/MDAvatar";
import warningIcon from '@/assets/images/warning-icon.svg';
import MDBox from "@/components/MDBox";
import axios from "axios";
import {useTranslation} from "react-i18next";

const DisconnectDialog = NiceModal.create(({onFinish, request_id, sourceType}) => {
    const modal = useModal();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const {t} = useTranslation();

    const axiosInstance = useCancellableAxios();

    let payload = {request_id}
    if (!!selectedShop) {
        payload.selectedShop = selectedShop;
    }

    const disconnectSource = () => {
        axiosInstance.post('/api/integrations/disconnect', payload ).then((res) => {
            if ('error' in res.data) {
                toast.error(res.data.error);
                onFinish();
            } else {
                toast.success(t("source-disconnected-success"));
                onFinish();
                fetchIntegrations(dispatch, selectedShop);
            }
        }).catch((err) => {
            console.log(err);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"))
            onFinish();
        });
        modal.hide();
    }
  
    return (
        <Dialog
          open={modal.visible}
          onClose={() => {
            modal.hide()
            onFinish();
          }}
          maxWidth="xs"
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">
            {t("are-you-sure")}
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              <MDBox component="p" p={2} display="flex" justifyContent="center" alignItems="center">
                <MDAvatar size="lg" src={warningIcon} alt="warning logo" variant="rounded" color="warning" />
              </MDBox>
                <MDTypography variant="button" color="dark">
                    {t(`disconnecting-${sourceType}`)}
                </MDTypography>
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <MDButton onClick={disconnectSource} variant="outlined" color="info" size="small">
              {t("cancel-subs-alert-confirm")}
            </MDButton>
            <MDButton onClick={() => {onFinish(); modal.hide()}} autoFocus variant="outlined" color="dark" size="small">
              {t("cancel-subs-alert-cancel")}
            </MDButton>
          </DialogActions>
        </Dialog>
    );
  });

  export default DisconnectDialog;