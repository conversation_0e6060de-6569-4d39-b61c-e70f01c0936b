// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import { useTranslation } from "react-i18next";

import MDTypography from "@/components/MDTypography";
import React from "react";
import MDTooltip from "@/components/MDTooltip";
import Icon from "@mui/material/Icon";
// Material Dashboard 2 PRO React examples
import DatePickerAnt from "@/components/Filters/DatePickerFilter";
import {RelativeGroupingFilter} from "@/components/Filters/TimeFrameFilter";
import MetricSummary from "@/layouts/dashboards/metrics/MetricSummary";
import { useMaterialUIController } from "@/context";
import {Element} from 'react-scroll';
import PivotDashboard from "@/layouts/ads/components/PivotDashboard";
import ConnectedAdAccounts from "@/layouts/ads/components/ConnectedAdAccounts";

export default function AccountAnalytics({request_ids, preset}) {
    const { t } = useTranslation();
    const [controller] = useMaterialUIController();
    const {shopConfig} = controller;
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;

    const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
    let tooltip_title = t("p-date-block-title-3-m");
    if (time_limit === "1_year") {
        tooltip_title = t("p-date-block-title-1-yr")
    }

    const footerHelp = (<ConnectedAdAccounts preset={preset}/>);

    return (
        <>
        <MDBox mb={3} mt={2}>
            <Card elevation={0} mb={4} my={4}>
            <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" px={2} pb={2}>
                <Grid item>
                    <MDTypography variant="button" sx={{fontSize:"13px"}}>
                        {t("time-period")} &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                            <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                        </MDTooltip>}
                    </MDTypography>
                    <DatePickerAnt report={preset} enableComparison={true} />
                </Grid>
                <Grid item>
                    <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("group-by")}</MDTypography>
                    <RelativeGroupingFilter report={preset} />
                </Grid>
            </Grid>
            </Card>
        </MDBox>
        <Grid container spacing={3}>
        <Grid item xs={12} md={12}>
          <Element name="account-summary">
            <MetricSummary
              preset={preset}
              footerHelp={footerHelp}
              integrationRequestIds={request_ids}
            />
          </Element>
        </Grid>
        <Grid item xs={12} md={12}>
            <Element name="campaign-performance">
            <Card>
                <PivotDashboard
                  preset={preset}
                  integrationRequestIds={request_ids}
                />
            </Card>
            </Element>
        </Grid>
        </Grid>
      </>
    )
}