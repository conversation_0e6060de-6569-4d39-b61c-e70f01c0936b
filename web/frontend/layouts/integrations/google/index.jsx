// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import React, {useState, useEffect, useMemo} from "react";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import {tracker, useMaterialUIController} from "@/context";
import {DashboardLoader} from "@/components/AppLoader";
import ReviewBar from "@/examples/ReviewBar";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import AccountOptionsDropdown from "@/layouts/integrations/components/AccountOptions";
import AccountAnalytics from "@/layouts/integrations/components/AccountAnalytics";
import {Integrations} from "@/layouts/integrations";
import { SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS } from "@/layouts/dashboards/metrics/metadata";

export function GoogleAds() {
    return <GoogleOverview sourceType={SOURCE_GOOGLE_ADS} />
}

export function GoogleAnalytics() {
    return <GoogleOverview sourceType={SOURCE_GOOGLE_ANALYTICS} /> 
}

export default function GoogleOverview({sourceType}) {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig, integrations} = controller;

  const loader = integrations.loading ?? false;
  const [selectedAccount, setSelectedAccount] = useState("add-new-account");

  let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  let blockConnectMultipleAccounts = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.connect_multiple_accounts ?? false);

  let intgs = sourceType in integrations ? integrations[sourceType] : [];
  useEffect(() => {
    if ((selectedAccount == "add-new-account" && intgs.length > 0) || intgs.length == 1) {
        setSelectedAccount(intgs[0].request_id);
    }
  }, [intgs]);

  let integration = {}
  for (var k in intgs) {
    if (intgs[k].request_id == selectedAccount) {
      integration = intgs[k]
      break;
    }
  }
  let seeDashboard = integration.synced ?? false;

  const handleSetSelectedAccount = (value) => {
    if (value === "add-new-account" && blockConnectMultipleAccounts) {
      tracker.event("Paywall", {feature: "connect_multiple_accounts"});
      NiceModal.show(PaywallDialog, {feature : "connect_multiple_accounts"})
      return false
    }

    setSelectedAccount(value)
  }

  const request_ids = useMemo(() => {
    if (integration.request_id) {
      return [integration.request_id]
    }

    return []
  }, [integration.request_id]);

  return (
      <DashboardLayout>
          <DashboardNavbar />
          <MDBox mt={1.5}>
            {loader && <DashboardLoader />}
            {!loader && intgs.length > 0 && <MDBox display="flex" direction="row" justifyContent="flex-end"> 
              <AccountOptionsDropdown
                  sourceType={sourceType}
                  integrations={integrations}
                  selectedAccount={selectedAccount}
                  setSelectedAccount={handleSetSelectedAccount}
              />
              </MDBox>
            }
            {!loader && !seeDashboard 
              && <Integrations
                    st={sourceType}
                    request_id={integration.request_id ?? ""}
                    setupMode={selectedAccount == "add-new-account"}
                  />}
            {!loader && seeDashboard && <AccountAnalytics request_ids={request_ids} preset={sourceType} />}
          </MDBox>
          {!loader && seeDashboard && <ReviewBar />}
          <Footer />
      </DashboardLayout>
  )
}