import { useState, useEffect } from "react";
import Link from "@mui/material/Link";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useCancellableAxios, tracker, useMaterialUIController } from "@/context";
import MDAvatar from "@/components/MDAvatar";
import klaviyoLogo from '@/assets/images/logos/klaviyo-logo.svg'
import axios from "axios";

const SOURCE_TYPE = "klaviyo";

// integration prop is optional - if not passed then it will assume that klaviyo is not connected
function Onboarding({integration, onSuccess}) {
  const [controller, dispatch] = useMaterialUIController();
  const { selectedShop } = controller;
  const [loaderCreateSource, setLoaderCreateSource] = useState("");
  const { t } = useTranslation();
  const [klaviyoApi, setKlaviyoApi] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  let klaviyoConfigured = !!integration && !!integration.klaviyo_api_key;
  useEffect(() => {   
    setKlaviyoApi(integration?.klaviyo_api_key ?? "");
  }, [integration]);

  const axiosInstance = useCancellableAxios();

  const createSource = () => {
      if (loaderCreateSource === SOURCE_TYPE) {
          return
      }
      if (klaviyoConfigured) {
          toast.error(t("source-already-connected"));
          return;
      }

      let klaviyoApiTrimmed = klaviyoApi.trim();

      let reqData = {
          klaviyo_api_key: klaviyoApiTrimmed,
          source_type: SOURCE_TYPE
      }

      if (!!selectedShop) {
          reqData.selectedShop = selectedShop;
      }
      setLoaderCreateSource(SOURCE_TYPE);
      axiosInstance.post('/api/integration/klaviyo/connect', reqData).then((res) => {
          setLoaderCreateSource("");
          if ('error' in res.data) {
              toast.error(res.data.error);
          } else {
              tracker.event("Source Configured", {
                  acc_id: klaviyoApiTrimmed,
                  source_type: SOURCE_TYPE
              });
              toast.success(t("source-configured-success"));
              onSuccess();
          }
      })
          .catch((err) => {
              console.log(err)
              setLoaderCreateSource("");
              if (err.response?.status == 401) {
                  toast.error(t("Invalid API Key"));
              }
              else {
                !axios.isCancel(err) && toast.error(t("something-went-wrong"))
              }
          });
  }

    return (
        <MDBox width="100%" mx="auto" bgColor="light" py={6}>
        <Grid container spacing={1} justifyContent="center" alignItems="center" height="100%">
          <Grid item xs={12} lg={7}>
          <MDBox mb={4} textAlign="center">
              <MDBox mb={1}>
                <MDBox display="flex" justifyContent="center" alignItems="center" mb={1}>
                  <MDAvatar size="lg" src={klaviyoLogo} alt="klaviyo logo" variant="rounded" />
                </MDBox>
                  <MDTypography variant="h4" fontWeight="medium">
                    {t("configure-klaviyo-title")}
                  </MDTypography>
              </MDBox>
              <MDTypography variant="h6" fontWeight="regular" color="secondary">
                {t("configure-klaviyo-description")}
              </MDTypography>
            </MDBox>
            <Card>
              <MDBox p={2}>
              <MDBox>
                <MDBox width="80%" textAlign="center" mx="auto" my={4}>
                    <MDButton
                      variant="outlined"
                      color={"dark"}
                      component={Link}
                      href={"https://www.klaviyo.com/create-private-api-key?scopes=accounts:read,profiles:read,profiles:write,campaigns:read,flows:read,lists:write,lists:read,segments:read,segments:write,metrics:read,events:read,templates:read,subscriptions:read"}
                      target="_blank"
                      >
                        {t("create-klaviyo-key")}&nbsp;
                        <Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                    </MDButton>

                    <br/>

                    <MDTypography variant="button" color="secondary" textTransform="uppercase" fontWeight="medium">
                        {t("or")}
                    </MDTypography>

                    <br/>

                    <MDBox width="100%" display="flex" justifyContent="center" flexDirection="column" alignItems="center">
                          <MDTypography variant="button" color="dark" fontWeight="regular">
                              <span dangerouslySetInnerHTML={{__html: t("new-klaviyo-key")}} />
                          </MDTypography>
                          <MDTypography variant="caption" color="text">
                          <Link
                              color="info"
                              href="https://help.klaviyo.com/hc/en-us/articles/7423954176283#h_01HDKDXQA1ZGSRCGM041B507KG"
                              style={{ textDecoration: "underline" }}
                              target="_blank"><u>{t("learn-more")}</u>&nbsp; <Icon fontSize="small">arrow_outward</Icon></Link>.
                        </MDTypography>
                    </MDBox>
                </MDBox>
                <MDBox mt={2} mx={4}>
                    <MDBox pb={3}>
                        <MDBox mb={2}>
                            <MDInput
                                type={showPassword ? "text" : "password"}
                                // disabled={loaderCreateSource == SOURCE_TYPE || klaviyoConfigured}
                                onChange={(e) => setKlaviyoApi(e.target.value)}
                                value={klaviyoApi}
                                label={t("klaviyo-api-key")}
                                placeholder="pk_111111111111111111111111"
                                variant="standard"
                                InputProps={{
                                  disabled : (loaderCreateSource == SOURCE_TYPE || klaviyoConfigured),
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton
                                        aria-label="toggle password visibility"
                                        onClick={handleClickShowPassword}
                                        edge="end"
                                      >
                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                                fullWidth />
                        </MDBox>
                        <MDButton
                            color={klaviyoConfigured ? "success" : "info"}
                            fullWidth
                            disabled={klaviyoConfigured || klaviyoApi == ""}
                            variant="gradient"
                            onClick={createSource}
                        >
                            
                            {loaderCreateSource == SOURCE_TYPE
                                ? <CircularProgress size={20} color="white" />
                                : (klaviyoConfigured ? <><CheckCircleOutlineIcon fontSize="medium"/>&nbsp;{t("saved")}</> : t("save"))
                            }
                        </MDButton>
                    </MDBox>
                </MDBox>
            </MDBox>
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
  )
}

export default Onboarding;
