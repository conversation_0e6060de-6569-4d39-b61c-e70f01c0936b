import React, { useState, useEffect } from "react";
// Material UI components
import Grid from "@mui/material/Grid";

// Material Dashboard components
import MDBox from "@/components/MDBox";
import { DashboardLoader } from "@/components/AppLoader";
import { toast } from "react-toastify";
import { useSearchParams } from "react-router-dom";

// Dashboard layout components
import PageLayout from "@/examples/LayoutContainers/PageLayout";
import Breadcrumbs from "@/examples/Breadcrumbs";
import Footer from "@/examples/Footer";

// Import Sidenav from settings
import Sidenav from "@/layouts/pages/account/settings/components/Sidenav";

// Workspace components
import WorkspaceHeader from "@/layouts/workspaces/WorkspaceHeader";
import ConnectedShops from "@/layouts/pages/account/settings/components/ConnectedShops";
import WorkspaceMembers from "@/layouts/pages/account/settings/components/Members";
import Support from "@/layouts/pages/account/settings/components/Support";

// Context and utilities
import { useMaterialUI<PERSON>ontroller } from "@/context";
import { useTranslation } from "react-i18next";
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "../../constants";
import { axiosInstance } from "@/context";

// Define workspace management options
const workspaceOptions = [
  { id: "workspace-stores", icon: "add_business", label: "workspace stores" },
  { id: "workspace-members", icon: "group", label: "workspace members" },
  { id: "support", icon: "support", label: "support-settings-title" },
];

const WorkspaceManagement = () => {
  const { t } = useTranslation();
  const [controller] = useMaterialUIController();
  const { shopConfig, loginConfig, selectedWorkspaceId } = controller;

  const [selectedOption, setSelectedOption] = useState("workspace-stores");
  const [workspaceDetails, setWorkspaceDetails] = useState(null);
  const [workspaceLoading, setWorkspaceLoading] = useState(true);

  const [searchParams] = useSearchParams();
  const selectedOpt = searchParams.get("opt");

  const fetchWorkspaceDetails = async () => {
    try {
      setWorkspaceLoading(true);
      const response = await axiosInstance.post('/api/workspace/details', {
        workspace_id: selectedWorkspaceId,
      });

      if (response.data && response.data.status) {
        setWorkspaceDetails(response.data.data);
      }

    } catch (error) {
      console.error('Failed to fetch workspace details:', error);
      toast.error(t("something-went-wrong"));
    } finally {
      setWorkspaceLoading(false);
    }
  };

  useEffect(() => {
    if (!loginConfig.loading) {
      fetchWorkspaceDetails();
    }
  }, [selectedWorkspaceId]);

  useEffect(() => {
    if (
      selectedOpt &&
      ["workspace-stores", "workspace-members", "support"].includes(selectedOpt)
    ) {
      setSelectedOption(selectedOpt);
    }
  }, [selectedOpt]);

  // Filter options based on user role for workspace members
  const filteredOptions = workspaceOptions.filter((option) => {
    if (option.id === "workspace-members") {
      // Check if user is admin - either by role or admin flag
      return (
        loginConfig.admin ||
        !loginConfig.userData.role ||
        (loginConfig.userData.role &&
          [WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(loginConfig.userData.role))
      );
    }
    return true;
  });

  const isLoading = shopConfig.loading || workspaceLoading || loginConfig.loading;

  return (
    <PageLayout>
      <MDBox pt={3} px={3}>
        <Breadcrumbs
          icon="home"
          title="Workspace Management"
          route={["Workspace"]}
        />
      </MDBox>
      {!isLoading && (<MDBox mt={6} mb={5} mx={3}>
        <WorkspaceHeader
          workspaceDetails={workspaceDetails}
        />
      </MDBox>)}
      <MDBox mt={3} mb={3} mx={3}>  {/* Added mx={3} for horizontal margin */}
        {isLoading ? (
          <DashboardLoader height="85vh" />
        ) : (
          <Grid container spacing={3}>
            {/* Sidebar using Sidenav component */}
            <Grid item xs={12} lg={2} >
              <Sidenav
                options={filteredOptions}
                setSelectedOption={setSelectedOption}
                selectedOption={selectedOption}
              />
            </Grid>

            {/* Main Content */}
            <Grid item xs={12} lg={10}>
              <MDBox mb={3}>
                <Grid container spacing={3}>
                  {selectedOption === "workspace-stores" && (
                    <Grid item xs={12}>
                      <ConnectedShops
                        workspaceDetails={workspaceDetails}
                        fetchWorkspaceDetails={fetchWorkspaceDetails}
                      />
                    </Grid>
                  )}
                  {selectedOption === "workspace-members" && (
                    <Grid item xs={12}>
                      <WorkspaceMembers
                        workspaceDetails={workspaceDetails}
                        fetchWorkspaceDetails={fetchWorkspaceDetails}
                      />
                    </Grid>
                  )}
                  {selectedOption === "support" && (
                    <Grid item xs={12}>
                      <Support />
                    </Grid>
                  )}
                </Grid>
              </MDBox>
            </Grid>
          </Grid>
        )}
      </MDBox>
      <Footer />
    </PageLayout>
  );
};

export default WorkspaceManagement;
