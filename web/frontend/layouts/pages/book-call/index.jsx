import { useState } from "react";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import { useCalendlyEventListener, InlineWidget } from "react-calendly";


// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";

import avatarImage from "@/assets/images/sumit.jpeg";
import {useTranslation} from "react-i18next";
import {useMaterialUIController, tracker} from "@/context";

function BookCall() {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig} = controller;
    const {t} = useTranslation();

    useCalendlyEventListener({
        onDateAndTimeSelected: () => {
            tracker.mixpanel.track('BookCall Slot Selected');
        },
        onEventScheduled: (e) => {
            tracker.mixpanel.track('BookCall Scheduled', {
                event: e
            });
        },
    });

    let prefillEmail = '';
    let prefillName = '';
    let prefillDomain = '';
    
    if (!!loginConfig.userData && !!loginConfig.userData.email) {
        prefillEmail = loginConfig.userData.email
        prefillName = loginConfig.userData.onboard_name
    } else if (!!loginConfig.shop && !!loginConfig.shop.onboard_email) {
        prefillEmail = loginConfig.shop.onboard_email
        prefillName = loginConfig.shop.onboard_name
        prefillDomain = loginConfig.shop.domain
    }

    return (
        <DashboardLayout>
        <DashboardNavbar />
        <MDBox mt={6} mb={3} pt={0}>
            <Grid container spacing={3} justifyContent="center" alignItems={"center"} pt={0}>
                {false && <Grid item  width={"880px"}>
                    <Card>
                    <MDBox py={3} px={2}>
                    <MDBox display="flex" alignItems="center">
                    <MDBox mr={3}>
                        <MDAvatar size="xxl" src={avatarImage} alt="Sumit" />
                    </MDBox>
                    <MDBox lineHeight={1}>
                        <MDTypography variant="h6" fontWeight="medium" mb={1}>
                            {t("book-call-title")}
                        </MDTypography>
                        <MDBox mb={2}>
                            <MDTypography variant="button" color="dark">
                                {t("book-call-desc")}
                            </MDTypography>
                        </MDBox>
                    </MDBox>
                    </MDBox>
                    </MDBox>
                    </Card>
                </Grid>}

                <Grid item xs={12} p={0} style={{paddingTop: 0}}>
                    <InlineWidget
                        prefill={{
                            email: prefillEmail,
                            name: prefillName,
                            customAnswers: {
                                a2: prefillDomain
                            }
                        }}
                        styles={{
                            height: '700px'
                        }}
                        pageSettings={{
                            backgroundColor: '#ffffff',
                            hideEventTypeDetails: false,
                            hideLandingPageDetails: true,
                            primaryColor: '00a2ff',
                            textColor: '4d5055',
                            padding : 0
                        }}
                        url="https://calendly.com/sumit-growth/30min" />
                </Grid>
            </Grid>
        </MDBox>
        <Footer />
        </DashboardLayout>
    );
}

export default BookCall;
