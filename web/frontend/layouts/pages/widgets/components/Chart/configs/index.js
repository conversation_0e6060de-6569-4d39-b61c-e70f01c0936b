import numeral from 'numeral'

function configs(labels, datasets, customOptions = {}) {
  return {
    data: {
      labels,
      datasets: [...datasets],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          titleColor: "#000",
          titleAlign: "center",
          bodyColor: "#000",
          titleSpacing: 3,
          caretSize	: 0,
          bodySpacing: 5,
          boxPadding: 2,
          borderColor: "rgba(0, 0, 0, 0.2)",
          borderWidth: 2,
          callbacks: {
              label: function(tooltipItems) { 
                  if (customOptions.tooltipLabel) {
                    return customOptions.tooltipLabel(tooltipItems);
                  }
                  return `${tooltipItems.dataset.label ?? ""}: ${customOptions.valFormatter ? customOptions.valFormatter(tooltipItems.raw) : tooltipItems.formattedValue}`;
              },
              title : function(tooltipItems) {
                  if (customOptions.tooltipTitle) {
                    return customOptions.tooltipTitle(tooltipItems);
                  }
                  if (tooltipItems.length > 0) {
                    return tooltipItems[0].label;
                  }
                  return null;
              }
          },
        },
      },
      interaction: {
        intersect: false,
        mode: "index",
      },
      scales: {
        y: {
          grid: {
            drawBorder: true,
            display: true,
            drawOnChartArea: true,
            drawTicks: true,
          },
          ticks: {
            display: true,
            callback: function (value) {
              return numeral(value).format('0.[0]a')
            }
          },
        },
        x: {
          grid: {
            drawBorder: true,
            display: customOptions.xGridDisplay ?? true,
            drawOnChartArea: true,
            drawTicks: true,
          },
          ticks: {
            display: customOptions.xAxisTicks ?? false
          },
        },
      },
    },
  };
}

export default configs;
