import { useRef, useEffect, useState, useMemo } from "react";

// porp-types is a library for typechecking of props
import PropTypes from "prop-types";

// react-chartjs-2 components

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";

// @mui material components
import rgba from "@/assets/theme-dark/functions/rgba";

// Material Dashboard 3 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Chart configurations
import configs from "@/layouts/pages/widgets/components/Chart/configs";

// Material Dashboard 3 PRO React base styles
import colors from "@/assets/theme/base/colors";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend
);

function Chart({ title, count, percentage, chart, customHeight }) {
  const chartRef = useRef(null);
  const [chartData, setChartData] = useState({});
  const { data, options } = chartData;

  useEffect(() => {
    const chartDatasets = chart.datasets.map((dataset) => ({
      ...dataset,
      tension: 0.4,
      pointRadius: 0,
      borderWidth: 2,
      borderColor: colors[dataset.color].main,
      fill: true,
      maxBarThickness: 6,
      backgroundColor: rgba(colors[dataset.color].main, 0.03),
    }));

    setChartData(configs(chart.labels, chartDatasets, chart.customOptions ?? {}));
  }, [chart]);

  return (
    <>
      {!!title && !!count && <MDBox p={2} lineHeight={1}>
        {title !== undefined && <MDTypography variant="button" textTransform="capitalize" fontWeight="medium" color="text">
          {title}
        </MDTypography>}
        {count !== undefined && <MDTypography variant="h5" fontWeight="bold" color="dark">
          {count}&nbsp;
          {!!percentage && <MDTypography variant="button" fontWeight="bold" color={percentage.color}>
            {percentage.label}
          </MDTypography>}
        </MDTypography>}
      </MDBox>}
      {useMemo(
        () => (
          <MDBox ref={chartRef} sx={{ height: !!customHeight ? customHeight : "5.375rem", transform: "scale(1.02)" }}>
            {data && <Line data={data} options={options} />}
          </MDBox>
        ),
        [chartData]
      )}
    </>
  );
}

// Typechecking props for the Chart
Chart.propTypes = {
  title: PropTypes.string,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  percentage: PropTypes.shape({
    color: PropTypes.oneOf(["primary", "secondary", "info", "success", "warning", "error", "dark"]),
    label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  }),
  chart: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.array, PropTypes.object])).isRequired,
};

export default Chart;
