import { useState, useEffect } from "react";

// @mui material components
import Container from "@mui/material/Container";

// Material Dashboard 2 PRO React examples
import {DashboardLoader} from "@/components/AppLoader";

// Pricing page components
import Header from "@/layouts/pages/pricing-page/components/Header";
import Footer from "@/layouts/pages/pricing-page/components/Footer";
import PricingCards from "@/layouts/pages/pricing-page/components/PricingCards";
import TrustedBrands from "@/layouts/pages/pricing-page/components/TrustedBrands";
import Faq from "@/layouts/pages/pricing-page/components/Faq";
import {useMaterialUIController, tracker} from "@/context";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import { useTranslation } from "react-i18next";
import Dialog from '@mui/material/Dialog';
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { toast } from "react-toastify";
import Divider from "@mui/material/Divider";
import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

const CouponDialog = NiceModal.create(({couponCode, setCouponCode}) => {
  const modal = useModal();
  const {t} = useTranslation();
  const [coupon, setCoupon] = useState(couponCode);

  const removeCoupon = () => {
    setCouponCode("");
    setCoupon("");
    toast.success(t("coupon-removed"));
    modal.hide();
  }

  const applyCoupon = () => {
    setCouponCode(coupon);
    modal.hide();
  }

  return (
      <Dialog
        open={modal.visible}
        onClose={() => {
          modal.hide();
        }}
        maxWidth="xs"
        fullWidth
      >
      <Card>
      <MDBox m={2}>
        <MDBox mb={2}>
          <MDTypography variant="h6" fontWeight="bold" color="dark">
            {t("i-hav-coupon")}
          </MDTypography>
        </MDBox>
          <MDBox pb={3}>
            <MDInput
                type="text"
                onChange={(e) => {setCoupon(e.target.value)}}
                value={coupon}
                label={t("coupon-code")}
                variant="standard"
                fullWidth />
          </MDBox>
          <MDBox display="flex" flexDirection="row" justifyContent="space-between" alignItems="center">
            <MDButton
                color="error"
                size="small"
                fullWidth
                disabled={coupon == ""}
                variant="outlined"
                onClick={removeCoupon}
            >
              {t("remove")}
            </MDButton>
            <Divider orientation="vertical" flexItem />
            <MDButton
                color="info"
                size="small"
                fullWidth
                disabled={coupon == ""}
                variant="gradient"
                onClick={applyCoupon}
            >
              {t("apply")}
            </MDButton>
          </MDBox>
      </MDBox>
      </Card>
      </Dialog>
  );
});

export const PricingCardsDialog = NiceModal.create(({onExit}) => {
  const modal = useModal();
  const [tabValue, setTabValue] = useState(0);
  const [controller] = useMaterialUIController();
  const { shopConfig, selectedShop } = controller;
  const [couponCode, setCouponCode] = useState("");
  const {t} = useTranslation();


  const handleSetTabValue = (event, newValue) => {
    tracker.event("Switch PricingGroup", {
      report: "pricing",
      old_value: tabValue,
      new_value: newValue
    });

    setTabValue(newValue);
  };

  return (
    <Dialog
      open={modal.visible}
      onClose={() => {
        modal.hide()
        if (onExit) {
          onExit();
        }
      }}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      maxWidth="xl"
    >
        <MDBox width="100%" bgColor="light" variant="gradient" p={2}>
          <MDBox minWidth={{ xs: "22rem", md: "25rem" }} mx={"auto"} mt={1} width="50%">
            <AppBar position="static">
              <Tabs value={tabValue} onChange={handleSetTabValue}>
                <Tab
                  id="monthly"
                  label={
                    <MDBox py={0.5} px={2} color="inherit">
                      {t("p-monthly")}
                    </MDBox>
                  }
                />
                <Tab
                  id="annual"
                  label={
                    <MDBox py={0.5} px={2} color="inherit" display="flex" alignItems="center">
                        {t("annual")}&nbsp;<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="#33c6ab" viewBox="0 0 256 256"><path d="M96,104a8,8,0,1,1,8-8A8,8,0,0,1,96,104Zm64,48a8,8,0,1,0,8,8A8,8,0,0,0,160,152Zm80-24c0,10.44-7.51,18.27-14.14,25.18-3.77,3.94-7.67,8-9.14,11.57-1.36,3.27-1.44,8.69-1.52,13.94-.15,9.76-.31,20.82-8,28.51s-18.75,7.85-28.51,8c-5.25.08-10.67.16-13.94,1.52-3.57,1.47-7.63,5.37-11.57,9.14C146.27,232.49,138.44,240,128,240s-18.27-7.51-25.18-14.14c-3.94-3.77-8-7.67-11.57-9.14-3.27-1.36-8.69-1.44-13.94-1.52-9.76-.15-20.82-.31-28.51-8s-7.85-18.75-8-28.51c-.08-5.25-.16-10.67-1.52-13.94-1.47-3.57-5.37-7.63-9.14-11.57C23.51,146.27,16,138.44,16,128s7.51-18.27,14.14-25.18c3.77-3.94,7.67-8,9.14-11.57,1.36-3.27,1.44-8.69,1.52-13.94.15-9.76.31-20.82,8-28.51s18.75-7.85,28.51-8c5.25-.08,10.67-.16,13.94-1.52,3.57-1.47,7.63-5.37,11.57-9.14C109.73,23.51,117.56,16,128,16s18.27,7.51,25.18,14.14c3.94,3.77,8,7.67,11.57,9.14,3.27,1.36,8.69,1.44,13.94,1.52,9.76.15,20.82.31,28.51,8s7.85,18.75,8,28.51c.08,5.25.16,10.67,1.52,13.94,1.47,3.57,5.37,7.63,9.14,11.57C232.49,109.73,240,117.56,240,128ZM96,120A24,24,0,1,0,72,96,24,24,0,0,0,96,120Zm77.66-26.34a8,8,0,0,0-11.32-11.32l-80,80a8,8,0,0,0,11.32,11.32ZM184,160a24,24,0,1,0-24,24A24,24,0,0,0,184,160Z"></path></svg>
                    </MDBox>
                  }
                />
              </Tabs>
            </AppBar>
          </MDBox>
          <Container>
            <PricingCards
              tabValue={tabValue}
              setTabValue={setTabValue}
              setCouponCode={setCouponCode}
              paywallMode={true}
              couponCode={couponCode}/>
          </Container>
        </MDBox>
    </Dialog>
  );
});


function PricingPage() {
  const [tabValue, setTabValue] = useState(0);
  const [controller] = useMaterialUIController();
  const { shopConfig, selectedShop } = controller;
  const [couponCode, setCouponCode] = useState("");

  const handleSetTabValue = (event, newValue) => {
    tracker.event("Switch PricingGroup", {
      report: "pricing",
      old_value: tabValue,
      new_value: newValue
    });

    setTabValue(newValue);
  };

  const handleCouponDialog = () => {
      NiceModal.show(CouponDialog, {couponCode, setCouponCode});
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      {shopConfig.loading && <DashboardLoader />}
      {!shopConfig.loading && <Header
        tabValue={tabValue}
        tabHandler={handleSetTabValue}
        couponCode={couponCode}
        handleCouponDialog={handleCouponDialog}
      >
        <Container>
          <PricingCards
            tabValue={tabValue}
            setTabValue={setTabValue}
            paywallMode={false}
            setCouponCode={setCouponCode}
            couponCode={couponCode}/>
          <TrustedBrands />
          <Faq />
        </Container>
      </Header>}
      <Footer />
    </DashboardLayout>
  );
}

export default PricingPage;
