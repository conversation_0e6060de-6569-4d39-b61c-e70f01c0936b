import { useState } from "react";

// @mui material components
import Grid from "@mui/material/Grid";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Pricing page components
import FaqCollapse from "@/layouts/pages/pricing-page/components/FaqCollapse";

import { useTranslation } from "react-i18next";

function Faq() {
  const [collapse, setCollapse] = useState(false);
  const {t} = useTranslation();

  return (
    <MDBox mt={8} mb={6}>
      <Grid container justifyContent="center">
        <Grid item xs={12} md={8}>
          <MDTypography variant="h2" align="center" fontWeight="medium" gutterBottom>
            {t("p-faq")}
          </MDTypography>
          <MDBox mb={2}>
            <MDTypography variant="body2" align="center" color="text">
              {t("p-contact-us")} {" "}  <MDTypography
                component="a"
                href="mailto:<EMAIL>"
                variant="body2"
                fontWeight="regular"
                color="secondary"
              >
              <EMAIL>
              </MDTypography>
            </MDTypography>
          </MDBox>
        </Grid>
        <Grid item xs={12} md={10}>
          {/* <FaqCollapse
            title={t("p-faq-q1")}
            open={collapse === 1}
            onClick={() => (collapse === 1 ? setCollapse(false) : setCollapse(1))}
          >
            {t("p-faq-a1")}
          </FaqCollapse>
          <FaqCollapse
            title={t("p-faq-q2")}
            open={collapse === 2}
            onClick={() => (collapse === 2 ? setCollapse(false) : setCollapse(2))}
          >
            {t("p-faq-a2")}
          </FaqCollapse> */}
          <FaqCollapse
            title={t("p-faq-q3")}
            open={collapse === 3}
            onClick={() => (collapse === 3 ? setCollapse(false) : setCollapse(3))}
          >
            {t("p-faq-a3")}
          </FaqCollapse>
          <FaqCollapse
            title={t("p-faq-q4")}
            open={collapse === 4}
            onClick={() => (collapse === 4 ? setCollapse(false) : setCollapse(4))}
          >
            {t("p-faq-a4")}
          </FaqCollapse>
          <FaqCollapse
            title={t("p-faq-q5")}
            open={collapse === 5}
            onClick={() => (collapse === 5 ? setCollapse(false) : setCollapse(5))}
          >
            {t("p-faq-a5")}
          </FaqCollapse>
          <FaqCollapse
            title={t("p-faq-q6")}
            open={collapse === 6}
            onClick={() => (collapse === 6 ? setCollapse(false) : setCollapse(6))}
          >
            {t("p-faq-a6")}
          </FaqCollapse>
        </Grid>
      </Grid>
    </MDBox>
  );
}

export default Faq;
