// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
// Images
import kradle from "@/assets/images/kradle.webp";
import silvertraq from "@/assets/images/silvertraq.png";
import fashor from "@/assets/images/fashor.png";
import disguise from "@/assets/images/disguise.png";
import shredlights from "@/assets/images/shredlights.png";
import finca from "@/assets/images/finca.png";

import { useTranslation } from "react-i18next";
import { useMaterialUIController } from "@/context";

function PricingCards() {
  const { t } = useTranslation();
  const [controller] = useMaterialUIController();
  const { darkMode, loginConfig } = controller;

  let domain = '';
  if (!!loginConfig.shop && !!loginConfig.shop.myshopify_domain) {
    domain = loginConfig.shop.myshopify_domain
  }

  domain = domain.replace(".myshopify.com", "");

  let brands = [
    { name: "kradlemypet", img: kradle},
    { name: "silvertraq", img: silvertraq },
    { name: "envy-me-fashion", img: fashor },
    { name: "disguise-cosmetics", img: disguise },
    { name: "shredlights", img: shredlights }
  ];

  brands = brands.filter(b => b.name !== domain);
  
  return (
    <MDBox mt={8}>
      <MDBox textAlign="center">
        <MDTypography variant="h6" opacity={0.5}>
          {t("p-trusted-title")}
        </MDTypography>
      </MDBox>
      <MDBox mt={5} ml={{ xs: 0, lg: -8 }}>
        <Grid container spacing={6} display="flex" flexDirection={"row"} alignItems={"center"} justifyContent={"center"}>
          {brands.map(b => {
            return (
              <Grid item xs={6} md={4} lg={2} key={b.name}>
                <MDBox
                  component="img"
                  src={b.img}
                  alt={b.name}
                  style={{
                    filter: "grayscale(1)"
                  }}
                  width={{ xs: "100%", xl: "100%" }}
                  opacity={0.5}
                  mb={3}
                />
              </Grid>
            )
          })}
        </Grid>
      </MDBox>
    </MDBox>
  );
}

export default PricingCards;
