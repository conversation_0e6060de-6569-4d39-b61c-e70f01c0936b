import React, { useEffect, useState } from "react";
// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Material Dashboard 2 PRO React examples
import DefaultPricingCard from "@/examples/Cards/PricingCards/DefaultPricingCard";

// Material Dashboard 2 PRO React context
import { useMaterialUIController, useCancellableAxios, tracker } from "@/context";
import {toast} from "react-toastify";

import { useTranslation } from "react-i18next";

import Dialog from '@mui/material/Dialog';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Widget } from '@typeform/embed-react'
import {CardLoader} from "@/components/AppLoader";
import axios from "axios";

const SusbscriptionCancelDialog = NiceModal.create(({onFinish, shopName}) => {
  const modal = useModal();
  const {t} = useTranslation();
  const axiosInstance = useCancellableAxios();
  
  const [controller, dispatch] = useMaterialUIController();
  const {loginConfig, selectedShop} = controller;
  
  // Randomly choose between the two typeform IDs (50/50 chance) - only once when component mounts
  const [typeformId] = useState(() => Math.random() < 0.5 ? "Seu4jXuK" : "ghA8hzaq");

  const cancelSubscription = () => {

    let reqData = {}
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
    }

    axiosInstance.post("/api/plan/cancel", reqData).then((res) => {
      if (res.data && res.data.done) {
        tracker.event("Plan Cancelled")
        window.location.reload();
      } else if (res.data && res.data.error) {
        tracker.event("Error Occurred", {report: "pricing", error : res.data.error})
        console.error(res.data.error)
        toast.error(res.data.error)
      } else {
        console.log(res);
      }
      onFinish();
    }).catch((err) => {
      console.error(err);
      onFinish();
    })
    modal.hide();
  }

  return (
    <div>
      <Dialog
        open={modal.visible}
        onClose={() => {
          modal.hide()
          onFinish();
        }}
        style={{width: "100%"}}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Widget
        id={typeformId}
        width={500}
        height={500}
        enableSandbox={loginConfig.admin}
        onSubmit={cancelSubscription}
        hidden={{ shop: shopName }}
      />
      {/* <MDButton onClick={cancelSubscription} color="primary">
        {t("cancel-subscription")}
      </MDButton> */}
      </Dialog>
    </div>
  );
});


const ACTION_TYPES = {
  startTrial7d: {
    label: "pricing.start-trial-7d",
    disabled: false,
    hasIcon: true,
    type: "api",
    color: "info"
  },
  upgrade: {
    label: "upgrade",
    disabled: false,
    hasIcon: true,
    type: "api",
    color: "info"
  },
  switchToMonthly: {
    label: "switch-to-monthly", 
    disabled: false,
    hasIcon: false,
    type: "api",
    color: "secondary"
  },
  switchToAnnual: {
    label: "switch-to-annual", 
    disabled: false,
    hasIcon: true,
    type: "api",
    color: "info"
  },
  downgrade: {
    label: "downgrade",
    disabled: false,
    hasIcon: false,
    type: "api",
    color: "secondary"
  },
  active: {
    label: "pricing.current-plan",
    disabled: true,
    hasIcon: false,
    type: "api",
    color: "secondary"
  }
}


function PricingCards({ tabValue, setTabValue, couponCode, setCouponCode, paywallMode}) {
  const [controller] = useMaterialUIController();
  const { darkMode, loginConfig, shopConfig, selectedShop } = controller;
  const axiosInstance = useCancellableAxios();

  const {t} = useTranslation();
  const [loader, setLoader] = React.useState(true);
  const [planLoader, setPlanLoader] = React.useState("");
  const [cancelLoader, setCancelLoader] = React.useState(false);
  const [plans, setPlans] = useState([{}, {}]);
  
  const fetchPlans = async () => {
    setLoader(true)
    try {
      let reqData = {};
      if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
      }
      if (!!couponCode) {
        reqData.coupon = couponCode;
      }
      const response = await axiosInstance.post('/api/plans', reqData);

      setLoader(false)
      if (response.data.error) {
        toast.error(response.data.error)
        return;
      }

      if (response.data && response.data.plans) {
        setPlans(response.data.plans);
      }

      if (response.data && couponCode && ('isCouponValid' in response.data)) {
        if (!response.data.isCouponValid) {
          toast.error(t("invalid-coupon"))
          setCouponCode("");
        } else {
          toast.success(t("coupon-applied"))
        }
      }
      
      if (response.data && response.data.activeTab && !couponCode) {
        setTabValue(response.data.activeTab);
      }
    } catch (error) {
      setLoader(false)
      !axios.isCancel(error) && toast.error(t("something-went-wrong"))
      console.error('Failed to fetch plan details:', error);
    } 
  };

  useEffect(() => {
    fetchPlans();
  }, [couponCode, selectedShop]);


  let subscriptionCancellationHandler = () => {

    if (cancelLoader) {
      return;
    }

    setCancelLoader(true);
    tracker.event("Cancel Plan Clicked")
    NiceModal.show(SusbscriptionCancelDialog, {
      onFinish : () => {
        setCancelLoader(false)
      },
      shopName: shopConfig.shop.name
    })
  }

  let subscriptionHandler = (plan) => {

      if (planLoader === plan) {
        return;
      }

      setPlanLoader(plan);
      tracker.event("Upgrade Plan Clicked", {
        plan: plan,
        coupon: couponCode
      })

      let reqData = {
        plan: plan,
        coupon: couponCode
      }

      if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
      }

      axiosInstance.post("/api/plan/upgrade", reqData).then((res) => {
        setPlanLoader("")
        if (res.data && res.data.redirect) {
          window.location.href = res.data.redirect;
        } else if (res.data && res.data.error) {
          tracker.event("Error Occurred", {report: "pricing", error : res.data.error})
          console.error(res.data.error)
          toast.error(res.data.error)
        } else {
          console.log(res);
        }
      }).catch((err) => {
        setPlanLoader("");
        console.error(err);
      })
  }

  const planToCard = (planName, planRender) => {
    let isFreePlan = planRender.display_price == 0;
    const specifications = [
      ...((planRender.prev_features ?? []).map((feature) => ({
          label: t(feature),
          includes: true,
          highlight: true,
          remove_icon: true
      })) || []),
      ...((planRender.limited_features ?? []).map((feature) => ({
          label: t(feature),
          includes: false
      })) || []),
      ...((planRender.features ?? []).map((feature) => ({
          label: t(feature),
          includes: true,
          understate: isFreePlan
      })) || [])
    ];

    const price = isFreePlan
      ? { value: t("free"), type: "text" }
      : {
          currency: "$",
          value: planRender.display_price,
          type: planRender.interval_type,
          ...(planRender.strike_price && { strikeValue: planRender.strike_price }),
          subtext: planRender.interval_type == "year" && `${t("pricing.annual-offer")}`
      };

    const planAction = ACTION_TYPES[planRender.action] ?? {};
    const isRecommended = planRender.isRecommended ?? false;

    let actionHandler = () => subscriptionHandler(planName);
    let actionLoader = planLoader === planName;

    if (planRender.action == "active") {
      actionHandler = () => {};
      actionLoader = false;
    }

    if (planRender.action == "downgrade" && isFreePlan) {
      actionHandler = subscriptionCancellationHandler;
      actionLoader = cancelLoader;
    }

    const action = {
      ...planAction,
      label: t(planAction?.label),
      handler: actionHandler,
      loader: actionLoader
    }

    return (
      <Grid item xs={12} lg={4} key={planName}>
          <DefaultPricingCard
              color={isRecommended ? "dark" : "white"}
              badge={{ color: darkMode ? "warning" : "light", label: planRender.display_name, hasIcon: !isFreePlan }}
              price={ price }
              specifications={specifications}
              action={action}
              shadow={darkMode}
          />
      </Grid>
    );
  }

  return (
    <>
      {loader && <MDBox px={10}><CardLoader height={"30vh"} /></MDBox>}
      {!loader && <MDBox position="relative" zIndex={10} mt={paywallMode ? 6 : 8} px={{ xs: 1, sm: 0 }} >
        <Grid container spacing={paywallMode ? 3 : 6} justifyContent="center">
          {Object.entries(plans[tabValue]).map(([planName, planRender]) => planToCard(planName, planRender))}
        </Grid>
      </MDBox>}
    </>
  );
}

// Typechecking props for the PricingCards
PricingCards.propTypes = {
  prices: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default PricingCards;
