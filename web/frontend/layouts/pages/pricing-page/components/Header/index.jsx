// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDBadge from "@/components/MDBadge";
import MDTypography from "@/components/MDTypography";

// Images
import bgImage from "@/assets/images/bg-pricing-new.jpg";
import brandDark from "@/assets/images/brand-short-2.png";
import { useTranslation } from "react-i18next";
import premiumTag from "@/assets/images/premium-tag.png";
import MDButton from "@/components/MDButton";
import { useMaterialUIController } from "@/context";
import Icon from "@mui/material/Icon";

function Header({ tabValue, tabHand<PERSON>, children, couponCode, handleCouponDialog }) {
  const [controller] = useMaterialUIController();
  const { shopConfig, selectedShop } = controller;
  const {t} = useTranslation();

  let shopName = shopConfig.shop?.name ?? "";
  let isFreePlan = shopConfig.planDetails?.planType == "free";
  let planName = shopConfig.planDetails?.planName;
  if (shopConfig.planDetails?.render?.display_name) {
      planName = shopConfig.planDetails.render.display_name;
  }
  
  return (
    <>
      <MDBox
        position="relative"
        minHeight="25vh"
        height="25vh"
        borderRadius="xl"
        m={2}
        pt={2}
        sx={{
          backgroundImage: ({ functions: { linearGradient, rgba }, palette: { black } }) =>
            `${linearGradient(rgba(black.main, 0.25), rgba(black.main, 0.25))}, url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "bottom",
        }}
      >
        <Grid
          container
          spacing={3}
          justifyContent="center"
          sx={{ position: "relative", textAlign: "center" }}
        >
          <Grid item xs={11} lg={5} display="flex" flexDirection="column" alignItems="center" pb={3}>
            <MDBox mt={2}>
              <MDBox component="img" src={brandDark} alt="Brand" width="4rem"/>
            </MDBox>
            <MDBox mt={2}>
            <MDTypography variant="h6" color="white" fontWeight="regular">
                {t("p-drive-growth")}
              </MDTypography>
              <MDTypography variant="h6" color="white" fontWeight="regular">
                {t("pricing.tagline")}
              </MDTypography>
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
      <Grid container sx={{ px: 2, my: 8, mt: 20 }}>
        <Grid item xs={12}>
          <Card sx={{ mt: -16 }}>
            {!isFreePlan && !!planName && <MDBox
              bgColor={"grey-300"}
              borderRadius="xl"
              display="flex"
              justifyContent="center"
              alignItems={{ xs: "flex-start", sm: "center" }}
              flexDirection={{ xs: "column", sm: "row" }}
              m={3}
              mt={4}
              p={2.4}
            >
              <MDTypography variant="h6" fontWeight="medium">
                {shopName}
                <MDBadge 
                  badgeContent={
                    <>
                      {planName}
                      <img src={premiumTag} alt="premium" style={{width: "15px", height: "15px", marginLeft:"5px"}}/>
                    </>
                  } 
                  size="lg" 
                  p={0}
                  variant="gradient"
                  color="dark"
                  sx={{color:"white", p: 0}}
                />
              </MDTypography>
            </MDBox>}

            <MDBox display="flex" justifyContent="flex-end" mr={4} my={2}>
              {!couponCode && <MDButton onClick={handleCouponDialog}>
                {t("i-hav-coupon")}
              </MDButton>}
              {!!couponCode && <MDButton onClick={handleCouponDialog} variant="outlined" color="dark" size="small">
                  {t("remove-coupon")} &nbsp; <Icon sx={{fontSize:"15px"}}>cancel</Icon>
              </MDButton>}
            </MDBox>

            <MDBox minWidth={{ xs: "22rem", md: "25rem" }} mx={"auto"} mt={1}>
              <AppBar position="static">
                <Tabs value={tabValue} onChange={tabHandler}>
                  <Tab
                    id="monthly"
                    label={
                      <MDBox py={0.5} px={2} color="inherit">
                        {t("p-monthly")}
                      </MDBox>
                    }
                  />
                  <Tab
                    id="annual"
                    label={
                      <MDBox py={0.5} px={2} color="inherit" display="flex" alignItems="center">
                          {t("annual")}&nbsp;<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="#33c6ab" viewBox="0 0 256 256"><path d="M96,104a8,8,0,1,1,8-8A8,8,0,0,1,96,104Zm64,48a8,8,0,1,0,8,8A8,8,0,0,0,160,152Zm80-24c0,10.44-7.51,18.27-14.14,25.18-3.77,3.94-7.67,8-9.14,11.57-1.36,3.27-1.44,8.69-1.52,13.94-.15,9.76-.31,20.82-8,28.51s-18.75,7.85-28.51,8c-5.25.08-10.67.16-13.94,1.52-3.57,1.47-7.63,5.37-11.57,9.14C146.27,232.49,138.44,240,128,240s-18.27-7.51-25.18-14.14c-3.94-3.77-8-7.67-11.57-9.14-3.27-1.36-8.69-1.44-13.94-1.52-9.76-.15-20.82-.31-28.51-8s-7.85-18.75-8-28.51c-.08-5.25-.16-10.67-1.52-13.94-1.47-3.57-5.37-7.63-9.14-11.57C23.51,146.27,16,138.44,16,128s7.51-18.27,14.14-25.18c3.77-3.94,7.67-8,9.14-11.57,1.36-3.27,1.44-8.69,1.52-13.94.15-9.76.31-20.82,8-28.51s18.75-7.85,28.51-8c5.25-.08,10.67-.16,13.94-1.52,3.57-1.47,7.63-5.37,11.57-9.14C109.73,23.51,117.56,16,128,16s18.27,7.51,25.18,14.14c3.94,3.77,8,7.67,11.57,9.14,3.27,1.36,8.69,1.44,13.94,1.52,9.76.15,20.82.31,28.51,8s7.85,18.75,8,28.51c.08,5.25.16,10.67,1.52,13.94,1.47,3.57,5.37,7.63,9.14,11.57C232.49,109.73,240,117.56,240,128ZM96,120A24,24,0,1,0,72,96,24,24,0,0,0,96,120Zm77.66-26.34a8,8,0,0,0-11.32-11.32l-80,80a8,8,0,0,0,11.32,11.32ZM184,160a24,24,0,1,0-24,24A24,24,0,0,0,184,160Z"></path></svg>
                      </MDBox>
                    }
                  />
                </Tabs>
              </AppBar>
            </MDBox>
            {children}
          </Card>
        </Grid>
      </Grid>
    </>
  );
}

// Typechecking props for the Header
Header.propTypes = {
  tabValue: PropTypes.number.isRequired,
  tabHandler: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};

export default Header;
