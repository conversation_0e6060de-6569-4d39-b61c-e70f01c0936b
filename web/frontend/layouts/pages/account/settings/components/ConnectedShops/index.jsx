import dayjs from 'dayjs';
import { useMemo, useState, useEffect } from "react";
import {Link, useLocation, useNavigate} from "react-router-dom";
import { toast } from 'react-toastify';
import MDTooltip from "@/components/MDTooltip";
import ConfirmationDialog from '@/components/ConfirmationDialog';
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import DatePickerAnt from "@/components/Filters/DatePickerFilter";
// @mui icons
import MDAvatar from "@/components/MDAvatar";
import MDButton from "@/components/MDButton";
import MDBadge from "@/components/MDBadge";
import DeleteIcon from '@mui/icons-material/Delete';
import DataTable from "@/examples/Tables/DataTable";
import Icon from "@mui/material/Icon";
import {AddStoreDialog} from '@/layouts/authentication/add-store';
import premiumTag from "@/assets/images/premium-tag.png";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import NiceModal from '@ebay/nice-modal-react';
import { stringToColor } from "@/util";

import {useCancellableAxios, tracker, useMaterialUIController, fetchLoginConfig, setSelectedShop} from "@/context";
import {useTranslation} from "react-i18next";
import StorefrontTwoToneIcon from '@mui/icons-material/StorefrontTwoTone';
import StorefrontIcon from '@mui/icons-material/Storefront';
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "@/constants";
import { getLocalStorageValue, SELECTED_SHOP_KEY } from "@/localstore";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import SettingsIcon from '@mui/icons-material/Settings';

import { SOURCE_FB, SOURCE_GOOGLE_ADS, BLENDED } from "@/layouts/dashboards/metrics/metadata";
import { axiosInstance } from "@/context";
// import PaywallDialog from "@/components/PaywallDialog";
import CircularProgress from "@mui/material/CircularProgress";
import PricingCardsDialog from "@/components/Paywall";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import ConnectedAdAccounts from "@/layouts/ads/components/ConnectedAdAccounts";
import { CardLoader } from "@/components/AppLoader";
import { getMetricFormatterFn } from '@/util.jsx';

function ShopNameCell ({name, myshopify_domain}) {
    const [controller, dispatch] = useMaterialUIController();
    const navigate = useNavigate();

    const handleStoreDashboard = () => {
        setSelectedShop(dispatch, myshopify_domain);
        navigate('/dashboard');
    };

    return (
        <MDBox display="flex" alignItems="center" pr={1}>
            <MDBox mr={1}>
                <MDAvatar sx={{ bgcolor: stringToColor(name) }} size="md">
                    <StorefrontTwoToneIcon fontSize="small" />
                </MDAvatar>
            </MDBox>
            <MDTooltip title="Go to Dashboard" placement="top">
                <MDButton
                    color="secondary"
                    variant="text"
                    onClick={handleStoreDashboard}
                    sx={{
                        fontSize: "13px",
                        p: 0.5
                    }}
                    size="small"
                >
                    <MDTypography variant="button" fontWeight="medium">
                        {name}&nbsp;<Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                    </MDTypography>
                </MDButton>
            </MDTooltip>
        </MDBox>
    )
}

function SubscriptionCell ({subscription, myshopify_domain}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const {t} = useTranslation();
    const { pathname } = useLocation();

    const handleShopChangeForPricing = () => {
        if (selectedShop != myshopify_domain) {
            setSelectedShop(dispatch, myshopify_domain);
        }
        tracker.mixpanel.track('Clicked Pricing Page', {page : pathname, source: "settings_connected_shops"})
    }

    return (
        <MDBox display="flex" alignItems="center" justifyContent="center" pr={1}>
            <MDBox mr={1}>
                {(subscription && subscription.id) ? <MDBadge
                    badgeContent={
                        <>
                        {subscription.name ?? ""}
                        <img src={premiumTag} alt="premium" style={{width: "15px", height: "15px", marginLeft:"5px"}}/>
                        </>
                    }
                    size="lg"
                    p={0}
                    variant="gradient"
                    color="dark"
                    sx={{color:"white", p: 0}}
                /> :
                    <MDButton
                        color="secondary"
                        variant="text"
                        component={Link}
                        to="/pricing"
                        sx={{
                            fontSize: "13px",
                            p: 0.5
                        }}
                        size="small"
                        onClick={handleShopChangeForPricing}
                    >
                        <MDTooltip title={t("upgrade")} placement="right">
                            <MDTypography variant="button" fontWeight="medium">
                                {t("free")}&nbsp;<Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                            </MDTypography>
                        </MDTooltip>
                    </MDButton>
                }
            </MDBox>
        </MDBox>
    )
}

function StoreActionMenu({ shop, unlinkStore, filteredShops }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [controller, dispatch] = useMaterialUIController();
  const {t} = useTranslation();
  const navigate = useNavigate();

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleStoreSettings = () => {
    setSelectedShop(dispatch, shop.myshopify_domain);
    navigate('/settings');
    handleClose();
  };



  const handleUnlink = () => {
    handleClose();
    unlinkStore(shop.myshopify_domain);
  };

  return (
    <>
      <MDButton
        variant="text"
        color="dark"
        size="medium"
        onClick={handleClick}
        sx={{
          minWidth: 'auto',
          p: 1.5,
          '& .MuiSvgIcon-root': {
            fontSize: '1.5rem'
          }
        }}
      >
        <MoreVertIcon />
      </MDButton>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            '& .MuiMenuItem-root': {
              fontSize: '0.875rem',
              padding: '10px 16px',
              minHeight: '48px'
            }
          }
        }}
      >
        <MenuItem onClick={handleStoreSettings}>
          <SettingsIcon sx={{ mr: 1.5 }} fontSize="medium" />
          {t("store-settings")}
        </MenuItem>
        <MenuItem onClick={() => {
          window.open(`https://${shop.myshopify_domain}`, '_blank');
          handleClose();
        }}>
          <Icon sx={{ mr: 1.5 }} fontSize="medium">language</Icon>
          {t("store-website")}
        </MenuItem>
        <MenuItem
          onClick={handleUnlink}
          disabled={filteredShops.length === 1}
          sx={{
            color: 'error.main',
            '&.Mui-disabled': {
              color: 'action.disabled'
            }
          }}
        >
          <DeleteIcon sx={{ mr: 1.5 }} fontSize="medium" />
          {t("remove-from-workspace")}
        </MenuItem>
      </Menu>
    </>
  );
}

const AdAccountButton = ({ logo, accounts, onClick, isLoading, disabled, showBadge, color, badgeContent, isConnected }) => {
  const { t } = useTranslation();

  const button = (
    <MDBox
      display="flex"
      justifyContent="center"
      alignItems="center"
      p={1}
      mr={1}
      onClick={disabled || isConnected ? undefined : onClick}
      sx={{
        cursor: disabled || isConnected ? "default" : "pointer",
        borderRadius: ({ borders: { borderRadius } }) => borderRadius.md,
        border: "1px solid #49a3f1",
        opacity: disabled ? 0.5 : 1,
      }}
    >
      <MDBox component="img" src={logo} alt="ads" width="1rem" mr={0.5} />
      <MDTypography variant="caption" fontWeight="regular" textAlign="center" sx={{color: "#49a3f1"}}>
        {isLoading ? (
          <CircularProgress size={16} color="info" />
        ) : showBadge ? (
          <MDBadgeDot badgeContent={badgeContent} color={color} size="sm" />
        ) : (
          t(isConnected ? "connected" : "connect")
        )}
      </MDTypography>
    </MDBox>
  );

  if (!isConnected || showBadge) {
    return button;
  }

  const tooltipContent = (
    <MDBox display="flex" flexDirection="column" alignItems="left" justifyContent="center">
      {accounts.map((account) => (
        <MDBadgeDot
          key={account}
          badgeContent={account}
          color="success"
          font={{color: "text", weight: "regular"}}
          size="sm"
        />
      ))}
    </MDBox>
  );

  return (
    <MDTooltip title={tooltipContent} placement="bottom">
      {button}
    </MDTooltip>
  );
};

const workspace_metrics_summary_config = {
  title: "sales",
  metrics: [
      "blended:spend",
      "blended:roas",
      "shopify:total_price",
      "shopify:new_cust_count",
      "blended:cac"
  ],
  feature: "",
  report_type: "metrics_summary"
}

function MetricCell({ shopMetricsData, metric, format_type }) {
  function getComponent(val) {
    return (<MDBox display="flex" flexDirection="column" alignItems="center" minWidth="120px">
      <MDTypography variant="button" color="dark" fontWeight="medium"  textGradient >
        {val}
      </MDTypography>
    </MDBox>)
  }

  if (!shopMetricsData || !shopMetricsData.data || (shopMetricsData.data.current ?? []).length === 0) {
    return getComponent("-")
  }

  const {currency = "", currentAgg = {}} = shopMetricsData.data;
  let formatter = getMetricFormatterFn(format_type);

  return (
    getComponent(metric in currentAgg ? formatter(currentAgg[metric], currency) : "-")
  );
}

function ConnectedShopsRoot({ workspaceDetails, fetchWorkspaceDetails }) {
  const [controller, dispatch] = useMaterialUIController();
  const [loading, setLoading] = useState(true);
  const [shopMetricsData, setShopMetricsData] = useState({});

  const { t } = useTranslation();
  const { integrations, loginConfig, selectedFilters, selectedShop } = controller;
  const { start_date, end_date, time_frame } = selectedFilters;

  const dates = useMemo(() => ({
    start_date,
    end_date
  }), [start_date, end_date]);

  const userRole = loginConfig?.userData?.role;
  let filteredShops = workspaceDetails?.shops ?? [];

  const getShopSubscription = (shopDomain) => {
    // admin selected shop (which is not in workspace) will be shown as "Free"
    return (workspaceDetails?.shops ?? []).find(shop => shop.myshopify_domain === shopDomain)?.subscription ?? "Free";
  };

  if (loginConfig.admin && !filteredShops.find(shop => shop.myshopify_domain === selectedShop)) {
    // push the selected shop to the filteredShops array so it can be displayed in the table
    filteredShops.push({
      shop_name: selectedShop,
      myshopify_domain: selectedShop,
      subscription: getShopSubscription(selectedShop),
    });
  }

  const getShopIntegrations = (shopDomain) => {
    let shopIntegrations = {};
    if (shopDomain == selectedShop) {
      shopIntegrations = {
        [SOURCE_FB]: integrations[SOURCE_FB] ?? [],
        [SOURCE_GOOGLE_ADS]: integrations[SOURCE_GOOGLE_ADS] ?? []
      };
    } else {
      shopIntegrations = (workspaceDetails?.shops ?? []).find(shop => shop.myshopify_domain === shopDomain)?.integrations ?? {};
      shopIntegrations = {
        [SOURCE_FB]: shopIntegrations[SOURCE_FB] ?? [],
        [SOURCE_GOOGLE_ADS]: shopIntegrations[SOURCE_GOOGLE_ADS] ?? []
      };
    }

    return shopIntegrations;
  };

  const fetchReportData = async ({
    selectedShop,
  }) => {
    try {
      const reqData = {
        type: "metrics_summary",
        filters: {
          start_date: dayjs(dates.start_date).format("YYYY-MM-DD"),
          end_date: dayjs(dates.end_date).format("YYYY-MM-DD"),
          compare: false,
          time_frame: time_frame,
          integration_request_ids: [],
          metrics: workspace_metrics_summary_config.metrics,
        },
      };

      if (selectedShop) {
        reqData.selectedShop = selectedShop;
      }

      const response = await axiosInstance.post('/api/report/data', reqData);

      if (!response.data || response.data.error) {
        throw new Error(response.data?.error || 'Failed to fetch report data');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching report data:', error);
      return null; // Throw error?
    }
  };

  const setReportData = async () => {
    try {
      setLoading(true);
      const results = await Promise.all(
        filteredShops.map(async (shop) => {
          const shopDomain = shop.myshopify_domain;
          const data = await fetchReportData({selectedShop: shopDomain});
          return { shop: shopDomain, data };
        })
      );

      let dataMap = {};
      results.forEach(({ shop, data }) => {
        dataMap[shop] = data;
      });

      setShopMetricsData(dataMap);
    } catch (error) {
      console.error('Failed to fetch metrics data for all shops:', error);
      toast.error(t("something-went-wrong"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setReportData();
  }, [dates]);

  const addNewStore = () => {
    NiceModal.show(AddStoreDialog, {});
  };

    const unlinkStore = (shop_origin) => {
        NiceModal.show(ConfirmationDialog, {
          title : t("are-you-sure"),
          message: t("store-unlinking-warning"),
          onConfirm : () => {
              axiosInstance.post('/api/unlink-user-shop', {
                shop_to_unlink : shop_origin,
                selectedWorkspaceId : loginConfig.userData?.workspace_id
              }).then((res) => {
                if (res.data.status) {
                    fetchWorkspaceDetails();
                    const selectedShop = getLocalStorageValue(SELECTED_SHOP_KEY);
                    // If the shop being unlinked is the currently selected shop,
                    // then clear the selected shop from localStorage
                    if (selectedShop == shop_origin) {
                        setSelectedShop(dispatch, null);
                    }
                  fetchLoginConfig(dispatch, true);
                  toast.success(t("store-unlinking-success"));
                } else {
                  toast.error(t("store-unlinking-failed"));
                }
              }).catch((err) => {
                console.error(err);
                toast.error(t("store-unlinking-failed"));
              })

              NiceModal.hide(ConfirmationDialog);
          },
          onCancel : () => {
              NiceModal.hide(ConfirmationDialog);
          }
        })
    }

  let dataTableData = useMemo(() => {
    if (filteredShops.length === 0) {
      return {
        columns: [
          { Header: t("store"), accessor: "name", align: "left" },
          { Header: t("plan"), accessor: "subscription", align: "center" },
          { Header: t("blended.spend"), accessor: "blended-spend", align: "center" },
          { Header: t("blended.roas"), accessor: "blended-roas", align: "center" },
          { Header: t("revenue"), accessor: "total_price", align: "center" },
          { Header: t("new-cust"), accessor: "new_cust_count", align: "center" },
          { Header: t("blended.cac"), accessor: "cac", align: "center" },
          { Header: t("integrations"), accessor: "integrations", align: "center" },
          { Header: t(""), accessor: "action", align: "center" },
        ],
        rows: [
          {
            name: (
              <MDTypography variant="body2" color="text" align="center">
                {t("no-shops-connected")}
              </MDTypography>
            ),
            subscription: "",
            "blended-spend": "",
            "blended-roas": "",
            total_price: "",
            new_cust_count: "",
            cac: "",
            integrations: "",
            action: "",
          },
        ],
      };
    }

    return {
      columns: [
        { Header: t("store"), accessor: "name", align: "left" },
        { Header: t("plan"), accessor: "subscription", align: "center" },
        { Header: t("integrations"), accessor: "integrations", align: "center" },
        { Header: t("blended.spend"), accessor: "blended-spend", align: "center" },
        { Header: t("blended.roas"), accessor: "blended-roas", align: "center" },
        { Header: t("revenue"), accessor: "total_price", align: "center" },
        { Header: t("new-cust"), accessor: "new_cust_count", align: "center" },
        { Header: t("blended.cac"), accessor: "cac", align: "center" },
        { Header: t(""), accessor: "action", align: "center", width: "80px" },
      ],
      rows: filteredShops.map((shop) => ({
        name: <ShopNameCell name={shop.shop_name} myshopify_domain={shop.myshopify_domain} />,
        subscription: (
          <SubscriptionCell subscription={getShopSubscription(shop.myshopify_domain)} myshopify_domain={shop.myshopify_domain} />
        ),
        "blended-spend": (
          <MetricCell
            shopMetricsData={shopMetricsData[shop.myshopify_domain]}
            metric="blended:spend"
            format_type="currency"
          />
        ),
        "blended-roas": (
          <MetricCell
            shopMetricsData={shopMetricsData[shop.myshopify_domain]}
            metric="blended:roas"
            format_type="roas"
          />
        ),
        total_price: (
          <MetricCell
            shopMetricsData={shopMetricsData[shop.myshopify_domain]}
            metric="shopify:total_price"
            format_type="currency"
          />
        ),
        new_cust_count: (
          <MetricCell
            shopMetricsData={shopMetricsData[shop.myshopify_domain]}
            metric="shopify:new_cust_count"
            format_type="absolute"
          />
        ),
        cac: (
          <MetricCell
            shopMetricsData={shopMetricsData[shop.myshopify_domain]}
            metric="blended:cac"
            format_type="currency"
          />
        ),
        integrations: (
          <MDBox display="flex" flexDirection="column" alignItems="center">
            <ConnectedAdAccounts
              shopIntegrations={getShopIntegrations(shop.myshopify_domain)}
              preset={BLENDED}
            />
          </MDBox>
        ),
        action: (
          <StoreActionMenu
            shop={shop}
            unlinkStore={unlinkStore}
            filteredShops={filteredShops}
          />
        ),
      })),
    };
  }, [filteredShops, integrations, shopMetricsData, t]);

  return (
    <>
      {loading && <CardLoader height={300} />}
      {!loading && <Card sx={{ boxShadow: "none" }}>
        <MDBox mb={3} mt={2}>
              <Card elevation={0} mb={4} my={4} style={{boxShadow: "none"}}>
              <Grid container spacing={0} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center"  px={2} pb={2}>
                  <Grid item>
                      <MDTypography variant="button" sx={{fontSize:"13px"}}>
                      </MDTypography>
                      <DatePickerAnt report="dashboard" enableComparison={false} />
                  </Grid>
              </Grid>
              </Card>
          </MDBox>
          <MDBox pt={1} pb={2} px={2} lineHeight={1.25}>
          <DataTable
              table={dataTableData}
              entriesPerPage={false}
              showTotalEntries={false}
              isSorted={false}
          />
          {[WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(userRole) && (
              <MDBox
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  width={{ xs: "100%", sm: "auto" }}
                  mt={2}
              >
                  <MDButton
                      variant="outlined"
                      color="dark"
                      size="large"
                      sx={{ width: "50%" }}
                      onClick={addNewStore} >
                          <StorefrontIcon color="dark" sx={{fontSize : "20px !important"}}/> &nbsp;
                          {t("add-new-store")}
                  </MDButton>
              </MDBox>
          )}
          </MDBox>
      </Card>}
    </>
  );
}

function ConnectedShops(props) {
  const {t} = useTranslation();

  return (
    <Card id="connected-shops">
      <MDBox p={3} lineHeight={1}>
        <MDBox mb={1}>
          <MDTypography variant="h5">
            {t("connected-stores")}
          </MDTypography>
        </MDBox>
        <MDTypography variant="button" color="text">
          {t("manage-connected-stores")}
        </MDTypography>
      </MDBox>
      <ConnectedShopsRoot {...props} />
    </Card>
  );
}

export default ConnectedShops;
