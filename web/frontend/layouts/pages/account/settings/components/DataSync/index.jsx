// @mui material components
import Card from "@mui/material/Card";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import {Progress} from "antd";
import { useTranslation } from "react-i18next";
import { useMaterialUIController } from "@/context";

function DataSync() {
    const {t} = useTranslation();
    const [controller] = useMaterialUIController();
    const { shopConfig } = controller;

  return (
    <Card id="data-sync">
      <MDBox
        pr={3}
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: "flex-start", sm: "center" }}
        flexDirection={{ xs: "column", sm: "row" }}
      >
        <MDBox p={3} lineHeight={1}>
          <MDBox mb={1}>
            <MDTypography variant="h5">{t("data-sync")}</MDTypography>
          </MDBox>
          <MDTypography variant="button" color="text">
            {t("data-sync-description")}
          </MDTypography>
        </MDBox>
      </MDBox>
      <MDBox p={3} pt={0} lineHeight={1} display="flex" justifyContent="center">
      <MDBox sx={{border: "1px dashed #ccc", width:"100%"}} p={1} display="flex" flexDirection="column" justifyContent="flex-end">
        <Progress
            trailColor="#ffffff"
            size={"large"}
            strokeColor={{
                "0%": "#108ee9",
                "100%": "#87d068",
            }}
            percent={shopConfig.syncPercentage ?? 0}
        />
        <MDTypography variant="button" textAlign="right" width="100%" color="dark" fontWeight="regular" pr={1}>
            {t("last-synced")} : {shopConfig.lastSyncedAt ?? "-"}
        </MDTypography>
        </MDBox>
        </MDBox>
    </Card>
  );
}

export default DataSync;
