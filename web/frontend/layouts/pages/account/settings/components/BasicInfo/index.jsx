import React, { useState } from "react";
// @material-ui core components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Autocomplete from "@mui/material/Autocomplete";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";

// Settings page components
import FormField from "@/layouts/pages/account/components/FormField";
import {useTranslation} from "react-i18next";
import { fetchShopConfig, tracker, useMaterialUIController } from "@/context";

function BasicInfo({updateSettings}) {
  const [controller, dispatch] = useMaterialUIController();
  const { shopConfig, selectedShop } = controller;
  const {shop} = shopConfig;

  const {t} = useTranslation();
  const [fullName, setFullName] = useState(shop.onboard_name ?? "");
  const [userType, setUserType] = useState(shop.onboard_user_type ?? "");
  const [industry, setIndustry] = useState(shop.onboard_industry ?? "");
  const [email, setEmail] = useState(shop.onboard_email ?? "");
  const [phone, setPhone] = useState(shop.onboard_phone ?? "");
  const [saveLoader, setSaveLoader] = useState(false);

  let optionList = [
    {label: t("shopify-merchant"), value: "owner"},
    {label: t("agency"), value: "agency"},
    {label: t("other"), value: "other"},
  ];

  let industryOptionList = [
    {label: t("art-photography"), value: "Art & Photography"},
    {label: t("animals-pet-supplies"), value: "Animals & Pet Supplies"},
    {label: t("baby-toddler"), value: "Baby & Toddler"},
    {label: t("clothing-fashion"), value: "Clothing & Fashion"},
    {label: t("jewelry-accessories"), value: "Jewelry & Accessories"},
    {label: t("electronics"), value: "Electronics"},
    {label: t("food-drink"), value: "Food & Drink"},
    {label: t("home-garden"), value: "Home & Garden"},
    {label: t("furniture"), value: "Furniture"},
    {label: t("hardware"), value: "Hardware"},
    {label: t("health-beauty"), value: "Health & Beauty"},
    {label: t("sports-recreation"), value: "Sports & Recreation"},
    {label: t("toys-games"), value: "Toys & Games"},
    {label: t("stationary"), value: "Stationary"},
    {label: t("other"), value: "Other"}
  ];

  let canSave = (fullName != shop.onboard_name && fullName != "")
    || userType != shop.onboard_user_type
    || (industry != shop.onboard_industry && industry != "")
    || (email != shop.onboard_email && email != "")
    || phone != shop.onboard_phone;

  const saveSettings = () => {

    if (saveLoader) {
      return
    }

    setSaveLoader(true)
    updateSettings({
      shopInfo : {
        onboard_name : fullName,
        onboard_user_type : userType,
        onboard_industry : industry,
        onboard_email : email,
        onboard_phone : phone
      }
    }, function () {
      tracker.event("Settings Updated", {
        industry_updated : industry != shop.onboard_industry,
        email_updated : email != shop.onboard_email,
        phone_updated : phone != shop.onboard_phone
      });
      fetchShopConfig(dispatch, selectedShop);
      setSaveLoader(false);
    })
  }

  return (
    <Card id="basic-info" sx={{ overflow: "visible" }}>
      <MDBox display="flex" justifyContent="space-between" alignItems="center" p={3} lineHeight={1}>
        <MDTypography variant="h5">{t("basic-info")}</MDTypography>
        {canSave && <MDButton size="medium" variant="gradient" color="primary" onClick={saveSettings}>
          {saveLoader ? <CircularProgress size={20} color="white" /> : t("save")}
        </MDButton>}
      </MDBox>

      <MDBox component="form" pb={3} px={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={12}>
            <FormField
              label={t("full-name")}
              value={fullName}
              onChange={(event) => {
                setFullName(event.target.value);
              }}
              />
          </Grid>
          <Grid item xs={12}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Autocomplete
                    onChange={(event, value) => {
                        setUserType(value.value);
                    }}
                    options={optionList}
                    value={optionList.find((option) => option.value === userType) ?? null}
                    getOptionLabel={(option) => {
                      return option.label ?? ""
                    }}
                    isOptionEqualToValue={(option, value) => {
                      return option.value === value.value;
                    }}
                    renderInput={(params) => (
                      <FormField {...params} label={t("what-best-describes-you")} InputLabelProps={{ shrink: true }} />
                    )}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                    <Autocomplete
                      onChange={(event, value) => {
                          setIndustry(value.value);
                      }}
                      value={industryOptionList.find((option) => option.value === industry) ?? null}
                      options={industryOptionList}
                      getOptionLabel={(option) => {
                        return option.label ?? ""
                      }}
                      isOptionEqualToValue={(option, value) => {
                        return option.value === value.value;
                      }}
                      renderInput={(params) => (
                        <FormField {...params} label={t("industry")} InputLabelProps={{ shrink: true }} />
                      )}
                    />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              onChange={(event) => {
                setEmail(event.target.value);
              }}
              label={t("work-email")}
              value={email}
              inputProps={{ type: "email" }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              disabled={true}
              value={shop.timezone ?? ""}
              label={t("time-zone")}
              />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              value={phone}
              onChange={(event) => {
                setPhone(event.target.value);
              }}
              label={t("phone-number")}
              placeholder="+40 735 631 620"
              inputProps={{ type: "number" }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              disabled={true}
              value={shop.currency ?? ""}
              label={t("currency")}
              />
          </Grid>
        </Grid>
      </MDBox>
    </Card>
  );
}

export default BasicInfo;
