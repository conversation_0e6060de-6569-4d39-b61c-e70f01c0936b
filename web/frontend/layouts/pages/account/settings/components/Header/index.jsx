// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import MDButton from "@/components/MDButton";
import MDBadge from "@/components/MDBadge";
import Avatar from 'boring-avatars';

// Images
// import burceMars from "@/assets/images/bruce-mars.jpg";
import { useTranslation } from 'react-i18next';
import { useMaterialUIController } from "@/context";
import dayjs from "dayjs";
import premiumTag from "@/assets/images/premium-tag.png";

function Header() {

  const [controller] = useMaterialUIController();
  const {loginConfig, shopConfig} = controller;
  const {t} = useTranslation();
  let isFreePlan = shopConfig.planDetails?.planType == "free";

  if (!shopConfig.shop) {
    return null;
  }

  let planName = shopConfig.planDetails?.planName;
  if (shopConfig.planDetails?.render?.display_name) {
      planName = shopConfig.planDetails.render.display_name;
  }

  return (
    <Card id="store">
      <MDBox p={2}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <MDAvatar alt="profile-image" size="xl" shadow="sm" >
              <Avatar name={shopConfig.shop.name} variant={"bauhaus"} size={120} colors={['#E91E63','#344767','#F44335','#FB8C00','#f0f2f5']} />
            </MDAvatar>
          </Grid>
          <Grid item>
            <MDBox height="100%" mt={0.5} lineHeight={1}>
              <MDTypography variant="h5" fontWeight="medium">
                {shopConfig.shop.name && shopConfig.shop.name.length > 25 ? (shopConfig.shop.name.substring(0, 25) + "...") : shopConfig.shop.name}
                {shopConfig.subscription_enabled && !isFreePlan && !!planName &&
                  <MDBadge 
                    badgeContent={
                      <>
                        {planName}
                        <img src={premiumTag} alt="premium" style={{width: "15px", height: "15px", marginLeft:"5px"}}/>
                      </>
                    } 
                    size="lg" 
                    p={0}
                    variant="gradient"
                    color="dark"
                    sx={{color:"white", p: 0}}
                  />
                }
              </MDTypography>
              <MDTypography variant="button" color="text" fontWeight="medium">
                {shopConfig.shop.domain}
              </MDTypography>
              {loginConfig.admin && <br/>}
              {loginConfig.admin && <MDTypography 
                  variant="caption"
                  fontWeight="medium"
                  color={shopConfig.shop.active_flag ? "secondary" : "error"}
                  >
                    {shopConfig.shop.active_flag ? `Installed (${dayjs(shopConfig.shop.row_created_at).format("DD MMM, YYYY")})` : `Uninstalled (${dayjs(shopConfig.shop.uninstalled_at).format("DD MMM, YYYY")})`}
                  </MDTypography>
              }
            </MDBox>
          </Grid>
          <Grid item xs={12} md={6} lg={3} sx={{ ml: "auto" }}>
            <MDBox
              display="flex"
              justifyContent={{ md: "flex-end" }}
              alignItems="center"
              lineHeight={1}
            >
              <MDButton color="secondary" variant="text" component="a" href={`https://${shopConfig.shop.myshopify_domain}`} sx={{fontSize:"13px"}} size="small" target="_blank" rel="noreferrer">
                  {t("shopify-store")}&nbsp;
                  <Icon fontSize="medium">arrow_outward</Icon>&nbsp;
              </MDButton>
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
    </Card>
  );
}

export default Header;
