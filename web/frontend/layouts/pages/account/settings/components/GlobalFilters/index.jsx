import React, {useState, useEffect} from "react";
// @mui material components
import Card from "@mui/material/Card";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import Switch from "@mui/material/Switch";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";

// Setting pages components
import TableCell from "@/layouts/pages/account/settings/components/TableCell";
import {useTranslation} from "react-i18next";
import MDInput from "@/components/MDInput";
import CircularProgress from "@mui/material/CircularProgress";
import { useCancellableAxios, tracker, useMaterialUIController } from "@/context";
import { toast } from "react-toastify";
import {CardLoader} from "@/components/AppLoader";
import axios from "axios";

// {
//    "exclude_order_tags": ["sample", "test"],
//    "exclude_customer_tags": ["test"],
//    "exclude_order_financial_status": ["pending", "authorized"],
//    "exclude_order_fulfillment_status": ["unfulfilled", "unshipped"],
//    "exclude_order_zero_value_orders": true,
//    "exclude_order_cancelled_orders": true
// }

function GlobalFilters() {


  const [controller] = useMaterialUIController();
  const { selectedShop, shopConfig } = controller;
  const {t} = useTranslation();
  const [exclude_order_tags, setExcludeOrderTags] = useState([]);
  const [exclude_customer_tags, setExcludeCustomerTags] = useState([]);
  const [exclude_order_financial_status, setExcludeOrderFinancialStatus] = useState([]);
  const [exclude_order_zero_value_orders, setExcludeOrderZeroValueOrders] = useState(false);
  const [exclude_order_cancelled_orders, setExcludeOrderCancelledOrders] = useState(false);
  const [exclude_order_fulfillment_status, setExcludeOrderFulfillmentStatus] = useState([]);

  const [saveLoader, setSaveLoader] = useState(false);
  const [loader, setLoader] = useState(false);
  const axiosInstance = useCancellableAxios();

  const fetchSettings = () => {
    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    setLoader(true);
    axiosInstance.post('/api/settings', reqData)
        .then((response) => {
            if (response.data && response.data.global_filters && response.data.global_filters.setting_value) {
                let setting = response.data.global_filters.setting_value;
                setExcludeOrderTags(setting.exclude_order_tags ?? []);
                setExcludeCustomerTags(setting.exclude_customer_tags ?? []);
                setExcludeOrderFinancialStatus(setting.exclude_order_financial_status ?? []);
                setExcludeOrderFulfillmentStatus(setting.exclude_order_fulfillment_status ?? []);
                setExcludeOrderZeroValueOrders(setting.exclude_order_zero_value_orders ?? false);
                setExcludeOrderCancelledOrders(setting.exclude_order_cancelled_orders ?? false);
            } else {
                setExcludeOrderTags([]);
                setExcludeCustomerTags([]);
                setExcludeOrderFinancialStatus([]);
                setExcludeOrderFulfillmentStatus([]);
                setExcludeOrderZeroValueOrders(false);
                setExcludeOrderCancelledOrders(false);
            }
            setLoader(false);
        })
        .catch((error) => {
            console.log(error)
            setLoader(false);
      })
  }

  const updateSettings = () => {
    if (saveLoader) {
        return
    }

    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    reqData.settings = {
        shopSettings : {
            global_filters : {
                setting_value: {
                    exclude_order_tags : exclude_order_tags.map(t => t.trim()),
                    exclude_customer_tags : exclude_customer_tags.map(t => t.trim()),
                    exclude_order_financial_status,
                    exclude_order_fulfillment_status,
                    exclude_order_zero_value_orders,
                    exclude_order_cancelled_orders
                },
                active_flag: 1
            }
        }
    };

    setSaveLoader(true)
    axiosInstance.post('/api/settings/update', reqData)
        .then((res) => {

          if ('error' in res.data) {
            toast.error(res.data.error);
          } else {
            toast.success(t("saved-successfully"));
          }

          setSaveLoader(false)
          tracker.event("Settings Updated", {
            "Setting": "Reports"
          })
          fetchSettings();
        })
        .catch((error) => {
            setSaveLoader(false)
            console.error(error)
            !axios.isCancel(error) && toast.error(t("something-went-wrong"))
        })
  }

  useEffect(fetchSettings, [selectedShop]);

  return (
        <Card id="global-filters">
        {!loader && <MDBox>
            <MDBox display="flex" justifyContent="space-between" alignItems="center" p={3} lineHeight={1}>
            <MDBox>
                <MDBox mb={1}>
                <MDTypography variant="h5">
                    {t("global_filters.title")}
                </MDTypography>
                </MDBox>
                <MDTypography variant="button" color="text">
                    {t("global_filters.desc")}
                </MDTypography>
            </MDBox>
            </MDBox>
            <MDBox pb={3} px={3}>
            <MDBox minWidth="auto" sx={{ overflow: "scroll" }}>
                <Table sx={{ minWidth: "36rem" }}>
                <TableBody>
                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-zero-value")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-zero-value-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_zero_value_orders} onChange={(e) => setExcludeOrderZeroValueOrders(e.target.checked)} />
                    </TableCell>
                    </TableRow>

                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-cancelled")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-cancelled-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_cancelled_orders} onChange={(e) => setExcludeOrderCancelledOrders(e.target.checked)} />
                    </TableCell>
                    </TableRow>


                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-unshipped")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-unshipped-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_fulfillment_status?.includes("unshipped") ?? false}
                            onChange={(e) =>  setExcludeOrderFulfillmentStatus(e.target.checked ? [...exclude_order_fulfillment_status, "unshipped"] : exclude_order_fulfillment_status.filter(f => f !== "unshipped"))} />
                    </TableCell>
                    </TableRow>

                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-unfulfilled")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-unfulfilled-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_fulfillment_status?.includes("unfulfilled") ?? false}
                            onChange={(e) =>  setExcludeOrderFulfillmentStatus(e.target.checked ? [...exclude_order_fulfillment_status, "unfulfilled"] : exclude_order_fulfillment_status.filter(f => f !== "unfulfilled"))} />
                    </TableCell>
                    </TableRow>

                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-pending")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-pending-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_financial_status?.includes("pending") ?? false}
                            onChange={(e) =>  setExcludeOrderFinancialStatus(e.target.checked ? [...exclude_order_financial_status, "pending"] : exclude_order_financial_status.filter(f => f !== "pending"))} />
                    </TableCell>
                    </TableRow>

                    <TableRow>
                    <TableCell padding={[1, 1, 1, 0.5]}>
                        <MDBox lineHeight={1.4} p={0} pl={1}>
                        <MDTypography display="block" variant="button" fontWeight="regular">
                            {t("global_filters.excl-authorized")}
                        </MDTypography>
                        <MDTypography variant="caption" color="text" fontWeight="regular">
                            {t("global_filters.excl-authorized-desc")}
                        </MDTypography>
                        </MDBox>
                    </TableCell>
                    <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                        <Switch checked={exclude_order_financial_status?.includes("authorized") ?? false}
                            onChange={(e) =>  setExcludeOrderFinancialStatus(e.target.checked ? [...exclude_order_financial_status, "authorized"] : exclude_order_financial_status.filter(f => f !== "authorized"))} />
                    </TableCell>
                    </TableRow>
                </TableBody>
                </Table>
                <MDBox mt={3}>
                <MDTypography variant="button" fontWeight="regular" color="dark">
                    {t("global_filters.excl-order-tags")}
                </MDTypography>
                <MDInput
                    mt={0.5}
                    variant="outlined"
                    onChange={(e) => setExcludeOrderTags(!!e.target.value ? e.target.value.split(",") : [])}
                    fullWidth
                    value={exclude_order_tags.join(",")}
                    InputLabelProps={{ shrink: true }}
                />
                <MDTypography variant="caption" color="text" fontWeight="regular">
                    {t("global_filters.excl-order-tags-desc")}
                </MDTypography>
                </MDBox>

                <MDBox mt={3}>
                <MDTypography variant="button" fontWeight="regular" color="dark">
                    {t("global_filters.excl-customer-tags")}
                </MDTypography>
                <MDInput
                    mt={0.5}
                    variant="outlined"
                    onChange={(e) => setExcludeCustomerTags(!!e.target.value ? e.target.value.split(",") : [])}
                    fullWidth
                    value={exclude_customer_tags.join(",")}
                    InputLabelProps={{ shrink: true }}
                />
                <MDTypography variant="caption" color="text" fontWeight="regular">
                    {t("global_filters.excl-customer-tags-desc")}
                </MDTypography>
                </MDBox>
            </MDBox>

            <MDBox mt={3}>
            <MDButton variant="gradient" color="secondary" onClick={updateSettings} disabled={saveLoader} fullWidth>
                {saveLoader ? <CircularProgress size={20} color="white" /> : t("save")}
            </MDButton>
            </MDBox>
            </MDBox>
        </MDBox>}
        {loader && <CardLoader />}
    </Card>
  );
};

export default GlobalFilters;
