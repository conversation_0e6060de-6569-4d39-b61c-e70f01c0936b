import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import Dialog from '@mui/material/Dialog'
import { useNavigate } from 'react-router-dom'

// Material Dashboard 2 PRO React components
import Card from '@mui/material/Card';
import MDBox from '@/components/MDBox';
import MDTypography from '@/components/MDTypography';
import MDButton from '@/components/MDButton';
import bgImage from '@/assets/images/bg-sign-in-basic.jpeg'
import BasicLayout from '@/layouts/authentication/components/BasicLayout'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import MDInput from '@/components/MDInput'
import CircularProgress from '@mui/material/CircularProgress'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { useTranslation } from 'react-i18next'

import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import FormGroup from '@mui/material/FormGroup'
import FormControlLabel from '@mui/material/FormControlLabel'
import Checkbox from '@mui/material/Checkbox'
import { useCancellableAxios, useMaterialUIController } from '@/context'
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_MEMBER } from '@/constants';

function EditTeamMember(props) {
    return (
        <BasicLayout image={bgImage}>
            <MDBox
                height='100vh'
                display='flex'
                flexDirection='column'
                justifyContent='center'
                alignItems='center'
            >
                <EditTeamMemberRoot {...props} />
            </MDBox>
        </BasicLayout>
    )
}

export const EditMemberDialog = NiceModal.create(props => {
    const modal = useModal()
    const [controller, dispatch] = useMaterialUIController()
    return (
        <Dialog
            open={modal.visible}
            onClose={modal.hide}
            TransitionProps={{
                onExited: () => modal.remove()
            }}
        >
            <EditTeamMemberRoot {...props} />
        </Dialog>
    )
})

function EditTeamMemberRoot(props) {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const { workspaceUser, workspaceShops, fetchWorkspaceDetails } = props;

    const [name, setName] = useState(workspaceUser.user_name || '');
    const [email, setEmail] = useState(workspaceUser.user_email || '');
    const [role, setRole] = useState(workspaceUser.role || 'Member');
    const [selectedShops, setSelectedShops] = useState(workspaceUser.shop_ids || []);
    const [submitButtonDisabled, setSubmitButtonDisabled] = useState(true);
    const [submitButtonColor, setSubmitButtonColor] = useState('info');
    const [loading, setLoading] = useState(false);

    const axiosInstance = useCancellableAxios();

    const workspace_shop_ids = workspaceShops.map(shop => shop.shop_id);
    
    const handleShopSelection = shopId => {
        if (selectedShops.includes(shopId)) {
            setSelectedShops(prev => prev.filter(id => id !== shopId))
        } else {
            setSelectedShops(prev => [...prev, shopId])
        }
    }

    useEffect(() => {
        if (selectedShops.length == 0 && role.toLowerCase() == 'member') {
            setSubmitButtonDisabled(true);
            setSubmitButtonColor('secondary');
        } else {
            setSubmitButtonDisabled(false);
            setSubmitButtonColor('info');
        }
    }, [selectedShops, role]);

    const handleSubmit = async ev => {
        ev.preventDefault()
        setLoading(true)
        try {
            await axiosInstance
                .post('/api/workspace/member/update', {
                    workspace_id: Number(workspaceUser.workspace_id),
                    user_id: workspaceUser.user_id,
                    data: {
                        role,
                        shop_ids: [WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_OWNER].includes(role) ? workspace_shop_ids : [...selectedShops]
                    }
                })
                .then(res => {
                    if (res.data.status) {
                        toast.success(t('update-team-member-success'))
                        NiceModal.hide(EditMemberDialog)
                        fetchWorkspaceDetails()
                    } else {
                        toast.error(t('update-team-member-failed'))
                    }
                })
                .catch(err => {
                    console.error(err)
                    toast.error(t('update-team-member-failed'))
                })
        } catch (error) {
            console.error('Error updating team member:', error)
        } finally {
            setLoading(false)
        }
    }

    return (
        <Card>
            <MDBox m={4} display='flex' flexDirection='column' alignItems='center' justifyContent='center'>
                <MDTypography variant='h4' gutterBottom>
                    {t('edit-team-member-heading')}
                </MDTypography>
                <MDTypography variant="button" textAlign='center' justifyContent='center'>
                    {t('edit-team-member-description')}
                </MDTypography>
                <MDBox
                    component='form'
                    role='form'
                    onSubmit={handleSubmit}
                    width='100%'
                    mt={4}
                >
                    <MDBox mb={4}>
                        <MDInput
                            fullWidth
                            label={t('name')}
                            value={name}
                            disabled
                        />
                    </MDBox>
                    <MDBox mb={4}>
                        <MDInput
                            fullWidth
                            type='email'
                            label={t('email')}
                            value={email}
                            disabled
                        />
                    </MDBox>
                    <MDBox mb={4}>
                        <FormControl fullWidth sx={{ minWidth: '100%', height: '56px' }}>
                            <Select
                                value={role || ''}
                                onChange={e => setRole(e.target.value)}
                                displayEmpty
                                IconComponent={ExpandMoreIcon}
                                sx={{
                                    height: '48px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    '& .MuiSelect-icon': {
                                        marginRight: '10px',
                                        display: 'block !important'
                                    }
                                }}
                            >
                                <MenuItem value={WORKSPACE_ROLE_ADMIN}>{t('admin')}</MenuItem>
                                <MenuItem value={WORKSPACE_ROLE_MEMBER}>{t('member')}</MenuItem>
                            </Select>
                        </FormControl>
                    </MDBox>
                    {role === 'Member' && (
                        <MDBox mb={4}>
                            <MDTypography variant='button'>{t('select-shops')}</MDTypography>
                            <FormGroup>
                                {workspaceShops.map(shop => (
                                    <FormControlLabel
                                        key={shop.shop_id}
                                        control={
                                            <Checkbox
                                                checked={selectedShops.includes(shop.shop_id)}
                                                onChange={() => handleShopSelection(shop.shop_id)}
                                            />
                                        }
                                        label={shop.shop_name}
                                    />
                                ))}
                            </FormGroup>
                        </MDBox>
                    )}
                    <MDBox mt={3}>
                        <MDButton id='edit-member-submit-button' fullWidth color={submitButtonColor} type='submit' size='large' disabled={loading || submitButtonDisabled}>
                            {loading ? (
                                <CircularProgress size={20} color='white' />
                            ) : (
                                t('submit')
                            )}
                        </MDButton>
                    </MDBox>
                </MDBox>
            </MDBox>
        </Card>
    )
}

export default EditTeamMember;
