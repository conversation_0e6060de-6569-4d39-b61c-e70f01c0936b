import React, {useState, useEffect} from "react";
// @mui material components
import Card from "@mui/material/Card";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import Switch from "@mui/material/Switch";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import InputAdornment from "@mui/material/InputAdornment";
import MDButton from "@/components/MDButton";

// Setting pages components
import TableCell from "@/layouts/pages/account/settings/components/TableCell";
import {useTranslation} from "react-i18next";
import MDInput from "@/components/MDInput";
import CircularProgress from "@mui/material/CircularProgress";
import SlackLogo from "@/assets/images/small-logos/logo-slack.svg";
import { useCancellableAxios, tracker, useMaterialUIController } from "@/context";

import { toast } from "react-toastify";
import DisconnectSlack from "./DisconnectSlack";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import MDTooltip from "@/components/MDTooltip";

function EmailReports({settings, updateSettings, fetchSettings}) {


  const [controller] = useMaterialUIController();
  const { selectedShop, shopConfig } = controller;
  const {email_weekly_report, slack_weekly_report} = settings;
  const {t} = useTranslation();
  const [emails, setEmails] = useState(email_weekly_report?.setting_value?.emails ?? "");
  const [emailEnabled, setEmailEnabled] = useState(Boolean(email_weekly_report?.active_flag ?? false));
  const [slackEnabled, setSlackEnabled] = useState(Boolean(slack_weekly_report?.active_flag ?? false));
  const [saveLoader, setSaveLoader] = useState(false);
  const [disconnectLoader, setDisconnectLoader] = useState(false);

  const [loader, setLoader] = useState(false);
  const [slackData, setSlackData] = useState({});
  const axiosInstance = useCancellableAxios();

  let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  let blockScheduledReports = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.scheduled_reports ?? false);

  let canSave = emails != (email_weekly_report?.setting_value?.emails ?? "")
    || emailEnabled != (Boolean(email_weekly_report?.active_flag ?? false))
    || slackEnabled != (Boolean(slack_weekly_report?.active_flag ?? false));

  const saveSettings = () => {

    if (blockScheduledReports) {
      tracker.event("Paywall", {feature: "scheduled_reports"});
      NiceModal.show(PaywallDialog, {feature : "scheduled_reports"})
      return false
    }

    if (saveLoader) {
      return
    }

    if (emailEnabled && !emails) {
      toast.error(t("report-emails-required"));
      return
    }

    setSaveLoader(true)
    updateSettings({
      shopSettings : {
        slack_weekly_report : {
          active_flag : slackEnabled,
        },
        email_weekly_report : {
          setting_value : {
            emails: emails
          },
          active_flag: emailEnabled
        }
      }
    }, function () {
      tracker.event("Settings Updated", {
        "Setting": "Reports"
      })
      fetchSettings();
    })
  }

  const fetchSlackStatus = async () => {

    let reqData = {}
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop
    }

    setLoader(true);
    try {
        const response = await axiosInstance.post("/api/integration/slack", reqData);
        if (response.data.error) {
            toast.error(response.data.error);
            setLoader(false);
        } else if (response.data) {
            setLoader(false);
            setSlackData(response.data);
        }
    } catch (error) {
        setLoader(false);
        console.error("Error fetching Slack status:", error);
    }
  };

  useEffect(() => {
    fetchSlackStatus();
  }, [selectedShop]);

  // slack auth redirect
  const slackAuth = async () => {
      if (slackData.slack_auth_url) {
          window.location.href = slackData.slack_auth_url;
      } else {
          toast.error(t("something-went-wrong"));
      }
  };

  const disconnectSlack = () => {
      if (disconnectLoader) {
          return;
      }

      setDisconnectLoader(true);
      NiceModal.show(DisconnectSlack, {onFinish: () => {
        setDisconnectLoader(false);
        fetchSlackStatus();
      }})
  }

  return (
    <Card id="scheduled-reports">
      <MDBox display="flex" justifyContent="space-between" alignItems="center" p={3} lineHeight={1}>
        <MDBox>
          <MDBox mb={1}>
            <MDTypography variant="h5">
              {t("scheduled-reports")}
            </MDTypography>
          </MDBox>
          <MDTypography variant="button" color="text">
            {t("email-reports-description")}
          </MDTypography>
        </MDBox>
        {canSave && (
          <MDButton size="medium" variant="gradient" color="primary" onClick={saveSettings} disabled={saveLoader}>
            {saveLoader ? <CircularProgress size={20} color="white" /> : t("save")}
          </MDButton>
        )}
      </MDBox>
      <MDBox pb={3} px={3}>
        <MDBox minWidth="auto" sx={{ overflow: "scroll" }}>
          <Table sx={{ minWidth: "36rem" }}>
          <MDBox component="thead">
            <TableRow>
              <TableCell width="100%" padding={[1.5, 3, 1.5, 0.5]}>
                <MDBox lineHeight={1.4} p={0} pl={1}>
                <MDTypography variant="button" fontWeight="regular">
                  {t("channel")}
                </MDTypography>
                </MDBox>
              </TableCell>
              <TableCell align="center" padding={[1.5, 6, 1.5, 6]}>
                <Icon fontSize="medium" color="primary">email</Icon>
                Email
              </TableCell>
              <TableCell align="center" padding={[1.5, 6, 1.5, 6]}>
                <MDBox component="img" src={SlackLogo} alt="slackLogo" width="2rem"  />
                Slack
              </TableCell>
            </TableRow>
          </MDBox>
            <TableBody>
              <TableRow>
                <TableCell padding={[1, 1, 1, 0.5]}>
                  <MDBox lineHeight={1.4} p={0} pl={1}>
                    <MDTypography display="block" variant="button" fontWeight="regular">
                      {t("weekly-report")}
                    </MDTypography>
                    <MDTypography variant="caption" color="text" fontWeight="regular">
                      {t("weekly-report-desc")}
                    </MDTypography>
                  </MDBox>
                </TableCell>
                <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                  <Switch checked={emailEnabled} onChange={(e) => setEmailEnabled(e.target.checked)} />
                </TableCell>
                <TableCell align="center" padding={[1, 1, 1, 0.5]}>
                  <MDBox display="flex" flexDirection="row" justifyContent="center" alignItems="center">
                    <Switch disabled={!slackData.slack_connected} checked={!!slackEnabled && !!slackData.slack_connected} onChange={(e) => setSlackEnabled(e.target.checked)} />  
                    {!slackData.slack_connected && <MDTooltip title={t("slack-connect")} >
                      <Icon fontSize="small" color="primary">help_outline</Icon>
                    </MDTooltip>}
                  </MDBox>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <MDBox mt={3}>
            <MDTypography variant="button" fontWeight="regular" color="dark">
              {t("report-emails")}
            </MDTypography>
            <MDInput
              mt={0.5}
              variant="outlined"
              error={emailEnabled && !emails}
              onChange={(e) => setEmails(e.target.value)}
              fullWidth
              value={emails}
              InputProps={{
                  type: "email",
                  startAdornment: (
                    <InputAdornment position="start">
                        <Icon fontSize="small" color="primary">email</Icon>
                    </InputAdornment>
                  )
              }}
              InputLabelProps={{ shrink: true }}
            />
          </MDBox>
          <MDBox mt={3}>
            <MDTypography variant="button" fontWeight="regular" color="dark">
              {t("slack-connect")}
            </MDTypography>
            <MDBox mt={0.5}>
                  {loader && <CircularProgress size={20} color="primary" />}
                  {!loader && !slackData.slack_connected && <MDButton
                      size="small"
                      variant="outlined"
                      color="dark"
                      onClick={slackAuth}
                  >
                      <MDBox component="img" src={SlackLogo} alt="slackLogo" width="1.5rem"  />
                      {t("connect")}
                  </MDButton>}
                  {!loader && slackData.slack_connected && <MDBox display="flex" flexDirection="row" justifyContent="space-between" alignItems="center" mb={2} p={1} bgColor="grey-100">
                      <MDTypography variant="button" color="dark" fontWeight="regular" display="flex" justifyContent="center" alignItems="center">
                          <MDBox component="img" src={SlackLogo} alt="slackLogo" width="1.5rem"  />
                          {t("connected")}: {slackData.channel ?? ""}
                      </MDTypography>
                      <MDButton 
                          variant="text"
                          color="error"
                          size="medium"
                          onClick={disconnectSlack}
                          style={{ marginTop: '0.1rem' }}
                      >
                          {disconnectLoader ? <CircularProgress size={20} color="error" /> : t("disconnect-btn")}
                      </MDButton>
                  </MDBox>}
            </MDBox>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
};

export default EmailReports;
