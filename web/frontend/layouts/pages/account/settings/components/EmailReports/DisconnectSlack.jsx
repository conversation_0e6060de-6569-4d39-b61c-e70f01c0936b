import { toast } from "react-toastify";
import {useCancellableAxios, useMaterialUIController} from "@/context";

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import MDTypography from "@/components/MDTypography";
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Alert from '@mui/material/Alert';
import MDButton from "@/components/MDButton";
import {useTranslation} from "react-i18next";
import axios from "axios";

const DisconnectSlack = NiceModal.create(({onFinish}) => {
    const modal = useModal();
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const axiosInstance = useCancellableAxios();


    const disconnectSource = () => {

        let reqData = {}
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop
        }

        axiosInstance.post('/api/integration/slack/disconnect', reqData)
        .then((res) => {
            if ('error' in res.data) {
                toast.error(res.data.error);
                onFinish();
            } else {
                toast.success(t("source-disconnected-success"));
                onFinish();
            }
        }).catch((err) => {
            console.log(err);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"))
            onFinish();
        });
        modal.hide();
    }

    return (
        <Dialog
            width="350px"
            open={modal.visible}
            onClose={() => {
            modal.hide()
            onFinish();
            }}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <DialogTitle id="alert-dialog-title">
                <MDTypography variant="h5" color="dark">
                {t("are-you-sure")}
                </MDTypography>
            </DialogTitle>
            <DialogContent>
                <DialogContentText id="alert-dialog-description">
                    <Alert severity="warning">
                    <MDTypography variant="button" color="dark">
                        {t("disconnecting-slack")}
                    </MDTypography>
                    </Alert>
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <MDButton onClick={() => {onFinish(); modal.hide()}} autoFocus variant="outlined" color="secondary" size="small">
                    {t("cancel-subs-alert-cancel")}
                </MDButton>
                <MDButton onClick={disconnectSource} variant="outlined" color="info" size="small">
                    {t("cancel-subs-alert-confirm")}
                </MDButton>
            </DialogActions>
        </Dialog>
    );
  });

  export default DisconnectSlack;