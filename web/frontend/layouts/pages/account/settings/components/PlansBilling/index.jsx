// @mui material components
import Card from "@mui/material/Card";
import {Link} from "react-router-dom";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { useTranslation } from "react-i18next";
import { useMaterialUIController } from "@/context";
import Divider from "@mui/material/Divider";
import premiumTag from "@/assets/images/premium-tag.png";
import MDAvatar from "@/components/MDAvatar";


function PlansBilling() {

    const [controller] = useMaterialUIController();
    const {shopConfig} = controller;
    const {t} =  useTranslation();
    let isFreePlan = shopConfig.subscription_enabled && shopConfig.planDetails?.planType == "free";

    let planName = shopConfig.planDetails?.planName;
    if (shopConfig.planDetails?.render?.display_name) {
        planName = shopConfig.planDetails.render.display_name;
    }

    let disclaimer = (
        <MDBox>
          <Divider style={{margin:"0"}}/>
          <MDTypography variant="button" fontWeight="regular" color="secondary" pl={3} py={1} display="block" sx={{lineHeight: "2"}}>
              {t("subscription-disclaimer")}
              <MDButton
                  variant="text"
                  color="secondary"
                  size="small"
                  target="_blank"
                  component="a"
                  href={"https://help.shopify.com/en/manual/your-account/manage-billing/your-invoice/apps#subscriptions"}
                  rel="noreferrer"
                  >
                  {t("learn-more")}
                  &nbsp; <Icon>open_in_new</Icon>
              </MDButton>
          </MDTypography>
      </MDBox>
    );

      return (
          <Card id="subscription">
          <MDBox
            pr={3}
            display="flex"
            justifyContent="space-between"
            alignItems={{ xs: "flex-start", sm: "center" }}
            flexDirection={{ xs: "column", sm: "row" }}
          >
            <MDBox p={3} lineHeight={1}>
              <MDBox mb={1}>
                <MDTypography variant="h5">{t("subscription")}</MDTypography>
              </MDBox>
              <MDTypography variant="button" color="text">
                {t("subscription-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox pt={0} pb={3} px={2}>
          <MDBox
            bgColor="light"
            p={2}
            borderRadius="lg"
            display="flex"
            justifyContent="space-between"
            alignItems={{ xs: "flex-start", sm: "center" }}
            flexDirection={{ xs: "column", sm: "row" }}
          >
            <MDBox display="flex" alignItems="center">
              <MDAvatar src={premiumTag} alt="premium" variant="rounded" size="xs" />
              <MDBox ml={2} lineHeight={0}>
                <MDTypography variant="h6" fontWeight="medium" color="dark">
                  {isFreePlan ? t("free-plan-active") : t("paid-plan-active", {planName})}
                </MDTypography>
                {!isFreePlan && !!shopConfig.planDetails?.render?.display_price && 
                  <MDTypography variant="button" fontWeight="regular" color="dark">
                    ${shopConfig.planDetails?.render?.display_price}/{t(shopConfig.planDetails?.render?.interval_type ?? "")}
                  </MDTypography>}
                {!!isFreePlan && <MDTypography variant="button" color="dark">{t("free-plan-upgrade")}</MDTypography>}
              </MDBox>
            </MDBox>
            <MDBox
              display="flex"
              justifyContent="flex-end"
              alignItems="center"
              width={{ xs: "100%", sm: "auto" }}
              mt={{ xs: 1, sm: 0 }}
            >
              <MDBox lineHeight={0} mx={2}>
                <MDButton
                  variant={isFreePlan ? "gradient" : "outlined"}
                  color={isFreePlan ? "info" : "secondary"}
                  component={Link}
                  to="/pricing"
                  size="small">
                    {isFreePlan ? t("see-all-plans") : t("modify-subscription")}&nbsp;
                    <Icon >arrow_forward</Icon>
                </MDButton>
              </MDBox>
            </MDBox>
          </MDBox>
          </MDBox>
          {disclaimer}
        </Card>
  );
}

export default PlansBilling;
