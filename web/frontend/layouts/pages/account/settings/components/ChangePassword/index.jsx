// @mui material components
import { useState } from "react";

import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import { toast } from "react-toastify";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDInput from "@/components/MDInput";
import {useTranslation} from "react-i18next";
import CircularProgress from "@mui/material/CircularProgress";
import { getAuth, updatePassword } from "firebase/auth";
import {app} from "@/firebase-config"

function ChangePassword() {
  const {t} = useTranslation();
  const [loader, setLoader] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");


  const handleChangePassword = async (event) => {
    event.preventDefault();
    if (loader) {
      return;
    }

    if (newPassword !== confirmNewPassword) {
      toast.error(t("passwords-dont-match"));
      return;
    }

    setLoader(true);

    const auth = getAuth(app);
    const user = auth.currentUser;

    if (user) {
      try {
        await updatePassword(user, newPassword)
        toast.success(t("password-updated"));
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
        setLoader(false);
      } catch (error) {
        console.log(error)
        switch (error.code) {
          case 'auth/weak-password':
            toast.error(t("weak-password"));
            break;
          case 'auth/requires-recent-login':
            toast.error(t("requires-recent-login"));
            break;
          default:
            toast.error(t("something-went-wrong"));
            break;
        }
        setLoader(false);
      }
    }
  };

  const passwordRequirements = [
    t("one-special-characters"),
    t("min-6-characters"),
    t("one-number"),
    t("change-it-often"),
  ];

  const renderPasswordRequirements = passwordRequirements.map((item, key) => {
    const itemKey = `element-${key}`;

    return (
      <MDBox key={itemKey} component="li" color="text" fontSize="1.25rem" lineHeight={1}>
        <MDTypography variant="button" color="text" fontWeight="regular" verticalAlign="middle">
          {item}
        </MDTypography>
      </MDBox>
    );
  });

  return (
    <Card id="change-password">
      <MDBox p={3}>
        <MDTypography variant="h5">
          {t("change-password")}
        </MDTypography>
      </MDBox>
      <MDBox component="form" pb={3} px={3} onSubmit={handleChangePassword}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MDInput
              fullWidth
              label={t("current-password")}
              inputProps={{ type: "password", autoComplete: "", value: currentPassword, onChange: (e) => setCurrentPassword(e.target.value) }}
            />
          </Grid>
          <Grid item xs={12}>
            <MDInput
              fullWidth
              label={t("new-password")}
              inputProps={{ type: "password", autoComplete: "", value: newPassword, onChange: (e) => setNewPassword(e.target.value) }}
            />
          </Grid>
          <Grid item xs={12}>
            <MDInput
              fullWidth
              label={t("confirm-new-password")}
              inputProps={{ type: "password", autoComplete: "", value: confirmNewPassword, onChange: (e) => setConfirmNewPassword(e.target.value) }}
            />
          </Grid>
        </Grid>
        <MDBox mt={6} mb={1}>
          <MDTypography variant="h5">{t("password-requirements")}</MDTypography>
        </MDBox>
        <MDBox mb={1}>
          <MDTypography variant="body2" color="text">
            {t("please-follow-guide")}
          </MDTypography>
        </MDBox>
        <MDBox display="flex" justifyContent="space-between" alignItems="flex-end" flexWrap="wrap">
          <MDBox component="ul" m={0} pl={3.25} mb={{ xs: 8, sm: 0 }}>
            {renderPasswordRequirements}
          </MDBox>
          <MDBox ml="auto">
            <MDButton type="submit" variant="gradient" color="dark" size="small">
              {!loader ? t("update-password") : <CircularProgress size={20} color="white" />}
            </MDButton>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

export default ChangePassword;
