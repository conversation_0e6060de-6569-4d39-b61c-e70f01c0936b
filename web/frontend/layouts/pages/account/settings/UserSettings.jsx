// @mui material components
import React from "react";
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Settings page components
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import ChangePassword from "@/layouts/pages/account/settings/components/ChangePassword";
import {useTranslation} from "react-i18next";

function UserSettings() {
  const {t} = useTranslation();

  return (
    <DashboardLayout>
      <DashboardNavbar absolute={true} />
      <MDBox mt={12}>
        <Grid container spacing={3} justifyContent="center">
          <Grid item xs={12} md={8} lg={6}>
            <MDBox mb={3}>
              <ChangePassword />
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default UserSettings;
