// @mui material components
import React, {useState, useEffect} from "react";
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MD<PERSON>ox from "@/components/MDBox";
import {DashboardLoader} from "@/components/AppLoader";
import { toast } from "react-toastify";
import { useSearchParams } from 'react-router-dom'
// Settings page components
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import Sidenav from "@/layouts/pages/account/settings/components/Sidenav";
import Header from "@/layouts/pages/account/settings/components/Header";
import BasicInfo from "@/layouts/pages/account/settings/components/BasicInfo";
// import Accounts from "@/layouts/pages/account/settings/components/Accounts";
import EmailReports from "@/layouts/pages/account/settings/components/EmailReports";
import Support from "@/layouts/pages/account/settings/components/Support";
import PlansBilling from "@/layouts/pages/account/settings/components/PlansBilling";
import DataSync from "@/layouts/pages/account/settings/components/DataSync";
import GlobalFilters from "@/layouts/pages/account/settings/components/GlobalFilters";
import axios from "axios";
import {useTranslation} from "react-i18next";
import {useCancellableAxios, useMaterialUIController} from "@/context";
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "@/constants";

import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

// Material Dashboard 2 PRO React base styles
import breakpoints from "@/assets/theme/base/breakpoints";
import { Integrations } from "@/layouts/integrations";

const settingOptions = {
  "store" : [
    { id: "store" , icon: "store_front", label: "store" },
    { id: "integrations" , icon: "hub", label: "integrations" },
    { id: "global-filters", icon: "tune", label: "global_filters.title" },
    { id: "scheduled-reports" , icon: "move_to_inbox", label: "scheduled-reports" },
    { id: "subscription" , icon: "stars_rounded", label: "subscription" },
    { id: "support" , icon: "support", label: "support-settings-title" },
  ]
}


function Settings() {
  const {t} = useTranslation();
  const [controller, dispatch] = useMaterialUIController();
  const { selectedShop, loginConfig, shopConfig } = controller;
  const [loader, setLoader] = useState(true);
  const [settings, setSettings] = useState({});
  const [selectedOption, setSelectedOption] = useState("store");
  const [tabValue, setTabValue] = useState("store");

  const [searchParams, setSearchParams] = useSearchParams();
  let paramErr = searchParams.get('err')
  let selectedOpt = searchParams.get('opt')

  const axiosInstance = useCancellableAxios();

  const fetchSettings = () => {
    let reqData = {};
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
    }

    setLoader(true);
    axiosInstance
      .post("/api/settings", reqData)
      .then((response) => {
        setSettings(response.data);
        setLoader(false);
      })
      .catch((error) => {
        console.log(error);
        setLoader(false);
        !axios.isCancel(error) && toast.error(t("something-went-wrong"));
      });
  };

  useEffect(() => {
    if (!!paramErr) {
      toast.error(t("something-went-wrong"));
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [selectedShop]);

  let availableOptions = settingOptions[tabValue] ?? [];
  let filteredOptions = availableOptions.filter((option) => {
    if (option.id == "subscription") {
      return !!shopConfig.subscription_enabled;
    }
    if (option.id == "global-filters") {
      return !!shopConfig.useMigrationDataset;
    }
    if (option.id == "workspace-members") {
      // NOTE: including subtab for users where role is not defined,
      // assuming they are admins who are not yet added in workspace_users table
      return !loginConfig.userData.role || [WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(loginConfig.userData.role);
    }
    return true;
  });

  useEffect (() => {
    if (selectedOpt && filteredOptions.map((opt) => opt.id).includes(selectedOpt)) {
      setSelectedOption(selectedOpt);
    }
  }, [selectedOpt]);

  let isLoading = loader || shopConfig.loading;

  const updateSettings = (data, finishCallback) => {
    let reqData = {
      settings : data
    };
    if (!!selectedShop) {
      reqData.selectedShop = selectedShop;
    }

    return axiosInstance
      .post("/api/settings/update", reqData)
      .then((response) => {
        if (response.data.error) {
          toast.error(response.data.error);
        } else {
          toast.success(t("saved-successfully"));
          finishCallback();
        }
        return response;
      })
      .catch((error) => {
        console.log(error);
        !axios.isCancel(error) && toast.error(t("something-went-wrong"));
        finishCallback();
      });
  };

  return (
    <DashboardLayout>
      <DashboardNavbar absolute={true} />
      <MDBox mt={12}>
        {isLoading && <DashboardLoader height="85vh" />}
        {!isLoading && <Grid container spacing={3}>
          <Grid item xs={12} lg={3}>
            <Sidenav options={filteredOptions} setSelectedOption={setSelectedOption} selectedOption={selectedOption} />
          </Grid>
          <Grid item xs={12} lg={9}>
            <MDBox mb={3}>
              <Grid container spacing={3}>
                {'store' == selectedOption && <Grid item xs={12}>
                  <Header />
                </Grid>}
                {'store' == selectedOption && <Grid item xs={12}>
                  <BasicInfo updateSettings={updateSettings} />
                </Grid>}
                {'store' == selectedOption && <Grid item xs={12}>
                  <DataSync />
                </Grid>}
                {'integrations' == selectedOption && <Grid item xs={12}>
                  <Integrations />
                </Grid>}
                {'scheduled-reports' == selectedOption && <Grid item xs={12}>
                  <EmailReports settings={settings} updateSettings={updateSettings} fetchSettings={fetchSettings}/>
                </Grid>}
                {'subscription' == selectedOption && <Grid item xs={12}>
                  <PlansBilling />
                </Grid>}
                {'global-filters' == selectedOption && <Grid item xs={12}>
                  <GlobalFilters />
                </Grid>}
                {'support' == selectedOption && <Grid item xs={12}>
                  <Support />
                </Grid>}
              </Grid>
            </MDBox>
          </Grid>
        </Grid>}
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default Settings;
