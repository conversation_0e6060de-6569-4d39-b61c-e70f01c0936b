import CustomerSegments from "./CustomerSegments";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import ReviewBar from "@/examples/ReviewBar";

const ORDERED_SECTIONS = [
  "section-cust-quartiles",
  "section-cust-order-freq",
  "section-cust-percentiles"
];

function CustomerSegmentsIndex() {

  return (
    <DashboardLayout>
      <DashboardNavbar sections={ORDERED_SECTIONS}/>
      <CustomerSegments />
      <ReviewBar />
      <Footer />
    </DashboardLayout>
  );
}

export default CustomerSegmentsIndex;
