import React from "react";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

import Repeat<PERSON>ie<PERSON>hart from "@/layouts/customer/customersegments/CustomerRepeatChart.jsx";
import CustomerPercentileTable from "@/layouts/customer/customersegments/CustomerPercentileTable.jsx";
import CustomerPercentileChart from "@/layouts/customer/customersegments/CustomerPercentileChart.jsx";
import { Element } from 'react-scroll';

export default function DataPieChart(props) {
    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Element name="section-cust-quartiles" >
                <Card>
                    <CustomerPercentileTable {...props} />
                </Card>
                </Element>
            </Grid>
            <Grid item xs={12} sm={12} lg={12}>
                <Element name="section-cust-order-freq" >
                <Card sx={{ height: "100%" }}>
                    <RepeatPieChart {...props} />
                </Card>
                </Element>
            </Grid>
            <Grid item xs={12} sm={12} lg={12}>
                <Element name="section-cust-percentiles" >
                <Card sx={{ height: "100%" }}>
                    <CustomerPercentileChart {...props} />
                </Card>
                </Element>
            </Grid>
        </Grid>
    )
}
