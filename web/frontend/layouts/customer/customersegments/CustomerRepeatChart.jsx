import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Cell,
    ResponsiveContainer,
} from "recharts";
// antd imports
import {Spin} from "antd";
import Empty from "@/components/EmptyChart";
import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";
import MDButton from "@/components/MDButton";
import { Link } from "react-router-dom";
import {tracker} from "@/context";

// @mui material components
import Grid from "@mui/material/Grid";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDBadgeDot from "@/components/MDBadgeDot";
import CircularProgress from "@mui/material/CircularProgress";

const PIE_COLORS = [
    { start: "#344767", end: "#56688c" },
    { start: "#EC407A", end: "#D81B60" },
    { start: "#747b8a", end: "#495361" },
    { start: "#49a3f1", end: "#1A73E8" },
    { start: "#66BB6A", end: "#43A047" },
]

import { useTranslation } from "react-i18next";

export default function RepeatPieChart(props) {
    const {t} = useTranslation();
    return (
        <RepeatPieChartRoot
            {...props}
            t={t}
         />
    )
}

class RepeatPieChartRoot extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        let { response, loading, t } = this.props;
        let { order_count } = response;
        var data = order_count;

        if (!response || Object.keys(response).length == 0 || !data || data.length == 0) {
            if (loading) {
                return (
                    <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="300px" height="100%">
                        <CircularProgress color="secondary" />
                    </MDBox>
                );
            }

            return <Empty />;
        }

        if (data && data.length > 0) {
            data = data.map((x) => {
                x.name = t(x.name)
                x.display = t(x.display)
                return x
            })
        }

        return (
            <Spin
                indicator={
                    <CircularProgress color="secondary" />
                }
                spinning={loading}
            >
                <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={2} px={2}>
                    <MDTypography variant="h6" className="card-title-default">{t("cust-order-freq")}</MDTypography>
                    <MDBox display="flex" flexDirection="row">
                        <MDButton mr={1} variant="outlined" color="dark" component={Link} to="/benchmarks" sx={{fontSize:"12px", p:"5px !important"}} size="small">
                            <Icon fontSize="small">leaderboard</Icon>&nbsp;
                            {t("benchmarks")}&nbsp;
                            <Icon fontSize="small">arrow_outward</Icon>&nbsp;
                        </MDButton>&nbsp;
                        {this.props.exportButton}
                    </MDBox>
                </MDBox>
                <Divider flexItem variant="fullWidth" sx={{width: "100%"}} />
                <MDBox mt={3} mb={3}>
                    <Grid container alignItems="center">
                    <Grid item xs={7} ml={2}>
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <defs>
                                {data.map((entry, index) => (
                                    <linearGradient id={`myGradient${index}`} key={index}>
                                        <stop
                                            offset="0%"
                                            stopColor={PIE_COLORS[index % PIE_COLORS.length].start}
                                        />
                                        <stop
                                            offset="100%"
                                            stopColor={PIE_COLORS[index % PIE_COLORS.length].end}
                                        />
                                    </linearGradient>
                                ))}
                                </defs>
                                <Pie
                                    data={data}
                                    dataKey="value"
                                    nameKey="name"
                                    cx="40%"
                                    cy="50%"
                                    fill="#8884d8"
                                >
                                    {data.map((entry, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={`url(#myGradient${index})`}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltipPieChart />} />
                            </PieChart>
                        </ResponsiveContainer>
                    </Grid>
                    <Grid item xs={3}>
                        <MDBox pr={1}>
                        <MDBox mb={1}>
                            <MDBadgeDot color="dark" size="sm" badgeContent={t("cust-order-1-title")} />
                        </MDBox>
                        <MDBox mb={1}>
                            <MDBadgeDot color="primary" size="sm" badgeContent={t("cust-order-2-title")} />
                        </MDBox>
                        <MDBox mb={1}>
                            <MDBadgeDot color="secondary" size="sm" badgeContent={t("cust-order-3-title")} />
                        </MDBox>
                        <MDBox mb={1}>
                            <MDBadgeDot color="info" size="sm" badgeContent={t("cust-order-4-title")} />
                        </MDBox>
                        <MDBox mb={1}>
                            <MDBadgeDot color="success" size="sm" badgeContent={t("cust-order-4+-title")} />
                        </MDBox>
                        </MDBox>
                    </Grid>
                    </Grid>
                </MDBox>
            </Spin>
        );
    }
}

const CustomTooltipPieChart = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
        return (
            <div className="custom-tooltip">
                {payload[0].payload.display} : {payload[0].payload.value}
            </div>
        );
    }

    return null;
};

