import React from "react";
import axios from "axios";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./DataPieChart";
import DatePickerAnt  from "@/components/Filters/DatePickerFilter";
import { useCancellableAxios, tracker } from "@/context";

// Material Dashboard 2 PRO React components
import Icon from "@mui/material/Icon";
import Grid from "@mui/material/Grid";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import dayjs from 'dayjs';
import MDTooltip from "@/components/MDTooltip";
import Card from "@mui/material/Card";

let cancelTokenCust;

import {useMaterialUIController} from "@/context";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import MDExport from "@/components/MDExport";

export default function CustomerSegments(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig, shopConfig} = controller;
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    return (
        <CustomerSegmentsRoot
            {...props}
            selectedShop={selectedShop}
            loginConfig={loginConfig}
            shopConfig={shopConfig}
            axiosInstance={axiosInstance}
            {...selectedFilters}
            t={t}
         />
    )
}

class CustomerSegmentsRoot extends React.Component {
    constructor(props) {
        super(props);

        // defaults
        this.state = {
            loading_customer_data: true,
            customer_data: {},
        };

        this.fetchCustomerData = this.fetchCustomerData.bind(this);
    }

    componentDidUpdate = (prevProps) => {
        // Typical usage (don't forget to compare props):
        let shopChange = prevProps.selectedShop != this.props.selectedShop
        let filterChange = prevProps.start_date.getTime() != this.props.start_date.getTime()
            || prevProps.end_date.getTime() != this.props.end_date.getTime()

        if (shopChange || filterChange) {
            this.fetchCustomerData();
        }
    }

    componentDidMount = () => {
        this.fetchCustomerData(false);
    };

    fetchCustomerData = (should_track = true) => {

        const { start_date, end_date, t, axiosInstance } = this.props;
        var setState = (newState) => {
            return this.setState(newState);
        };

        if (typeof cancelTokenCust != typeof undefined) {
            cancelTokenCust.cancel("Operation canceled due to new request.");
        }

        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
        };

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, type: "customer profile"});
        }

        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }

        //Save the cancel token for the current request
        cancelTokenCust = axios.CancelToken.source();

        setState({ loading_customer_data: true });
        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/cprofile", reqData, { cancelToken: cancelTokenCust.token })
            .then(function (response) {
                if (response.data && response.data.errors) {
                    toast.error(t("something-went-wrong"));
                } else {
                    setState({customer_data: response.data});
                }
                setState({ loading_customer_data: false });
            })
            .catch((err) => {
                console.log("error", err);
                if (!axios.isCancel(err)) {
                    setState({ loading_customer_data: false });
                }
            });
    };

    componentWillUnmount = () => {
        if (typeof cancelTokenCust != typeof undefined) {
            cancelTokenCust.cancel("Operation canceled due to unmount.");
        }
    }

    render() {
        const {
            loading_customer_data,
            customer_data,
        } = this.state;

        const {t, loginConfig, shopConfig} = this.props;
        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;

        let dataPieChartProps = {
            loading: loading_customer_data,
            response: customer_data,
        };

        let exportButton = (
            <MDExport
                feature="basic_segments_export"
                disabled={!(loginConfig.allow_customer_export ?? false)}
                formats={["xlsx", "csv"]}
                options={{
                    type: "basic_segments_export",
                    fileName: "Customer Segments",
                    parameters: {
                        start_date: this.props.start_date,
                        end_date: this.props.end_date,
                    },
                }}
            />
        );


        const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        let tooltip_title = t("p-date-block-title-3-m");
        if (time_limit === "1_year") {
            tooltip_title = t("p-date-block-title-1-yr")
        }

        return (
            <>
                <MDBox mb={3} mt={2}>
                    <Card elevation={0} mb={4} my={4}>
                    <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" pl={2} pb={2}>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>
                                {t("acquisition-period")} &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                                </MDTooltip>}
                            </MDTypography>
                            <DatePickerAnt report="customer segments"/>
                        </Grid>
                    </Grid>
                    </Card>
                </MDBox>
                <DataPieChart {...dataPieChartProps} exportButton={exportButton} />
            </>
        );
    }
}
