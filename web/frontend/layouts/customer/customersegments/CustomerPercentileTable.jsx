import React from "react";

// antd imports
import {Spin, Progress} from "antd";

import Empty from "@/components/EmptyChart";

// @mui material components
import CircularProgress from "@mui/material/CircularProgress";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import DataTable from "@/examples/Tables/DataTable";

import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";

// Sales dashboard components
import DefaultCell from "@/layouts/dashboards/sales/components/DefaultCell";
import { useTranslation } from "react-i18next";

const PercentileTable = (props) => {
    let { response, loading, exportButton } = props;
    let { percentile_table } = response;
    const { t } = useTranslation();

    if (
        !response ||
        Object.keys(response).length == 0 ||
        !percentile_table ||
        Object.keys(percentile_table).length == 0 || 
        !percentile_table.data ||
        percentile_table.data.length == 0
    ) {
        if (loading) {
            return (
                <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="300px" height="100%">
                    <CircularProgress color="secondary" />
                </MDBox>
            );
        }

        return <Empty />;
    }

    let { data } = percentile_table;

    const dataTableRows = data.map(row => {
        return {
            quartile : <DefaultCell>{row.quartile}</DefaultCell>,
            order :    <DefaultCell>{row.order}</DefaultCell>,
            revenue :    <DefaultCell>{row.revenue}</DefaultCell>,
            order_freq :    <DefaultCell>{row.order_freq}</DefaultCell>,
            ltv :    (
                <MDBox display="flex" alignItems="center" pr={2} width={"100%"}>
                    <MDBox mr={2}>
                        <MDTypography variant="button" fontWeight="medium">
                        {row.ltv}
                        </MDTypography>
                    </MDBox>
                    <MDBox width={"200px"}>
                        <Progress
                            style={{width:'200px'}}
                            percent={row.ltv_percent ?? '0'}
                            showInfo={false}
                            strokeWidth={10}
                            strokeColor="#e91e63"
                        />
                    </MDBox>
                </MDBox>
            )
        }
    })

    const dataTableData = {
        columns: [
            { Header: t("quartile"), accessor: "quartile" },
            { Header: t("orders"), accessor: "order" },
            { Header: t("revenue"), accessor: "revenue", align: "center" },
            { Header: t("order-freq"), accessor: "order_freq", align: "center" },
            { Header: t("customer-ltv"), accessor: "ltv", width: "30%"  },
        ],
        rows: dataTableRows
    };

    return (
        <Spin indicator={<CircularProgress color="secondary" />} spinning={loading}>
            <MDBox>
                <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={2} px={2}>
                    <MDTypography variant="h6" className="card-title-default">{t("cust-quartile-rev")}</MDTypography>
                    {exportButton}
                </MDBox>
            </MDBox>
            <MDBox py={1}>
                <DataTable
                    table={dataTableData}
                    entriesPerPage={false}
                    showTotalEntries={false}
                    isSorted={false}
                    noEndBorder
                />
            </MDBox>

            <Divider />

            <MDBox>
                <MDTypography variant="caption" fontWeight="regular" color="primary" px={3} pb={2} display="block" sx={{lineHeight: "2"}}>
                    {t("pro-tip")} <Icon 
                                fontSize="medium"
                                color="light"
                                sx={{
                                    fontSize:"1.2rem",
                                    cursor:"pointer",
                                    verticalAlign:"middle",
                                    position:"relative"
                                }} >tips_and_updates</Icon> - 
                    {t("cust-quartile-rev-tip")}
                </MDTypography>
            </MDBox>

        </Spin>
    );
}

export default PercentileTable;