import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    ResponsiveC<PERSON>r,
    AreaChart,
    Area,
    XAxis,
    YAxis,
    CartesianGrid,
} from "recharts";
// antd imports
import {Spin} from "antd";

// @mui material components
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

import Empty from "@/components/EmptyChart";
import CircularProgress from "@mui/material/CircularProgress";
import { useTranslation } from "react-i18next";
import { Divider } from "@mui/material";

const CustomTooltip = ({ active, payload, label }) => {
    const {t} = useTranslation();
    if (active && payload && payload.length && label != "100%") {
        return (
            <div className="custom-tooltip">
                {t("cust-percentile-tooltip", {
                    perc : payload[0].payload.top_percentage,
                    value : payload[0].payload.value_formatted
                })}
            </div>
        );
    }

    return null;
};

export default function Percentile<PERSON>hart(props) {
    const {t} = useTranslation();
    return (
        <PercentileChartRoot {...props} t={t} />
    )
}

class PercentileChartRoot extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        let { response, loading, t } = this.props;
        let { percentile_chart } = response;

        if (!response || Object.keys(response).length == 0 || !percentile_chart || percentile_chart.length == 0) {
            if (loading) {
                return (
                    <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="300px" height="100%">
                        <CircularProgress color="secondary" />
                    </MDBox>
                );
            }

            return <Empty />;
        }

        return (
            <Spin indicator={<CircularProgress color="secondary" />} spinning={loading}>
                <MDBox display="flex" justifyContent="space-between" alignItems="center" pt={2} px={2}>
                    <MDTypography variant="h6" className="card-title-default">{t("cust-percentile")}</MDTypography>
                </MDBox>
                <Divider flexItem variant="fullWidth" sx={{width: "100%"}} />
                <MDBox mt={2} mr={2} mb={3}>
                    <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={percentile_chart}>
                            <CartesianGrid strokeDasharray="4 4" />
                            <defs>
                                <linearGradient id={`color1`} x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="0%" stopColor="#344767" />
                                    <stop offset="100%" stopColor="#56688c" />
                                </linearGradient>
                            </defs>
                            <YAxis />
                            <Area
                                type="monotone"
                                dataKey={"value"}
                                stroke="#344767"
                                fillOpacity={1}
                                fill="url(#color1)"
                            />
                            <Area
                                type="monotone"
                                dataKey={"value_top"}
                                stroke="#344767"
                                fillOpacity={1}
                                fill="url(#color1)"
                            />
                            <XAxis
                                dataKey="name"
                                label={{
                                    value: `Percentile of customers`,
                                    position: "bottom",
                                    offset: -5,
                                }}
                                ticks={["0%", "25%", "50%", "75%", "95%"]}
                            />
                            <Tooltip content={<CustomTooltip />} />
                        </AreaChart>
                    </ResponsiveContainer>
                </MDBox>
            </Spin>
        );
    }
}