
export const rfmSegmentWiseCount = (period = "all_time") => {
  return {
    "measures": [
      "RfmSegmentsAgg.customerCount",
      "RfmSegmentsAgg.percentage"
    ],
    "dimensions": [
      "RfmSegmentsAgg.shopId",
      "RfmSegmentsAgg.rfmSegment"
    ],
    "filters": [
      {
        "member": "RfmSegmentsAgg.period",
        "operator": "equals",
        "values": [period]
      }
    ],
    "order": {
      "RfmSegmentsAgg.customerCount": "desc"
    }
  }
}

export const dataTableQuery = (selectedSegments, period = "all_time", limit = 100, offset = 0) => {

    let filters = [
      {
        "member": "RfmSegments.period",
        "operator": "equals",
        "values": [period]
      }
    ];

    if (!!selectedSegments) {
      filters.push({
        "member": "RfmSegments.rfmSegment",
        "operator": "equals",
        "values": [selectedSegments]
      })
    }
  
    // TODO - add pagination
    return {
        "limit": limit,
        "offset": offset,
        "dimensions": [
            "RfmSegments.customerId",
            "RfmSegments.rfmScore",
            "RfmSegments.rfmSegment",
            "RfmSegments.customerFirstName",
            "RfmSegments.customerEmail",
            "RfmSegments.customerPhone",
            "RfmSegments.customerLastName"
        ],
        "filters": filters,
        "measures": [
            "RfmSegments.lastOrderSince",
            "RfmSegments.totalOrders",
            "RfmSegments.totalSpend",
            "RfmSegments.lastOrderCreatedAt"
        ],
        "order": {
            "RfmSegments.lastOrderCreatedAt": "desc"
        }
    }
}