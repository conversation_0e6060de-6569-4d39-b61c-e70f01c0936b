import React from "react";
import numeral from "numeral";
import ContactsOutlinedIcon from '@mui/icons-material/ContactsOutlined';
import TipsAndUpdatesOutlinedIcon from '@mui/icons-material/TipsAndUpdatesOutlined';

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import Divider from "@mui/material/Divider";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDProgress from "@/components/MDProgress";
import { useTranslation } from 'react-i18next';

function SelectedSegmentStats({embedded, loading, segment}) {
    const { t } = useTranslation();

    return (
        <Card sx={embedded ? { boxShadow: "none" } : {  }}>
            <MDBox display="flex" alignItems="center" pt={1} px={1}>
                <MDBox
                    display="grid"
                    justifyContent="center"
                    alignItems="center"
                    bgColor={segment.color}
                    color="white"
                    width="4rem"
                    height="4rem"
                    shadow="md"
                    m={2}
                    borderRadius="lg"
                    variant="gradient"
                >
                    <Icon fontSize="default" className={segment.icon} />
                </MDBox>
                <MDBox ml={1} lineHeight={1}>
                <MDTypography
                    variant="button"
                    fontWeight="regular"
                    textTransform="capitalize"
                    color="text"
                >
                    {t(segment.title)} {t("cust")}
                </MDTypography>
                {(!loading && segment.count) ? (
                    <MDTypography variant="h5" fontWeight="bold">
                        {numeral(segment.count).format("0,0")}
                    </MDTypography>
                ) : null}
                </MDBox>
                <MDBox width="25%" ml="auto">
                {!loading && <MDTypography display="block" variant="button" fontWeight="medium" color="text">
                    {segment.percent}%
                </MDTypography>}
                <MDBox mt={0.25} mr={0.25}>
                    <MDProgress variant="gradient" color={segment.color} value={loading ? 0 : segment.percent} />
                </MDBox>
                </MDBox>
            </MDBox>
            <Divider />
            {!embedded && <Grid container spacing={1} pt={1} px={1}>
            <Grid item xs={12} md={6} xl={6} sx={{ display: "flex" }}>
                <Card sx={{ height: "100%", boxShadow: "none" }}>
                <MDBox p={2}>
                    <ContactsOutlinedIcon fontSize="medium" color="secondary" />
                    <br/>
                    <MDBox mb={2} lineHeight={1}>
                        <MDTypography variant="button" color="text" fontWeight="regular">
                            {t(segment.description)}
                        </MDTypography>
                    </MDBox>
                    <MDBox opacity={0.3}>
                        <Divider />
                    </MDBox>
                </MDBox>
                </Card>
              <Divider orientation="vertical" sx={{ mx: 0 }} />
            </Grid>
            <Grid item xs={12} md={6} xl={6} sx={{ display: "flex" }}>
                <Card sx={{ height: "100%", boxShadow: "none" }}>
                <MDBox p={2}>
                    <MDBox mb={2} lineHeight={1}>
                        <TipsAndUpdatesOutlinedIcon fontSize="medium" color="secondary"/>
                        <br/>
                        <MDTypography variant="button" color="text" fontWeight="regular">
                            {t(segment.tooltip)}
                        </MDTypography>
                    </MDBox>
                    <MDBox opacity={0.3}>
                        <Divider />
                    </MDBox>
                </MDBox>
                </Card>
            </Grid>
            <Divider />
            </Grid>}
        </Card>
    )
}

export default SelectedSegmentStats;