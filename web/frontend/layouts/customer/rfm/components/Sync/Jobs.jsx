import React, { useState, useEffect, useRef } from "react";
import Grid from "@mui/material/Grid";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { useCancellableAxios, useMaterialUIController } from "@/context";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import Card from "@mui/material/Card";
import Alert from '@mui/material/Alert';
import JobsTable from "@/layouts/customer/rfm/components/Sync/JobsTable";
import SelectedSegment from "@/layouts/customer/rfm/components/SelectedSegment";
import klaviyoLogo from '@/assets/images/logos/klaviyo.png';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import axios from "axios";



function KlaviyoJobs({ integration, selectedSegment, selectedPeriod }) {
    const { t } = useTranslation();
    const [loadingJobs, setLoadingJobs] = useState(false);
    const [loadingBtn, setLoadingBtn] = useState(false);
    const [jobs, setJobs] = useState([]);
    const [jobSubmitted, setJobSubmitted] = useState(false);
    const [controller] = useMaterialUIController();
    const { selectedShop } = controller;


    const timeoutId = useRef(null);
    const axiosInstance = useCancellableAxios();

    const fetchAutomationJobs = () => {

        let reqData = {
            job_type : "klaviyo_rfm_tagging"
        };
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setLoadingJobs(true);
        axiosInstance.post('/api/automation/jobs', reqData)
          .then((response) => {

            if (!response || !response.data) {
                toast.error("No response from server");
                setLoadingJobs(false);
                return;
            }

            if (response.data.error) {
                toast.error(response.data.error);
                setLoadingJobs(false);
                return;
            }

            setJobs(response.data);
            setLoadingJobs(false);

            if (!Array.isArray(response.data) || response.data.length === 0) {
                return;
            }

            // Check if any jobs have a status of 0 or 1
            const hasActiveJobs = response.data.some(job => job.status === 0 || job.status === 1);

            // If there are active jobs, schedule the next fetch
            if (hasActiveJobs) {
                timeoutId.current = setTimeout(fetchAutomationJobs, 30000); // after 30 sec
            } else {
                if (timeoutId.current) {
                    clearTimeout(timeoutId.current);
                }
            }
          })
          .catch((error) => {
            !axios.isCancel(error) && toast.error(t("something-went-wrong"));
            console.error("There was an error fetching the jobs data!", error);
            setLoadingJobs(false);
          });
    }

    useEffect(() => {
        fetchAutomationJobs();
    
        // Clear the timeout when the component unmounts
        return () => {
            if (timeoutId.current) {
                clearTimeout(timeoutId.current);
            }
        };
    }, [selectedShop]);

    const submitKlaviyoJob = async () => {

        if (loadingBtn) {
            return;
        }

        const reqData = {
            segment: selectedSegment.segment ?? "",
            period: selectedPeriod,
            request_id: integration.request_id ?? ""
        };

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        try {
            setLoadingBtn(true);
            const res = await axiosInstance.post('/api/automation/klaviyo/submit', reqData);
            if(res.data.error){
                setLoadingBtn(false);
                toast.error(res.data.error);
                return;
            }

            setLoadingBtn(false);
            setJobSubmitted(true);
            toast.success(t("sync-submitted"));

            if (timeoutId.current) {
                clearTimeout(timeoutId.current);
            }    

            fetchAutomationJobs();
        } catch (err) {
            setLoadingJobs(false);
            toast.error(err.response.data.error);
        }
    };

    return (
        <MDBox bgColor="light" borderRadius="lg" py={3} width="100%">
            <Grid container spacing={3}>
                <Grid item xs={10} lg={10} mx="auto">
                    <Card>
                    <MDBox p={4}>
                    <SelectedSegment
                        embedded={true}
                        loading={false}
                        segment={selectedSegment}
                    />
                    <MDBox width="100%" my={2}>
                        <Alert severity="warning">
                            <MDTypography variant="button" color="dark">
                                {t("rfm-sync-disclaimer")}
                            </MDTypography>
                        </Alert>
                    </MDBox>
                    <MDBox width="100%">
                    {jobSubmitted && (
                        <MDButton
                            variant="gradient"
                            color={"success"}
                            fullWidth
                            disabled={true}
                        >
                        <><CheckCircleOutlineIcon fontSize="medium"/>&nbsp;{t("done")}</>
                        </MDButton>
                    )}
                    {!jobSubmitted && <MDButton
                        variant="outlined"
                        color={"dark"}
                        fullWidth
                        onClick={submitKlaviyoJob}
                        disabled={loadingBtn || !selectedSegment.segment || !selectedSegment.count}
                    >
                        <MDBox component="img" src={klaviyoLogo} alt="klaviyoLogo" width="1.2rem" mr={0.4}  />
                        {t("tag-segment", { segment: selectedSegment.segment })}
                    </MDButton>}
                    </MDBox>
                    </MDBox>
                    </Card>
                </Grid>
                <Grid item xs={10} lg={10} mx="auto">
                    <JobsTable jobs={jobs} loading={loadingJobs} />
                </Grid>
            </Grid>
        </MDBox>
    );
}

export default KlaviyoJobs;
