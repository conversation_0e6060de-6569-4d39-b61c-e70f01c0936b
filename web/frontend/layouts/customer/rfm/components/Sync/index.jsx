import React from 'react';
import Dialog from '@mui/material/Dialog';
import NiceModal, { useModal } from '@ebay/nice-modal-react';

import MDBox from '@/components/MDBox';
// @mui material components
import Onboarding from "@/layouts/integrations/klaviyo/components/Onboarding";
import KlaviyoJobs from "@/layouts/customer/rfm/components/Sync/Jobs";


// assumes klaviyo is connected and opens a flow to tag segments
export const KlaviyoJobsDialog = NiceModal.create(({selectedSegment, selectedPeriod, integration, onExit}) => {
  const modal = useModal();

  return (
    <Dialog
      open={modal.visible}
      onClose={() => {
        modal.hide()
        if (onExit) {
          onExit();
        }
      }}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      maxWidth="md"
    >
        <MDBox minHeight={"40vh"} display="flex" justifyContent="space-between" alignItems="center" >
            <KlaviyoJobs
                integration={integration}
                selectedSegment={selectedSegment}
                selectedPeriod={selectedPeriod}
            />
        </MDBox>
    </Dialog>
  );
});


// assumes klaviyo is not connect and opens a flow to connect klaviyo
export const KlaviyoConnectDialog = NiceModal.create(({onExit, onSuccess}) => {
  const modal = useModal();

  return (
    <Dialog
      open={modal.visible}
      onClose={() => {
        modal.hide()
        if (onExit) {
          onExit();
        }
      }}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      maxWidth="md"
    >
        <MDBox minHeight={"40vh"} display="flex" justifyContent="space-between" alignItems="center" >
            <Onboarding
                integration={null}
                onSuccess={onSuccess}
            />
        </MDBox>
    </Dialog>
  );
});

export default KlaviyoJobsDialog;