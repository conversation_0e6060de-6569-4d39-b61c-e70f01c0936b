import React, { useMemo } from "react";
import CircularProgress from '@mui/material/CircularProgress';
import DataTable from "@/examples/Tables/DataTable";
import {CardLoader} from "@/components/AppLoader";
import Grid from "@mui/material/Grid";
import MDTypography from "@/components/MDTypography";
import Divider from "@mui/material/Divider";
import MDBox from "@/components/MDBox";
import MDBadge from "@/components/MDBadge";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

function JobsTable({jobs, loading}) {
  const {t} = useTranslation();

  let tableData = useMemo(() => ({
    columns: [
      { Header: t("rfm-segment"), accessor: "rfmSegment"},
      { Header: t("started-at"), accessor: "createdAt"},
      { Header: t("status"), accessor: "statusMessage"},
    ],
    rows: jobs.map((job) => {
      let jobStatus = null
      if (job.status == 0) {
        jobStatus = <MDBadge
            variant="gradient"
            size="lg" color="warning"
            badgeContent={<>{t("pending")} &nbsp;<CircularProgress size={15} color="white" /></>}
            container />
        } else if (job.status == 1 || job.status == 2) {
          jobStatus = (<>
              <MDBadge variant="gradient" size="lg" color="success"
                  badgeContent={<>{job.status == 1 ? t("running") : t("finished")} {job.status == 1 ? (<>&nbsp;<CircularProgress size={15} color="white" /></>) : null} </>}
                  container/>
              </>)
      } else {
          jobStatus = <MDBadge variant="gradient" size="lg" color="error" badgeContent={t("failed")} container/>
      }

      return {
        rfmSegment: !!job.source && !!job.source.rfm_segment ? t(`${job.source.rfm_segment}-title`) : "All Segments",
        statusMessage: jobStatus,
        createdAt: dayjs(job.row_created_at).format("MMM D, YYYY h:mm A"),
      }
    }),
  }), [jobs, t]);

  return (
    <MDBox>
        <Grid container spacing={3} direction="row" justifyContent="space-between" alignItems="flex-end">
            <Grid item>
                <MDTypography variant="h5" color="secondary" fontWeight="regular">
                    {t("jobs-history")}
                </MDTypography>
            </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <MDBox pt={1} pb={3}>
          {(loading && (!jobs || jobs.length == 0)) ? <CardLoader height={"10vh"}/> : <DataTable
            entriesPerPage={{ defaultValue: 2, entries: [1, 2, 3, 5] }}
            showTotalEntries={false}
            canSearch={false}
            isSorted={false}
            table={tableData}
          />}
        </MDBox>
    </MDBox>
    );
}

export default JobsTable;
