import React from "react";
import NewVsExistingCustomers from '@/layouts/customer/newvsexisting/NewVsExistingCustomers';
import DatePickerAnt  from "@/components/Filters/DatePickerFilter";
import {RelativeGroupingFilter} from "@/components/Filters/TimeFrameFilter";
import { new_vs_existing_metrics } from "@/components/Filters/MetricSelector";
import {tracker, useCancellableAxios, useMaterialUIController} from "@/context";
import {Link} from "react-router-dom";
import dayjs from 'dayjs';

// Material Dashboard 2 PRO React components
import Icon from "@mui/material/Icon";
import Grid from "@mui/material/Grid";
import Card  from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

import PillBar from '@/components/PillBar';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { CSVLink } from "react-csv";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";

import { useTranslation } from "react-i18next";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import ReviewBar from "@/examples/ReviewBar";
import {Element} from 'react-scroll';
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import FeatureBlurBox from "@/components/FeatureBlurBox";
import {toast} from "react-toastify";

export default function NewVsExisting(props) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, shopConfig} = controller;
    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    const isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;

    const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
    let tooltip_title = t("p-date-block-title-3-m");
    if (time_limit === "1_year") {
        tooltip_title = t("p-date-block-title-1-yr")
    }

    return (
        <DashboardLayout>
            <DashboardNavbar sections={["section-new-vs-existing"]}/>
            <DashboardFeatureBlock feature="new_vs_returning" />
                <MDBox mb={3} mt={2}>
                    <Card elevation={0} mb={4} my={4}>
                    <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center"  pl={2} pb={2}>
                        <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>
                            {t("time-period")} &nbsp;
                            {isSubscriptionEnabled && time_limit !== "unlimited" && (
                                <MDTooltip title={tooltip_title}>
                                    <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                                </MDTooltip>
                            )}
                        </MDTypography>
                            <DatePickerAnt report="new vs existing customer" />
                        </Grid>
                        <Grid item>
                            <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("group-by")}</MDTypography>
                            <RelativeGroupingFilter report="new vs existing customer" exclude={["day"]} />
                        </Grid>
                    </Grid>
                    </Card>
                </MDBox>
                <Element name="section-new-vs-existing">
                <NewVsExistingRoot
                    {...props}
                    selectedShop={selectedShop}
                    shopConfig={shopConfig}
                    {...selectedFilters}
                    hasTable={true}
                    hasExport={true}
                    axiosInstance={axiosInstance}
                    t={t}
                />
                </Element>
                <ReviewBar />
            <Footer />
        </DashboardLayout>
    )
}

export class NewVsExistingRoot extends React.Component {
    constructor(props) {
        super(props);

        // defaults
        this.state = {
            metric : "customer_count",
            loading_new_vs_existing: {},
            data : [],
        };

        this.handleNewExistingExport = this.handleNewExistingExport.bind(this);
        this.fetchNewVsExistingData = this.fetchNewVsExistingData.bind(this);
        this.handleMetricChange = this.handleMetricChange.bind(this);
        this.getNewVsExistingExportData = this.getNewVsExistingExportData.bind(this)
    }

    componentDidUpdate = (prevProps) => {
        // Typical usage (don't forget to compare props):
        let shopChange = prevProps.selectedShop != this.props.selectedShop
        let filterChange = prevProps.start_date.getTime() != this.props.start_date.getTime()
            || prevProps.end_date.getTime() != this.props.end_date.getTime()
            || prevProps.time_frame != this.props.time_frame

        if (shopChange || filterChange) {
            this.fetchNewVsExistingData(false)
        }
    }

    handleNewExistingExport = () => {
        const {shopConfig } = this.props;
        let reqData = {}

        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        if (isSubscriptionEnabled) {
            if (!shopConfig.planDetails?.features?.new_vs_returning_export) {
                tracker.event("Paywall", {...reqData, feature: "new_vs_returning_export"});
                NiceModal.show(PaywallDialog, {feature : "new_vs_returning_export"})
                return false
            }
        }

        tracker.event("Data Exported", {...reqData, report: "new vs existing customer"});
    }

    componentDidMount = () => {
        this.fetchNewVsExistingData(false)
    };

    handleMetricChange = (metric) => {
        tracker.mixpanel.track(`Switch Metric`, {
            report: "new vs existing customer",
            old_value: this.state.metric,
            new_value: metric
        });

        // const { updateIntercom } = this.props;
        // if (updateIntercom) {
        //     updateIntercom();
        // }
        this.setState({ metric: metric });
    };

    fetchNewVsExistingData = (should_track = true) => {

        const {start_date, end_date, time_frame, axiosInstance} = this.props;

        var setState = (newState) => {
            return this.setState(newState);
        };

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, report: "new vs existing customer"});
        }

        setState({ loading_new_vs_existing: true });


        let reqData = {
            type: 'metrics_summary',
            filters : {
                start_date : dayjs(start_date).format("YYYY-MM-DD"),
                end_date : dayjs(end_date).format("YYYY-MM-DD"),
                compare: false,
                time_frame,
                integration_request_ids: [],
                metrics : [
                    "shopify:new_cust_count",
                    "shopify:new_cust_order_count",
                    "shopify:new_cust_total_price",
                    "shopify:new_cust_count_pct",
                    "shopify:new_cust_order_count_pct",
                    "shopify:new_cust_total_price_pct",
                    "shopify:new_cust_aov",
                    "shopify:new_cust_arpu",
                    "shopify:returning_cust_count",
                    "shopify:returning_cust_order_count",
                    "shopify:returning_cust_total_price",
                    "shopify:returning_cust_count_pct",
                    "shopify:returning_cust_order_count_pct",
                    "shopify:returning_cust_total_price_pct",
                    "shopify:returning_cust_aov",
                    "shopify:returning_cust_arpu"
                ],
            },
        };
    
        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }
    
        axiosInstance.post('/api/report/data', reqData)
            .then(function (response) {

                if (!response.data || response.data.error) {
                    toast.error(t("something-went-wrong"));
                    return;
                }
    
                setState({ loading_new_vs_existing: false, data: response.data?.data?.current ?? [] });
            })
            .catch((err) => {
                console.log("error", err);
                setState({ loading_new_vs_existing: false });
            });
    };
    
    getNewVsExistingExportData = () => {

        let {data} = this.state;
        let {t} = this.props;
        if (!data || data.length == 0) {
            return {headers :[], data :[]}
        }

        let headers = [{
            label : "Period",
            key: "period"
        }];

        Object.keys(new_vs_existing_metrics).map((k) => {
            headers.push({
                label: k != "customer_count" ? t(new_vs_existing_metrics[k].label) + ` (${t("new")})` : `${t("cust")} (${t("new")})`,
                key: `new_customers.${k}`,
            })

            headers.push({
                label: k != "customer_count" ? t(new_vs_existing_metrics[k].label) + ` (${t("existing")})` : `${t("cust")} ${t("existing")}`,
                key: `existing_customers.${k}`,
            })
        });


        let exportData = [];
        for (var k in data) {
            let item = data[k]
            exportData.push({
                period : item.bucketName,
                new_customers: {
                    customer_count : item["shopify:new_cust_count"],
                    order_count : item["shopify:new_cust_order_count"],
                    total_sales : item["shopify:new_cust_total_price"],
                    aov : item["shopify:new_cust_aov"],
                    arpu : item["shopify:new_cust_arpu"],
                },
                existing_customers : {
                    customer_count : item["shopify:returning_cust_count"],
                    order_count : item["shopify:returning_cust_order_count"],
                    total_sales : item["shopify:returning_cust_total_price"],
                    aov : item["shopify:returning_cust_aov"],
                    arpu : item["shopify:returning_cust_arpu"],
                },
            });
        }
        return {headers, data: exportData}
    }

    render() {
        const {
            metric,
            data,
            loading_new_vs_existing
        } = this.state;

        const {time_frame, t, hasTable, hasExport, reportLink, shopConfig} = this.props;

        let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
        let isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.new_vs_returning_export ?? false);

        let {currency} = data && data.length > 0 ? data[0] : {currency : ""};

        let newVsExistingCardTitle = (
            <div>
                <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">{t("m-newvse-label")}</MDTypography>
                {!!new_vs_existing_metrics[metric].desc && <MDTooltip
                    title={
                        <div style={{ fontSize: "13px", padding: "5px" }}>
                            {t(new_vs_existing_metrics[metric].desc)}
                        </div>
                    }
                    placement="right"
                >
                    <InfoOutlinedIcon />
                </MDTooltip>}
            </div>
        );

        let pillbarOptions = Object.keys(new_vs_existing_metrics).map((k) => {
            return {
                label: t(new_vs_existing_metrics[k].label),
                value: k,
            };
        });

        let exportData = this.getNewVsExistingExportData()

        let newVsExistingExportButton = (
            <CSVLink
                data={exportData.data}
                headers={exportData.headers}
                filename={"New vs Existing Customers.csv"}
                onClick={this.handleNewExistingExport}
            >
                <MDBox>
                    <MDButton variant="outlined" color="dark" size="small">
                        <Icon>description</Icon>
                        &nbsp;{t("export-csv")}
                        {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                    </MDButton>
                </MDBox>
            </CSVLink>
        );

        let newVsExistingProps = {
            t,
            metric,
            currency,
            time_frame,
            tableData : exportData,
            loading : loading_new_vs_existing,
            hasTable,
            data
        }

        return (

        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
            <Grid item>
                {newVsExistingCardTitle}
            </Grid>
            <Grid item>
                <MDBox display="flex">
                    {hasExport && <MDBox ml={1}>
                        {newVsExistingExportButton}
                    </MDBox>}
                    {reportLink && <MDBox ml={1} display="flex" direction="row" alignItems="center">
                        <MDTypography variant="button" textTransform="capitalize" mr={1} sx={{fontSize:"13px"}}>{t("select-metric")}</MDTypography>
                        <PillBar
                                name="type"
                                isDropdown={false}
                                options={pillbarOptions}
                                value={metric}
                                onChange={this.handleMetricChange}
                            />
                    </MDBox>}
                </MDBox>
            </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />

        {!reportLink && <MDBox py={1.5} display="flex" justifyContent="center" alignItems="center">
            <PillBar
                name="type"
                isDropdown={false}
                options={pillbarOptions}
                value={metric}
                onChange={this.handleMetricChange}
            />
            </MDBox>
        }
        {!reportLink && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}

        <Grid container spacing={1.2} p={1.6} pb={2.4}>
            <Grid item xs={12} md={12} lg={12}>
                <FeatureBlurBox feature="new_vs_returning">
                <NewVsExistingCustomers {...newVsExistingProps} />
                </FeatureBlurBox>
            </Grid>
        </Grid>
        {reportLink && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
        {reportLink && <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="end" sx={{width :"100%"}}>
            <MDButton variant="outlined" color={"dark"} component={Link} to={reportLink} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                    {t("deepdive-btn")} &nbsp;
                    <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                </MDTypography>
            </MDButton>           
        </MDBox>}
        </Card>
        );
    }
}
