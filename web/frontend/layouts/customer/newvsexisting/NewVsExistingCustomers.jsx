import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    <PERSON><PERSON><PERSON><PERSON>,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    Legend,
    ResponsiveContainer,
} from "recharts";
// antd imports
import {Tooltip as AntToolTip} from "antd";
import {Spin} from "antd";

import { new_vs_existing_metrics } from "@/components/Filters/MetricSelector";
import Empty from "@/components/EmptyChart";
import CircularProgress from "@mui/material/CircularProgress";
import MDBox from "@/components/MDBox";

import DataTable from "@/examples/Tables/DataTable";
import Divider from "@mui/material/Divider";
import numeral from "numeral";

// Sales dashboard components
import DefaultCell from "@/layouts/dashboards/sales/components/DefaultCell";
import MDButton from "@/components/MDButton";
import { useTranslation } from "react-i18next";
import { getMetricBySlug } from "@/layouts/dashboards/metrics/metadata";
import {getMetricFormatterFn} from '@/util.jsx';

const CustomBarTooltip = ({ active, payload, label, metric }) => {
    const { t } = useTranslation();
    if (active && payload && payload.length) {
        return (
            <div
                className="custom-tooltip-full"
                style={{ backgroundColor: "rgba(255,255,255,0.95)" }}
            >
                <div className="custom-ant-tooltip-basic" style={{ width: 250, padding: "10px"}}>
                    <div style={{ fontSize: "13px", marginBottom: "5px" }}>{label}</div>
                    <div>
                        {payload.map((p, index) => {
                            if (!p || !p.value || !p.name) {
                                return null;
                            }

                            let showVal = p.value;
                            if (
                                p.dataKey &&
                                p.payload &&
                                p.payload.dataPayload 
                            ) {
                                showVal = p.payload.dataPayload[p.dataKey] ?? "";
                            }

                            return (
                                <div key={`item-${index}`} style={{paddingBottom: "2px", fontSize: "13px"}}>
                                    <MDBox display="flex" flexDirection="row" justifyContent="space-between" style={{color: p.color ?? "grey"}}>
                                        <div>
                                            {metric in new_vs_existing_metrics && metric != "customer_count"
                                                ? <span>
                                                    {t(new_vs_existing_metrics[metric].label)} ({p.name})
                                                </span>
                                                : <span>

                                                    {p.name}
                                                </span>
                                            }
                                        </div>
                                        <div>
                                            {showVal}
                                        </div>
                                    </MDBox>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        );
    }

    return null;
};

export default class NewVsExistingCustomers extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            selected : "",
            show_table : true
        };

        this.handleLabelClick = this.handleLabelClick.bind(this)
    }

    getMetricSlug = (ref) => {

        const {metric} = this.props;

        switch (metric) {
            case "customer_count":
                return `shopify:${ref}_cust_count`;
            case "order_count":
                return `shopify:${ref}_cust_order_count`;
            case "total_sales":
                return `shopify:${ref}_cust_total_price`;
            case "aov":
                return `shopify:${ref}_cust_aov`; // No percentage
            case "arpu":
                return `shopify:${ref}_cust_arpu`; // No percentage
            default:
                return "";
        }
    }

    handleLabelClick = (event) => {
        var dataKey = event.dataKey.trim()
        this.setState((prevState) => {
            if (prevState.selected == dataKey) {
                return {selected : ""}
            } else {
                return {selected : dataKey}
            }
        })
    }

    renderStackedBarChart () {
        const {data, metric, t, currency} = this.props;
        const {selected} = this.state;

        let chartData = [];

        for (var k in data) {
            let item = data[k];

            let newCustomerMetric = this.getMetricSlug("new")
            let newCustomerMetricConfig = getMetricBySlug(newCustomerMetric);
            let newCustomerMetricValFormatted = getMetricFormatterFn(newCustomerMetricConfig.format_type ?? "")(item[newCustomerMetric], currency)


            let returningCustomerMetric = this.getMetricSlug("returning")
            let returningCustomerMetricConfig = getMetricBySlug(returningCustomerMetric);
            let returningCustomerMetricValFormatted = getMetricFormatterFn(returningCustomerMetricConfig.format_type ?? "")(item[returningCustomerMetric], currency)

            chartData.push({
                name : item.bucketName,
                new_customers: item[newCustomerMetric] ?? 0,
                existing_customers : item[returningCustomerMetric] ?? 0,
                dataPayload : {
                    new_customers : newCustomerMetricValFormatted,
                    existing_customers : returningCustomerMetricValFormatted,
                }
            });
        }

        let is_stacked = true
        if (metric in new_vs_existing_metrics) {
            is_stacked = new_vs_existing_metrics[metric].is_stacked
        }

        var renderColorfulLegendText = (value, entry) => {
            const { color, inactive } = entry;
            return (
                <AntToolTip title={<span style={{color:"white", fontSize:"11.5px"}}>toggle</span>} >
                    <span style={{ color : (!inactive ? color : undefined) , cursor:"pointer", borderBottom: "1px dotted"}}>
                        {value}
                    </span>
                </AntToolTip>
            );
        }

        return (
            <ResponsiveContainer width="100%" height={360}>
                <BarChart
                    barCategoryGap={"15%"}
                    barSize={25}
                    height={360}
                    data={chartData}
                    style={{fontSize:"50px !important"}}
                    >
                        <Tooltip filterNull content={<CustomBarTooltip metric={metric} />} />
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Legend iconSize={10}
                            iconType="square"
                            align="center"
                            verticalAlign="bottom"
                            layout="horizontal"
                            wrapperStyle={{paddingLeft:10, paddingTop: 20}}
                            onClick={this.handleLabelClick}
                            formatter={renderColorfulLegendText} />
                        <Bar 
                            hide={!!selected && selected == "new_customers"}
                            dataKey="new_customers"
                            stackId="a"
                            fill="#344767"
                            name={t("new-customers")}
                        />
                        <Bar
                            hide={!!selected && selected == "existing_customers"}
                            dataKey="existing_customers"
                            stackId={is_stacked ? "a" : "b"}
                            fill="#e91e63"
                            name={t("existing-customers")}
                        />
                </BarChart>
            </ResponsiveContainer>
        )
    }

    render() {
        const { loading, data, tableData, t, hasTable } = this.props;
        const {show_table} = this.state;

        if (!data || data.length == 0) {

            if (loading) {
                return (
                    <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="350px" height="100%">
                        <CircularProgress color="secondary" />
                    </MDBox>
                );
            }

            return (
                <Empty
                    description={
                        <p>
                            {t("no-orders-found")}
                        </p>
                    }
                />
            );
        }

        const dataTableData = {
            columns: tableData.headers.map((h, i) => {
                return {
                    Header: h.label,
                    className: i == 0 ? "" : "right-aln",
                    accessor: h.key.split(".").join("_"),
                }
            }),
            rows: tableData.data.map((row) => {
                let newRow = {}
                for (var k in row) {
                    if (typeof row[k] !== "object") {
                        newRow[k] = <DefaultCell>{row[k]}</DefaultCell>
                        continue
                    }

                    for (var k2 in row[k]) {
                        newRow[k + "_" + k2] = <DefaultCell>{numeral(row[k][k2]).format("0,0")}</DefaultCell>
                    }
                }

                return newRow
            }),
        };

        return (
            <Spin
                indicator={
                    <CircularProgress color="secondary" />
                }
                spinning={loading}
            >
                <div
                    style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    {this.renderStackedBarChart()}
                </div>

                {hasTable && <Divider />}

                {hasTable && <MDBox py={1}>
                    {show_table
                        ? <DataTable
                            table={dataTableData}
                            entriesPerPage={false}
                            showTotalEntries={false}
                            isSorted={false}
                        />
                        : <MDButton onClick={() => this.setState({show_table : true})} variant="outlined" color="primary">
                            {t("see-table")}
                        </MDButton>
                    }
                </MDBox>}
            </Spin>
        );
    }
}
