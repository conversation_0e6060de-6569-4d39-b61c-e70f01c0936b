{"name": "dashr", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vikas-bansal/)", "private": true, "license": "UNLICENSED", "scripts": {"build": "vite build", "dev": "vite", "coverage": "vitest run --coverage", "start": "react-scripts start", "buildx": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start"}, "type": "module", "engines": {"node": ">= 12.16"}, "dependencies": {"@assistant-ui/react": "0.7.85", "@cubejs-client/core": "^0.35.23", "@cubejs-client/react": "^0.35.23", "@ebay/nice-modal-react": "^1.2.8", "@emotion/cache": "11.13.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mdi/font": "^7.1.96", "@mui/icons-material": "5.16.7", "@mui/material": "^5.16.7", "@mui/styled-engine": "^5.14.0", "@typeform/embed-react": "^3.8.0", "@vitejs/plugin-react": "^3.0.1", "antd": "^5.14.1", "axios": "^1.2.4", "boring-avatars": "1.7.0", "chart.js": "4.4.6", "chroma-js": "3.1.2", "classnames": "^2.3.2", "date-fns": "^2.29.3", "dayjs": "^1.11.7", "dinero.js": "^1.9.1", "dropzone": "6.0.0-beta.2", "firebase": "^9.17.1", "flowtoken": "^1.0.20", "formik": "2.4.6", "html-react-parser": "5.1.18", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^7.0.1", "i18next-xhr-backend": "^3.2.2", "mixpanel-browser": "^2.45.0", "numeral": "^2.0.6", "plotly.js": "^3.0.1", "prop-types": "15.8.1", "rc-tween-one": "^3.0.6", "react": "18.2.0", "react-calendly": "^4.3.0", "react-chartjs-2": "5.2.0", "react-confetti": "^6.2.2", "react-csv": "^2.2.2", "react-date-range": "^1.4.0", "react-dom": "18.2.0", "react-google-recaptcha": "^3.1.0", "react-highlight-words": "^0.20.0", "react-i18next": "^12.2.0", "react-plotly.js": "^2.6.0", "react-router-dom": "6.27.0", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "react-select": "^5.7.0", "react-spinners": "^0.13.8", "react-table": "7.8.0", "react-toastify": "^9.1.1", "react-use": "^17.6.0", "recharts": "^2.12.0", "stylis": "4.3.4", "uuid": "11.0.2", "vite": "^4.0.4", "web-vitals": "2.1.4", "yup": "1.4.0"}, "devDependencies": {"eslint": "8.8.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.3.0", "history": "^5.3.0", "jsdom": "^19.0.0", "prettier": "^2.6.0", "sass": "^1.69.5", "vi-fetch": "^0.6.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}}