import dotenv from 'dotenv';
import { shopifyApp } from "@shopify/shopify-app-express";
import { RedisSessionStorage } from "@shopify/shopify-app-session-storage-redis";
import { restResources } from "@shopify/shopify-api/rest/admin/2024-10"; // SHOPIFY_API_VERSION
import { shopifyBillingConfig } from './services/subscription/plans';
import shop from './services/shop.js';

dotenv.config();

export const sessionStorage  = new RedisSessionStorage(process.env.REDIS_URI)

// The transactions with Shopify will always be marked as test transactions, unless NODE_ENV is production.
// See the ensureBilling helper to learn more about billing in this template.

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET,
  scopes: (process.env.SCOPES ? process.env.SCOPES.split(",") : []),
  hostName: process.env.HOST,
  api: {
    apiVersion: shop.SHOPIFY_API_VERSION,
    restResources,
    billing: shopifyBillingConfig, // or replace with shopifyBillingConfig above to enable example billing
    isEmbeddedApp: false
  },
  auth: {
    path: "/api/auth",
    callbackPath: "/api/auth/callback",
  },
  webhooks: {
    path: "/api/webhooks",
  },
  // This should be replaced with your preferred storage strategy
  sessionStorage: sessionStorage,
});

export default shopify;
